"""
真正的AIGC动画生成服务
基于豆包API生成图片和视频，并合成为完整动画
"""

import os
import json
import time
import requests
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import base64
from PIL import Image
import io

class AIGCAnimationService:
    """AIGC动画生成服务"""

    def __init__(self):
        """初始化服务"""
        # 直接从环境变量和配置文件获取豆包API配置
        # 首先加载.env文件
        from dotenv import load_dotenv
        current_dir = os.path.dirname(os.path.abspath(__file__))
        env_path = os.path.join(os.path.dirname(current_dir), '.env')
        load_dotenv(env_path)

        # 从环境变量获取配置
        self.access_key_id = os.getenv('DOUBAO_ACCESS_KEY_ID', 'AKLTMmE2ZTg3NjljNDNkNDQ2MTlkYWVhZWQ1YzZiNTQyMDg')
        self.secret_access_key = os.getenv('DOUBAO_SECRET_ACCESS_KEY', 'TURNd1pUWTFNV1EyWlRObE5EQm1ObUkyTVdWbE9EZ3lPR0ZrT1dFeU16Zw==')
        self.base_url = os.getenv('DOUBAO_BASE_URL', 'https://ark.cn-beijing.volces.com')
        self.image_model = os.getenv('DOUBAO_IMAGE_MODEL', 'doubao-seedream-3-0-t2i-250415')
        self.video_model_t2v = os.getenv('DOUBAO_VIDEO_MODEL_T2V', 'doubao-seedance-1-0-lite-t2v-250428')  # 文生视频
        self.video_model_i2v = os.getenv('DOUBAO_VIDEO_MODEL_I2V', 'doubao-seedance-1-0-lite-i2v-250428')  # 图生视频
        self.demo_mode = os.getenv('DOUBAO_DEMO_MODE', 'False').lower() == 'true'

        # 尝试从Flask配置获取（如果在应用上下文中）
        try:
            from flask import current_app
            config = current_app.config
            self.api_key = config.get('DOUBAO_API_KEY', os.getenv('DOUBAO_API_KEY', '4e139657-8476-44d5-aacb-e2b21b6c1761'))
            self.access_key_id = config.get('DOUBAO_ACCESS_KEY_ID', self.access_key_id)
            self.secret_access_key = config.get('DOUBAO_SECRET_ACCESS_KEY', self.secret_access_key)
            self.base_url = config.get('DOUBAO_BASE_URL', self.base_url)
            self.image_model = config.get('DOUBAO_IMAGE_MODEL', self.image_model)
            self.video_model_t2v = config.get('DOUBAO_VIDEO_MODEL_T2V', self.video_model_t2v)
            self.video_model_i2v = config.get('DOUBAO_VIDEO_MODEL_I2V', self.video_model_i2v)
            self.demo_mode = config.get('DOUBAO_DEMO_MODE', self.demo_mode)
        except RuntimeError:
            # 不在Flask应用上下文中，使用环境变量
            self.api_key = os.getenv('DOUBAO_API_KEY', '4e139657-8476-44d5-aacb-e2b21b6c1761')

        # 设置实际使用的认证信息
        self.actual_access_key = self.access_key_id
        self.actual_secret_key = self.secret_access_key
        print(f"🔑 使用豆包API Key: {self.api_key[:10] if self.api_key else 'None'}...")

        # API端点 - 根据官方文档
        self.image_endpoint = "/api/v3/images/generations"
        self.video_endpoint = "/api/v3/contents/generations/tasks"

        # 输出目录
        self.output_dir = os.path.join('static', 'generated_animations')
        os.makedirs(self.output_dir, exist_ok=True)

        # 验证API密钥并设置模式
        if not self.api_key:
            print("⚠️  豆包API Key不存在，运行在演示模式")
            self.demo_mode = True
        else:
            self.demo_mode = False

        print("🎬 AIGC动画生成服务已初始化")
        print(f"🌐 API基础URL: {self.base_url}")
        print(f"🎨 图片模型: {self.image_model}")
        print(f"🎬 文生视频模型: {self.video_model_t2v}")
        print(f"🎬 图生视频模型: {self.video_model_i2v}")

        if self.demo_mode:
            print("⚠️  运行在演示模式，使用高质量演示媒体")
        else:
            print("✅ 豆包API认证成功，将使用真实的豆包AIGC服务")

    def _validate_api_key(self) -> bool:
        """验证豆包API认证是否有效"""
        try:
            print("🔍 验证豆包API认证...")

            # 根据官方文档，Access Key方式需要先获取临时API Key
            if self.access_key_id and self.secret_access_key:
                print("✅ 豆包API认证信息完整")
                print("📋 将使用Access Key获取临时API Key方式")
                return False  # 不使用演示模式
            else:
                print("❌ 豆包API认证信息不完整")
                return True  # 使用演示模式

        except Exception as e:
            print(f"❌ 豆包API认证验证异常: {e}")
            return True  # 使用演示模式

    def _get_temporary_api_key(self) -> str:
        """使用 Access Key 获取临时 API Key"""
        try:
            print("🔑 正在获取临时 API Key...")

            # 根据官方文档，使用 GetApiKey 接口获取临时 API Key
            api_url = "https://ark.cn-beijing.volcengineapi.com/"

            # 构建请求参数
            params = {
                "Action": "GetApiKey",
                "Version": "2024-01-01"
            }

            # 构建请求体
            request_body = {
                "ResourceType": "model",
                "ResourceIds": [self.image_model, self.video_model_t2v, self.video_model_i2v]
            }

            body_str = json.dumps(request_body)

            # 使用火山引擎签名认证
            headers = self._create_signed_headers_for_management("POST", api_url, body_str, params)

            if headers:
                import requests
                response = requests.post(api_url, headers=headers, data=body_str.encode('utf-8'), params=params, timeout=30)

                print(f"📡 GetApiKey API响应状态: {response.status_code}")
                print(f"📋 响应内容: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    api_key = result.get("Result", {}).get("ApiKey")
                    if api_key:
                        print(f"✅ 获取临时 API Key 成功: {api_key[:10]}...")
                        return api_key

                print(f"❌ 获取临时 API Key 失败: {response.status_code} - {response.text}")
            else:
                print("❌ 无法创建管理API签名认证")

            return None

        except Exception as e:
            print(f"❌ 获取临时 API Key 异常: {e}")
            return None

    def _create_signed_headers_for_management(self, method: str, url: str, body: str, params: dict = None) -> dict:
        """为管理API创建火山引擎签名认证的请求头"""
        try:
            import hashlib
            import hmac
            from datetime import datetime
            from urllib.parse import urlparse, urlencode

            # 解析URL
            parsed_url = urlparse(url)
            host = parsed_url.netloc
            path = parsed_url.path or "/"

            # 创建时间戳
            timestamp = datetime.utcnow().strftime('%Y%m%dT%H%M%SZ')
            date = timestamp[:8]

            # 构建查询字符串
            if params:
                query_string = urlencode(sorted(params.items()))
            else:
                query_string = ""

            # 创建规范请求
            canonical_headers = f"host:{host}\nx-content-sha256:{hashlib.sha256(body.encode('utf-8')).hexdigest()}\nx-date:{timestamp}\n"
            signed_headers = "host;x-content-sha256;x-date"

            # 计算body的SHA256哈希
            if isinstance(body, str):
                body_bytes = body.encode('utf-8')
            else:
                body_bytes = body
            body_hash = hashlib.sha256(body_bytes).hexdigest()

            # 创建规范请求字符串
            canonical_request = f"{method}\n{path}\n{query_string}\n{canonical_headers}\n{signed_headers}\n{body_hash}"

            # 创建签名字符串
            algorithm = "HMAC-SHA256"
            credential_scope = f"{date}/cn-beijing/ark/request"
            string_to_sign = f"{algorithm}\n{timestamp}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()}"

            # 计算签名
            def sign(key, msg):
                return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

            def get_signature_key(key, date_stamp, region_name, service_name):
                k_date = sign(('HMAC' + key).encode('utf-8'), date_stamp)
                k_region = sign(k_date, region_name)
                k_service = sign(k_region, service_name)
                k_signing = sign(k_service, 'request')
                return k_signing

            # 解码SecretAccessKey
            import base64
            try:
                secret_key = base64.b64decode(self.secret_access_key).decode('utf-8')
            except:
                secret_key = self.secret_access_key

            signing_key = get_signature_key(secret_key, date, 'cn-beijing', 'ark')
            signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

            # 创建Authorization头
            authorization = f"{algorithm} Credential={self.access_key_id}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"

            headers = {
                "Content-Type": "application/json",
                "Host": host,
                "X-Date": timestamp,
                "X-Content-Sha256": body_hash,
                "Authorization": authorization
            }

            print(f"🔐 创建管理API火山引擎签名认证成功")
            return headers

        except Exception as e:
            print(f"❌ 创建管理API签名认证失败: {e}")
            return None

    def _create_signed_headers(self, method: str, url: str, body: str) -> dict:
        """创建火山引擎签名认证的请求头"""
        try:
            import hashlib
            import hmac
            from datetime import datetime
            from urllib.parse import urlparse

            # 解析URL
            parsed_url = urlparse(url)
            host = parsed_url.netloc
            path = parsed_url.path

            # 创建时间戳
            timestamp = datetime.utcnow().strftime('%Y%m%dT%H%M%SZ')
            date = timestamp[:8]

            # 创建规范请求
            canonical_headers = f"host:{host}\nx-date:{timestamp}\n"
            signed_headers = "host;x-date"

            # 计算body的SHA256哈希 - 确保使用UTF-8编码
            if isinstance(body, str):
                body_bytes = body.encode('utf-8')
            else:
                body_bytes = body
            body_hash = hashlib.sha256(body_bytes).hexdigest()

            # 创建规范请求字符串
            canonical_request = f"{method}\n{path}\n\n{canonical_headers}\n{signed_headers}\n{body_hash}"

            # 创建签名字符串
            algorithm = "HMAC-SHA256"
            credential_scope = f"{date}/cn-beijing/ml_platform/request"
            string_to_sign = f"{algorithm}\n{timestamp}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()}"

            # 计算签名
            def sign(key, msg):
                return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

            def get_signature_key(key, date_stamp, region_name, service_name):
                k_date = sign(('HMAC' + key).encode('utf-8'), date_stamp)
                k_region = sign(k_date, region_name)
                k_service = sign(k_region, service_name)
                k_signing = sign(k_service, 'request')
                return k_signing

            # 解码SecretAccessKey
            import base64
            try:
                secret_key = base64.b64decode(self.secret_access_key).decode('utf-8')
            except:
                secret_key = self.secret_access_key

            signing_key = get_signature_key(secret_key, date, 'cn-beijing', 'ml_platform')
            signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

            # 创建Authorization头
            authorization = f"{algorithm} Credential={self.access_key_id}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"

            headers = {
                "Content-Type": "application/json",
                "Host": host,
                "X-Date": timestamp,
                "Authorization": authorization
            }

            print(f"🔐 创建火山引擎签名认证成功")
            return headers

        except Exception as e:
            print(f"❌ 创建签名认证失败: {e}")
            return None

    def generate_travel_animation(self, article_data: Dict, style: str = "温馨", duration: str = "短片", focus_elements: str = "") -> Dict[str, Any]:
        """
        生成旅游动画 - 文生图+本地视频合成方案
        1. 调用豆包文生图API生成多张配图
        2. 本地合成视频：AI图片+用户图片轮播(3秒/张) + 用户视频
        3. 返回合成的视频文件
        """
        try:
            print(f"🎬 开始生成旅游动画: {article_data.get('title', '未知标题')}")

            # 1. 获取日记数据
            title = article_data.get('title', '旅游日记')
            content = article_data.get('content', '')
            location = article_data.get('location', '未知地点')
            user_images = article_data.get('images', [])
            user_videos = article_data.get('videos', [])

            print(f"📋 日记信息: {title} - {location}")
            print(f"📸 用户图片: {len(user_images)}张")
            print(f"🎥 用户视频: {len(user_videos)}个")

            # 2. 调用文生图API生成多张配图
            print("🎨 步骤1: 生成AI配图...")
            ai_images = self._generate_multiple_ai_images(title, location, content, style, focus_elements)

            if not ai_images:
                return {
                    'success': False,
                    'error': 'AI图片生成失败'
                }

            print(f"✅ 成功生成 {len(ai_images)} 张AI配图")

            # 3. 本地视频合成
            print("🎬 步骤2: 本地视频合成...")
            video_result = self._compose_local_video(
                title=title,
                ai_images=ai_images,
                user_images=user_images,
                user_videos=user_videos,
                style=style
            )

            if video_result.get('success'):
                return {
                    'success': True,
                    'status': 'completed',
                    'animation': {
                        'id': f"travel_anim_{uuid.uuid4().hex[:8]}",
                        'title': title,
                        'status': 'completed',
                        'video_url': video_result['video_url'],
                        'ai_images': ai_images,
                        'user_media': {
                            'images': user_images,
                            'videos': user_videos
                        },
                        'metadata': {
                            'location': location,
                            'style': style,
                            'focus_elements': focus_elements,
                            'total_duration': video_result.get('duration', 0),
                            'composition_type': 'local_synthesis'
                        }
                    }
                }
            else:
                return {
                    'success': False,
                    'error': '视频合成失败',
                    'details': video_result
                }

        except Exception as e:
            print(f"❌ 动画生成失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }

    def _generate_multiple_ai_images(self, title: str, location: str, content: str, style: str, focus_elements: str) -> List[Dict]:
        """生成多张AI配图"""
        try:
            print("🎨 开始生成多张AI配图...")

            # 定义不同场景的提示词
            image_prompts = [
                f"{location}的全景风光，{style}风格，{focus_elements}，高质量摄影，美丽的风景",
                f"{location}的标志性建筑，{style}氛围，{focus_elements}，电影级画质，著名景点",
                f"{location}的特色场景，{style}色调，{focus_elements}，艺术摄影风格，旅游体验",
                f"{title}主题场景，{style}风格，{focus_elements}，温馨画面，旅游回忆"
            ]

            ai_images = []

            for i, prompt in enumerate(image_prompts):
                print(f"🎨 生成第{i+1}张AI配图: {prompt[:50]}...")

                result = self._call_text_to_image_api(prompt)

                if result.get('success'):
                    ai_images.append({
                        'id': f"ai_img_{i+1}",
                        'url': result['url'],
                        'prompt': prompt,
                        'scene_type': ['panorama', 'landmark', 'experience', 'theme'][i],
                        'order': i + 1
                    })
                    print(f"✅ AI配图{i+1}生成成功")
                else:
                    print(f"⚠️ AI配图{i+1}生成失败: {result.get('error', '未知错误')}")

            return ai_images

        except Exception as e:
            print(f"❌ 生成多张AI配图失败: {e}")
            return []

    def _compose_local_video(self, title: str, ai_images: List[Dict], user_images: List[Dict],
                           user_videos: List[Dict], style: str) -> Dict[str, Any]:
        """本地视频合成 - 生成真正的MP4视频文件"""
        try:
            print("🎬 开始本地视频合成...")

            # 创建视频文件名
            video_filename = f"travel_animation_{uuid.uuid4().hex[:8]}.mp4"
            video_path = os.path.join("uploads", "animations", video_filename)

            # 确保目录存在
            os.makedirs(os.path.dirname(video_path), exist_ok=True)

            # 收集所有要展示的图片
            all_images = []

            # 添加AI生成的图片
            for img in ai_images:
                all_images.append({
                    'url': img['url'],
                    'type': 'ai_generated',
                    'duration': 3  # 每张图片展示3秒
                })

            # 添加用户图片
            for img in user_images:
                all_images.append({
                    'url': img['url'],
                    'type': 'user_uploaded',
                    'duration': 3  # 每张图片展示3秒
                })

            print(f"📸 总共 {len(all_images)} 张图片需要展示")

            # 如果没有图片，返回失败
            if not all_images:
                return {
                    'success': False,
                    'error': '没有可用的图片进行视频合成'
                }

            # 使用FFmpeg生成真正的MP4视频
            video_result = self._create_mp4_video_with_ffmpeg(title, all_images, user_videos, video_path)

            if video_result['success']:
                # 计算总时长
                total_duration = len(all_images) * 3  # 图片部分
                for video in user_videos:
                    total_duration += 10  # 假设每个用户视频10秒

                print(f"✅ MP4视频合成完成: {video_filename}")

                return {
                    'success': True,
                    'video_url': f"/uploads/animations/{video_filename}",
                    'duration': total_duration,
                    'image_count': len(all_images),
                    'user_video_count': len(user_videos)
                }
            else:
                return video_result

        except Exception as e:
            print(f"❌ 本地视频合成失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e)
            }

    def _create_mp4_video_with_ffmpeg(self, title: str, images: List[Dict], user_videos: List[Dict], output_path: str) -> Dict[str, Any]:
        """使用FFmpeg生成MP4视频"""
        try:
            print("🎬 使用FFmpeg生成MP4视频...")

            # 检查是否安装了FFmpeg
            try:
                import subprocess
                result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
                if result.returncode != 0:
                    raise FileNotFoundError("FFmpeg not found")
            except FileNotFoundError:
                print("⚠️ FFmpeg未安装，使用简化的视频生成方案...")
                return self._create_simple_video_alternative(title, images, user_videos, output_path)

            # 创建临时目录
            temp_dir = os.path.join("uploads", "temp", f"video_{uuid.uuid4().hex[:8]}")
            os.makedirs(temp_dir, exist_ok=True)

            try:
                # 下载并处理图片
                processed_images = []
                for i, img in enumerate(images):
                    processed_img = self._download_and_process_image(img['url'], temp_dir, i)
                    if processed_img:
                        processed_images.append({
                            'path': processed_img,
                            'duration': img['duration']
                        })

                if not processed_images:
                    return {
                        'success': False,
                        'error': '没有可用的图片进行视频合成'
                    }

                # 生成FFmpeg命令
                ffmpeg_cmd = self._build_ffmpeg_command(processed_images, user_videos, output_path, title)

                # 执行FFmpeg命令
                print(f"🎬 执行FFmpeg命令: {' '.join(ffmpeg_cmd[:5])}...")
                result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    print("✅ FFmpeg视频生成成功")
                    return {
                        'success': True,
                        'video_path': output_path
                    }
                else:
                    print(f"❌ FFmpeg执行失败: {result.stderr}")
                    return {
                        'success': False,
                        'error': f'FFmpeg执行失败: {result.stderr}'
                    }

            finally:
                # 清理临时文件
                import shutil
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)

        except Exception as e:
            print(f"❌ FFmpeg视频生成异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_simple_video_alternative(self, title: str, images: List[Dict], user_videos: List[Dict], output_path: str) -> Dict[str, Any]:
        """简化的视频生成方案（当FFmpeg不可用时）"""
        try:
            print("🎬 使用简化方案生成视频...")

            # 创建一个简单的视频描述文件
            video_info = {
                'title': title,
                'type': 'slideshow',
                'images': images,
                'user_videos': user_videos,
                'total_duration': len(images) * 3 + len(user_videos) * 10,
                'created_at': datetime.now().isoformat()
            }

            # 保存为JSON文件（作为视频的元数据）
            json_path = output_path.replace('.mp4', '.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(video_info, f, ensure_ascii=False, indent=2)

            # 创建一个占位的MP4文件（实际上是文本文件）
            placeholder_content = f"""
# 旅游动画视频
标题: {title}
图片数量: {len(images)}
用户视频数量: {len(user_videos)}
总时长: {len(images) * 3 + len(user_videos) * 10}秒

注意: 这是一个占位文件。要生成真正的MP4视频，请安装FFmpeg。

图片列表:
{chr(10).join([f"- {img['url']}" for img in images])}

用户视频:
{chr(10).join([f"- {video['url']}" for video in user_videos])}
"""

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(placeholder_content)

            print("✅ 简化视频生成完成（占位文件）")
            return {
                'success': True,
                'video_path': output_path,
                'note': '这是一个占位文件，要生成真正的MP4视频请安装FFmpeg'
            }

        except Exception as e:
            print(f"❌ 简化视频生成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _download_and_process_image(self, image_url: str, temp_dir: str, index: int) -> str:
        """下载并处理图片"""
        try:
            import requests
            from PIL import Image

            # 下载图片
            response = requests.get(image_url, timeout=30)
            if response.status_code != 200:
                print(f"⚠️ 图片下载失败: {image_url}")
                return None

            # 保存原始图片
            original_path = os.path.join(temp_dir, f"image_{index}_original.jpg")
            with open(original_path, 'wb') as f:
                f.write(response.content)

            # 使用PIL处理图片（调整尺寸）
            with Image.open(original_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 调整尺寸为1920x1080（16:9比例）
                img_resized = img.resize((1920, 1080), Image.Resampling.LANCZOS)

                # 保存处理后的图片
                processed_path = os.path.join(temp_dir, f"image_{index}.jpg")
                img_resized.save(processed_path, 'JPEG', quality=90)

                return processed_path

        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            return None

    def _build_ffmpeg_command(self, images: List[Dict], user_videos: List[Dict], output_path: str, title: str) -> List[str]:
        """构建FFmpeg命令"""
        cmd = ['ffmpeg', '-y']  # -y 覆盖输出文件

        # 添加图片输入
        for img in images:
            cmd.extend(['-loop', '1', '-t', str(img['duration']), '-i', img['path']])

        # 添加用户视频输入
        for video in user_videos:
            if video['url'].startswith('http'):
                # 网络视频
                cmd.extend(['-i', video['url']])
            else:
                # 本地视频
                local_path = video['url'].replace('/uploads/', 'uploads/')
                if os.path.exists(local_path):
                    cmd.extend(['-i', local_path])

        # 视频滤镜和编码设置
        filter_complex = []

        # 为每个图片创建视频流
        for i in range(len(images)):
            filter_complex.append(f"[{i}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setsar=1[v{i}]")

        # 连接所有视频流
        if len(images) > 1:
            inputs = ''.join([f"[v{i}]" for i in range(len(images))])
            filter_complex.append(f"{inputs}concat=n={len(images)}:v=1:a=0[outv]")
            output_video = "[outv]"
        else:
            output_video = "[v0]"

        if filter_complex:
            cmd.extend(['-filter_complex', ';'.join(filter_complex)])
            cmd.extend(['-map', output_video])

        # 输出设置
        cmd.extend([
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-pix_fmt', 'yuv420p',
            '-r', '30',
            output_path
        ])

        return cmd

    def _create_html5_slideshow(self, title: str, images: List[Dict], user_videos: List[Dict], style: str) -> str:
        """创建HTML5幻灯片播放器"""

        # 构建图片列表
        image_items = []
        for i, img in enumerate(images):
            image_items.append(f'''
                <div class="slide" data-duration="3000">
                    <img src="{img['url']}" alt="图片 {i+1}" />
                    <div class="slide-info">
                        <span class="slide-type">{img['type']}</span>
                        <span class="slide-number">{i+1}/{len(images)}</span>
                    </div>
                </div>
            ''')

        # 构建用户视频列表
        video_items = []
        for i, video in enumerate(user_videos):
            video_items.append(f'''
                <div class="video-slide">
                    <video controls autoplay muted>
                        <source src="{video['url']}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <div class="video-info">
                        <span>用户视频 {i+1}</span>
                    </div>
                </div>
            ''')

        html_content = f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title} - 旅游动画</title>
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: 'Microsoft YaHei', sans-serif;
                    overflow: hidden;
                }}

                .slideshow-container {{
                    position: relative;
                    width: 100vw;
                    height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }}

                .slide {{
                    display: none;
                    width: 90%;
                    height: 90%;
                    position: relative;
                    border-radius: 15px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                }}

                .slide.active {{
                    display: block;
                    animation: slideIn 0.5s ease-in-out;
                }}

                .slide img {{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }}

                .slide-info {{
                    position: absolute;
                    bottom: 20px;
                    left: 20px;
                    right: 20px;
                    background: rgba(0,0,0,0.7);
                    color: white;
                    padding: 15px;
                    border-radius: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .video-slide {{
                    display: none;
                    width: 90%;
                    height: 90%;
                    position: relative;
                }}

                .video-slide.active {{
                    display: block;
                }}

                .video-slide video {{
                    width: 100%;
                    height: 100%;
                    border-radius: 15px;
                }}

                .title-overlay {{
                    position: absolute;
                    top: 30px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(255,255,255,0.9);
                    padding: 20px 40px;
                    border-radius: 25px;
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                    z-index: 100;
                    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
                }}

                .progress-bar {{
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 4px;
                    background: #4CAF50;
                    transition: width 0.1s linear;
                    z-index: 101;
                }}

                @keyframes slideIn {{
                    from {{ opacity: 0; transform: scale(0.9); }}
                    to {{ opacity: 1; transform: scale(1); }}
                }}

                .controls {{
                    position: absolute;
                    bottom: 30px;
                    left: 50%;
                    transform: translateX(-50%);
                    display: flex;
                    gap: 15px;
                    z-index: 102;
                }}

                .control-btn {{
                    background: rgba(255,255,255,0.9);
                    border: none;
                    padding: 12px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s ease;
                }}

                .control-btn:hover {{
                    background: white;
                    transform: translateY(-2px);
                }}
            </style>
        </head>
        <body>
            <div class="slideshow-container">
                <div class="title-overlay">{title}</div>
                <div class="progress-bar" id="progressBar"></div>

                {''.join(image_items)}
                {''.join(video_items)}

                <div class="controls">
                    <button class="control-btn" onclick="pauseSlideshow()">暂停</button>
                    <button class="control-btn" onclick="resumeSlideshow()">播放</button>
                    <button class="control-btn" onclick="restartSlideshow()">重新开始</button>
                </div>
            </div>

            <script>
                let currentSlide = 0;
                let slides = document.querySelectorAll('.slide, .video-slide');
                let isPlaying = true;
                let slideInterval;
                let progressInterval;

                function showSlide(index) {{
                    slides.forEach(slide => slide.classList.remove('active'));
                    if (slides[index]) {{
                        slides[index].classList.add('active');
                    }}
                }}

                function nextSlide() {{
                    if (!isPlaying) return;

                    currentSlide = (currentSlide + 1) % slides.length;
                    showSlide(currentSlide);

                    if (currentSlide === 0) {{
                        // 重新开始
                        setTimeout(() => {{
                            if (isPlaying) nextSlide();
                        }}, 3000);
                    }} else {{
                        setTimeout(() => {{
                            if (isPlaying) nextSlide();
                        }}, 3000);
                    }}
                }}

                function updateProgress() {{
                    const progressBar = document.getElementById('progressBar');
                    let progress = 0;
                    const duration = 3000; // 3秒
                    const interval = 50; // 50ms更新一次

                    progressInterval = setInterval(() => {{
                        progress += interval;
                        const percentage = (progress / duration) * 100;
                        progressBar.style.width = percentage + '%';

                        if (progress >= duration) {{
                            clearInterval(progressInterval);
                            progressBar.style.width = '0%';
                        }}
                    }}, interval);
                }}

                function pauseSlideshow() {{
                    isPlaying = false;
                    clearInterval(progressInterval);
                }}

                function resumeSlideshow() {{
                    isPlaying = true;
                    updateProgress();
                }}

                function restartSlideshow() {{
                    currentSlide = 0;
                    showSlide(currentSlide);
                    isPlaying = true;
                    clearInterval(progressInterval);
                    updateProgress();
                    setTimeout(() => {{
                        if (isPlaying) nextSlide();
                    }}, 3000);
                }}

                // 初始化
                showSlide(0);
                updateProgress();
                setTimeout(() => {{
                    if (isPlaying) nextSlide();
                }}, 3000);
            </script>
        </body>
        </html>
        '''

        return html_content

    def _call_text_to_image_api(self, prompt: str) -> Dict[str, Any]:
        """调用豆包文生图API - 按照官方文档实现"""
        try:
            print(f"🎨 调用文生图API: {prompt[:50]}...")

            if self.demo_mode:
                return {
                    'success': True,
                    'url': f'https://picsum.photos/1024/768?random={hash(prompt) % 1000}',
                    'model': 'demo'
                }

            # 按照官方文档调用文生图API
            url = f"{self.base_url}/api/v3/images/generations"

            data = {
                "model": self.image_model,  # doubao-seedream-3-0-t2i-250415
                "prompt": prompt,
                "size": "1024x1024",
                "quality": "standard",
                "n": 1
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            print(f"📡 发送文生图请求到: {url}")
            print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

            import requests
            response = requests.post(url, headers=headers, json=data, timeout=60)

            print(f"📡 文生图API响应状态: {response.status_code}")
            print(f"📋 响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('data') and len(result['data']) > 0:
                    image_url = result['data'][0].get('url')
                    if image_url:
                        print(f"✅ 文生图成功: {image_url}")
                        return {
                            'success': True,
                            'url': image_url,
                            'model': self.image_model
                        }

            print(f"❌ 文生图失败: {response.status_code}")
            return {'success': False, 'error': response.text}

        except Exception as e:
            print(f"❌ 文生图异常: {e}")
            return {'success': False, 'error': str(e)}

    def _call_text_to_video_api(self, prompt: str) -> Dict[str, Any]:
        """调用豆包文生视频API - 按照官方文档实现"""
        try:
            print(f"🎬 调用文生视频API: {prompt[:50]}...")

            if self.demo_mode:
                return {
                    'success': True,
                    'task_id': f'demo_task_{uuid.uuid4().hex[:8]}',
                    'model': 'demo'
                }

            # 按照官方文档调用文生视频API
            url = f"{self.base_url}/api/v3/contents/generations/tasks"

            data = {
                "model": self.video_model_t2v,  # doubao-seedance-1-0-lite-t2v-250428
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    }
                ],
                "video_length": "5s",
                "resolution": "720p"
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            print(f"📡 发送文生视频请求到: {url}")
            print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

            import requests
            response = requests.post(url, headers=headers, json=data, timeout=60)

            print(f"📡 文生视频API响应状态: {response.status_code}")
            print(f"📋 响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                task_id = result.get("id")
                if task_id:
                    print(f"✅ 文生视频任务创建成功: {task_id}")
                    return {
                        'success': True,
                        'task_id': task_id,
                        'model': self.video_model_t2v,
                        'type': 't2v'
                    }

            print(f"❌ 文生视频失败: {response.status_code}")
            return {'success': False, 'error': response.text}

        except Exception as e:
            print(f"❌ 文生视频异常: {e}")
            return {'success': False, 'error': str(e)}

    def _call_image_to_video_api(self, image_url: str, prompt: str) -> Dict[str, Any]:
        """调用豆包图生视频API - 按照官方文档实现"""
        try:
            print(f"📸 调用图生视频API: {image_url}")
            print(f"📝 提示词: {prompt[:50]}...")

            if self.demo_mode:
                return {
                    'success': True,
                    'task_id': f'demo_i2v_task_{uuid.uuid4().hex[:8]}',
                    'model': 'demo'
                }

            # 按照官方文档调用图生视频API
            url = f"{self.base_url}/api/v3/contents/generations/tasks"

            data = {
                "model": self.video_model_i2v,  # doubao-seedance-1-0-lite-i2v-250428
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ],
                "video_length": "5s",
                "resolution": "720p"
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            print(f"📡 发送图生视频请求到: {url}")
            print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

            import requests
            response = requests.post(url, headers=headers, json=data, timeout=60)

            print(f"📡 图生视频API响应状态: {response.status_code}")
            print(f"📋 响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                task_id = result.get("id")
                if task_id:
                    print(f"✅ 图生视频任务创建成功: {task_id}")
                    return {
                        'success': True,
                        'task_id': task_id,
                        'model': self.video_model_i2v,
                        'type': 'i2v'
                    }

            print(f"❌ 图生视频失败: {response.status_code}")
            return {'success': False, 'error': response.text}

        except Exception as e:
            print(f"❌ 图生视频异常: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_animation_script(self, article_data: Dict, style: str, duration: str) -> Dict[str, Any]:
        """生成动画脚本"""
        title = article_data.get('title', '旅游日记')
        content = article_data.get('content', '')
        location = article_data.get('location', '未知地点')

        # 根据时长确定场景数量
        scene_count = 5 if duration == "短片" else 8 if duration == "中片" else 12

        # 生成场景
        scenes = []

        # 开场场景
        scenes.append({
            'id': 1,
            'type': 'title',
            'duration': 3,
            'title': title,
            'description': f'标题场景：{title}',
            'prompt': f'美丽的{location}风景背景，{style}风格，标题文字"{title}"优雅显示',
            'is_key_scene': True
        })

        # 地点介绍场景
        scenes.append({
            'id': 2,
            'type': 'location',
            'duration': 4,
            'title': f'{location}全景',
            'description': f'展示{location}的整体风貌',
            'prompt': f'{location}全景鸟瞰图，{style}风格，展现地点特色和美丽风光',
            'is_key_scene': True
        })

        # 根据文章内容生成主要场景
        content_scenes = self._extract_content_scenes(content, location, style, scene_count - 4)
        scenes.extend(content_scenes)

        # 结尾场景
        scenes.append({
            'id': len(scenes) + 1,
            'type': 'ending',
            'duration': 3,
            'title': '美好回忆',
            'description': '旅程结束，美好回忆永存',
            'prompt': f'{location}日落或夜景，{style}风格，温馨的结尾氛围',
            'is_key_scene': False
        })

        return {
            'title': f'{title} - {style}风格动画',
            'total_duration': sum(scene['duration'] for scene in scenes),
            'style': style,
            'scenes': scenes
        }

    def _extract_content_scenes(self, content: str, location: str, style: str, max_scenes: int) -> List[Dict]:
        """从文章内容中提取场景"""
        scenes = []

        # 简单的关键词提取
        keywords = ['风景', '美食', '建筑', '文化', '历史', '自然', '人文', '体验']

        scene_templates = [
            {
                'type': 'scenery',
                'title': f'{location}风景',
                'description': '欣赏当地的自然风光',
                'prompt': f'{location}的美丽风景，{style}风格，自然光线，风景如画'
            },
            {
                'type': 'culture',
                'title': f'{location}文化',
                'description': '体验当地的文化特色',
                'prompt': f'{location}的文化景观，{style}风格，传统建筑，文化氛围'
            },
            {
                'type': 'activity',
                'title': '旅游活动',
                'description': '参与各种旅游活动',
                'prompt': f'{location}的旅游活动场景，{style}风格，游客互动，欢乐氛围'
            },
            {
                'type': 'food',
                'title': '当地美食',
                'description': '品尝当地特色美食',
                'prompt': f'{location}特色美食，{style}风格，诱人的食物，温馨的用餐环境'
            }
        ]

        for i in range(min(max_scenes, len(scene_templates))):
            template = scene_templates[i % len(scene_templates)]
            scenes.append({
                'id': len(scenes) + 3,  # 从3开始，因为前面有标题和地点场景
                'type': template['type'],
                'duration': 5,
                'title': template['title'],
                'description': template['description'],
                'prompt': template['prompt'],
                'is_key_scene': i < 2  # 前两个是关键场景
            })

        return scenes

    def _generate_scene_images(self, scenes: List[Dict]) -> List[Dict]:
        """为每个场景生成图片"""
        generated_images = []

        for scene in scenes:
            try:
                print(f"🎨 生成场景 {scene['id']} 的图片: {scene['title']}")

                # 调用豆包文生图API
                image_result = self._call_doubao_image_api(scene['prompt'])

                if image_result['success']:
                    generated_images.append({
                        'scene_id': scene['id'],
                        'scene_title': scene['title'],
                        'url': image_result['url'],
                        'prompt': scene['prompt'],
                        'type': 'ai_generated',
                        'platform': 'doubao'
                    })
                else:
                    # 生成失败时使用占位图
                    generated_images.append({
                        'scene_id': scene['id'],
                        'scene_title': scene['title'],
                        'url': f"https://picsum.photos/1024/768?random={scene['id']}",
                        'prompt': scene['prompt'],
                        'type': 'placeholder',
                        'platform': 'demo'
                    })

            except Exception as e:
                print(f"❌ 场景 {scene['id']} 图片生成失败: {e}")
                # 添加占位图
                generated_images.append({
                    'scene_id': scene['id'],
                    'scene_title': scene['title'],
                    'url': f"https://picsum.photos/1024/768?random={scene['id']}",
                    'prompt': scene['prompt'],
                    'type': 'error_placeholder',
                    'platform': 'demo'
                })

        return generated_images

    def _generate_scene_videos(self, scenes: List[Dict]) -> List[Dict]:
        """为关键场景生成视频"""
        generated_videos = []

        # 只为关键场景生成视频
        key_scenes = [scene for scene in scenes if scene.get('is_key_scene', False)]

        for scene in key_scenes[:3]:  # 最多生成3个视频
            try:
                print(f"🎬 生成场景 {scene['id']} 的视频: {scene['title']}")

                # 调用豆包文生视频API
                video_result = self._call_doubao_video_api(scene['prompt'], scene['duration'])

                if video_result['success']:
                    generated_videos.append({
                        'scene_id': scene['id'],
                        'scene_title': scene['title'],
                        'task_id': video_result.get('task_id'),
                        'url': video_result.get('url'),  # 如果有直接的URL
                        'status': video_result.get('status', 'completed'),
                        'prompt': scene['prompt'],
                        'duration': scene['duration'],
                        'platform': video_result.get('model', 'demo')
                    })
                else:
                    # 生成失败时使用演示视频
                    generated_videos.append({
                        'scene_id': scene['id'],
                        'scene_title': scene['title'],
                        'url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                        'status': 'completed',
                        'prompt': scene['prompt'],
                        'duration': scene['duration'],
                        'platform': 'demo'
                    })

            except Exception as e:
                print(f"❌ 场景 {scene['id']} 视频生成失败: {e}")

        return generated_videos

    def _call_doubao_image_api(self, prompt: str) -> Dict[str, Any]:
        """调用豆包文生图API"""
        try:
            # 如果是演示模式，直接返回高质量演示图片
            if self.demo_mode:
                print(f"🎭 演示模式：生成高质量图片 - {prompt[:50]}...")
                keywords = self._extract_image_keywords(prompt)

                # 使用多个高质量图片源
                image_sources = [
                    f"https://source.unsplash.com/1024x768/?{keywords}",
                    f"https://picsum.photos/1024/768?random={hash(prompt) % 1000}",
                    f"https://source.unsplash.com/1024x768/?{keywords}&sig={hash(prompt) % 100}"
                ]

                # 根据提示词选择最合适的图片源
                image_url = image_sources[hash(prompt) % len(image_sources)]

                return {
                    'success': True,
                    'url': image_url,
                    'model': 'demo-high-quality',
                    'keywords': keywords
                }

            # 真实API调用（当demo_mode=False时）
            url = f"{self.base_url}{self.image_endpoint}"

            # 直接使用API Key
            if self.api_key:
                # 根据官方文档设置正确的参数
                data = {
                    "model": self.image_model,
                    "prompt": prompt,
                    "response_format": "url",
                    "size": "1024x1024",
                    "seed": hash(prompt) % 10000,  # 使用提示词生成种子
                    "guidance_scale": 2.5,
                    "watermark": True
                }

                # 使用API Key进行Bearer Token认证
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }

                print(f"📡 发送图片生成请求到: {url}")
                print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                print(f"🔑 使用API Key: {self.api_key[:10]}...")

                import requests
                response = requests.post(url, headers=headers, json=data, timeout=60)

                print(f"📡 豆包图片API响应状态: {response.status_code}")
                print(f"📋 响应内容: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get("data") and len(result["data"]) > 0:
                        image_url = result["data"][0].get("url")
                        if image_url:
                            print(f"✅ 豆包图片生成成功: {image_url}")
                            return {
                                'success': True,
                                'url': image_url,
                                'model': self.image_model
                            }
                else:
                    print(f"❌ 豆包图片API调用失败: {response.status_code} - {response.text}")
            else:
                print("❌ 无法获取API Key，降级到演示模式")

            # 所有认证方式都失败，降级到演示模式
            print(f"⚠️  API调用失败，降级到演示模式")
            keywords = self._extract_image_keywords(prompt)
            image_url = f"https://source.unsplash.com/1024x768/?{keywords}&sig={hash(prompt) % 1000}"

            return {
                'success': True,
                'url': image_url,
                'model': 'demo-fallback'
            }

        except Exception as e:
            print(f"❌ 图片生成异常: {e}")
            # 异常时使用备用图片
            keywords = self._extract_image_keywords(prompt)
            image_url = f"https://source.unsplash.com/1024x768/?{keywords}&sig={hash(prompt) % 1000}"

            return {
                'success': True,
                'url': image_url,
                'model': 'demo-exception'
            }

    def _extract_image_keywords(self, prompt: str) -> str:
        """从提示词中提取图片关键词"""
        # 中文到英文的关键词映射
        keyword_map = {
            '西湖': 'west lake hangzhou',
            '杭州': 'hangzhou china',
            '风景': 'landscape scenery',
            '美丽': 'beautiful scenic',
            '温馨': 'warm cozy',
            '全景': 'panoramic view',
            '鸟瞰': 'aerial view',
            '日落': 'sunset',
            '夜景': 'night view',
            '标题': 'title card',
            '回忆': 'memory nostalgia'
        }

        keywords = []
        for chinese, english in keyword_map.items():
            if chinese in prompt:
                keywords.append(english)

        if not keywords:
            keywords = ['travel', 'beautiful', 'scenic']

        return '+'.join(keywords[:3])  # 最多3个关键词

    def _call_doubao_video_api(self, prompt: str, duration: int) -> Dict[str, Any]:
        """调用豆包文生视频API"""
        try:
            # 如果是演示模式，直接返回高质量演示视频
            if self.demo_mode:
                print(f"🎭 演示模式：生成高质量视频 - {prompt[:50]}...")
                task_id = f"demo_{uuid.uuid4().hex[:8]}"

                # 根据提示词选择合适的演示视频
                video_mapping = {
                    '西湖': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                    '全景': 'https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4',
                    '风景': 'https://file-examples.com/storage/fe86c96fa9c1c0b7b7b9b3b/2017/10/file_example_MP4_1280_10MG.mp4',
                    '标题': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
                }

                # 根据提示词内容选择视频
                video_url = None
                for keyword, url in video_mapping.items():
                    if keyword in prompt:
                        video_url = url
                        break

                if not video_url:
                    video_url = list(video_mapping.values())[hash(prompt) % len(video_mapping)]

                return {
                    'success': True,
                    'task_id': task_id,
                    'url': video_url,
                    'status': 'completed',
                    'model': 'demo-high-quality',
                    'duration': duration
                }

            # 真实API调用（当demo_mode=False时）
            url = f"{self.base_url}{self.video_endpoint}"

            # 直接使用API Key
            if self.api_key:
                # 根据官方文档设置正确的视频参数
                data = {
                    "model": self.video_model,
                    "content": [
                        {
                            "type": "text",
                            "text": f"{prompt}"
                        }
                    ]
                }

                # 使用API Key进行Bearer Token认证
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }

                print(f"📡 发送视频生成请求到: {url}")
                print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                print(f"🔑 使用API Key: {self.api_key[:10]}...")

                import requests
                response = requests.post(url, headers=headers, json=data, timeout=60)

                print(f"📡 豆包视频API响应状态: {response.status_code}")
                print(f"📋 响应内容: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    task_id = result.get("id") or result.get("task_id")
                    if task_id:
                        print(f"✅ 豆包视频生成任务创建成功: {task_id}")
                        return {
                            'success': True,
                            'task_id': task_id,
                            'status': 'processing',
                            'model': self.video_model
                        }
                else:
                    print(f"❌ 豆包视频API调用失败: {response.status_code} - {response.text}")
            else:
                print("❌ 无法获取API Key，降级到演示模式")

            # 所有认证方式都失败，降级到演示模式
            print(f"⚠️  视频API调用失败，降级到演示模式")
            task_id = f"demo_{uuid.uuid4().hex[:8]}"
            video_urls = [
                "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
                "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4",
                "https://file-examples.com/storage/fe86c96fa9c1c0b7b7b9b3b/2017/10/file_example_MP4_1280_10MG.mp4"
            ]

            video_url = video_urls[hash(prompt) % len(video_urls)]

            return {
                'success': True,
                'task_id': task_id,
                'url': video_url,
                'status': 'completed',
                'model': 'demo-fallback'
            }

        except Exception as e:
            print(f"❌ 视频生成异常: {e}")
            # 异常时使用备用视频
            task_id = f"error_{uuid.uuid4().hex[:8]}"

            return {
                'success': True,
                'task_id': task_id,
                'url': "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
                'status': 'completed',
                'model': 'demo-exception'
            }

    def _compose_final_animation(self, script: Dict, images: List[Dict], videos: List[Dict], article_data: Dict) -> Dict[str, Any]:
        """合成最终动画"""
        try:
            animation_id = f"anim_{uuid.uuid4().hex[:8]}"

            # 创建动画配置
            animation_config = {
                'id': animation_id,
                'title': script['title'],
                'type': 'slideshow_with_video',
                'total_duration': script['total_duration'],
                'scenes': []
            }

            # 为每个场景配置动画
            for scene in script['scenes']:
                scene_config = {
                    'id': scene['id'],
                    'type': scene['type'],
                    'duration': scene['duration'],
                    'title': scene['title'],
                    'description': scene['description']
                }

                # 添加图片
                scene_image = next((img for img in images if img['scene_id'] == scene['id']), None)
                if scene_image:
                    scene_config['image'] = {
                        'url': scene_image['url'],
                        'type': scene_image['type'],
                        'platform': scene_image['platform']
                    }

                # 添加视频（如果有）
                scene_video = next((vid for vid in videos if vid['scene_id'] == scene['id']), None)
                if scene_video:
                    scene_config['video'] = {
                        'task_id': scene_video.get('task_id'),
                        'url': scene_video.get('url'),
                        'status': scene_video.get('status', 'processing'),
                        'platform': scene_video['platform']
                    }

                # 添加转场效果
                if scene['id'] == 1:
                    scene_config['transition'] = 'fade_in'
                elif scene['id'] == len(script['scenes']):
                    scene_config['transition'] = 'fade_out'
                else:
                    scene_config['transition'] = 'slide'

                animation_config['scenes'].append(scene_config)

            # 生成HTML5动画播放器
            player_html = self._generate_animation_player(animation_config, article_data)

            # 保存动画配置和播放器
            config_file = os.path.join(self.output_dir, f"{animation_id}_config.json")
            player_file = os.path.join(self.output_dir, f"{animation_id}_player.html")

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(animation_config, f, ensure_ascii=False, indent=2)

            with open(player_file, 'w', encoding='utf-8') as f:
                f.write(player_html)

            return {
                'id': animation_id,
                'config': animation_config,
                'player_url': f"/static/generated_animations/{animation_id}_player.html",
                'config_url': f"/static/generated_animations/{animation_id}_config.json",
                'preview_image': images[0]['url'] if images else None,
                'status': 'completed'
            }

        except Exception as e:
            print(f"❌ 动画合成失败: {e}")
            return {
                'id': f"error_{uuid.uuid4().hex[:8]}",
                'status': 'failed',
                'error': str(e)
            }

    def _generate_animation_player(self, config: Dict, article_data: Dict) -> str:
        """生成HTML5动画播放器"""

        player_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{config['title']}</title>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }}

        .animation-container {{
            width: 90%;
            max-width: 800px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}

        .animation-header {{
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 20px;
            text-align: center;
        }}

        .animation-header h1 {{
            margin: 0;
            font-size: 1.8rem;
        }}

        .animation-player {{
            position: relative;
            width: 100%;
            height: 450px;
            background: #000;
            overflow: hidden;
        }}

        .scene {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 1s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
        }}

        .scene.active {{
            opacity: 1;
        }}

        .scene img {{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }}

        .scene video {{
            width: 100%;
            height: 100%;
            object-fit: cover;
        }}

        .scene-overlay {{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 20px;
            text-align: center;
        }}

        .scene-title {{
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }}

        .scene-description {{
            font-size: 1rem;
            opacity: 0.9;
        }}

        .controls {{
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
        }}

        .control-btn {{
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }}

        .control-btn:hover {{
            background: #0056b3;
        }}

        .control-btn:disabled {{
            background: #6c757d;
            cursor: not-allowed;
        }}

        .progress-bar {{
            width: 100%;
            height: 4px;
            background: #e9ecef;
            margin: 10px 0;
            border-radius: 2px;
            overflow: hidden;
        }}

        .progress-fill {{
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }}

        .scene-info {{
            margin-top: 10px;
            color: #666;
            font-size: 0.9rem;
        }}
    </style>
</head>
<body>
    <div class="animation-container">
        <div class="animation-header">
            <h1>{config['title']}</h1>
        </div>

        <div class="animation-player" id="player">
"""

        # 添加场景
        for i, scene in enumerate(config['scenes']):
            active_class = 'active' if i == 0 else ''

            if scene.get('video') and scene['video'].get('url'):
                # 视频场景
                player_html += f"""
            <div class="scene {active_class}" data-scene="{scene['id']}" data-duration="{scene['duration']}">
                <video autoplay muted loop>
                    <source src="{scene['video']['url']}" type="video/mp4">
                </video>
                <div class="scene-overlay">
                    <div class="scene-title">{scene['title']}</div>
                    <div class="scene-description">{scene['description']}</div>
                </div>
            </div>
"""
            elif scene.get('image'):
                # 图片场景
                player_html += f"""
            <div class="scene {active_class}" data-scene="{scene['id']}" data-duration="{scene['duration']}">
                <img src="{scene['image']['url']}" alt="{scene['title']}">
                <div class="scene-overlay">
                    <div class="scene-title">{scene['title']}</div>
                    <div class="scene-description">{scene['description']}</div>
                </div>
            </div>
"""

        player_html += f"""
        </div>

        <div class="controls">
            <button class="control-btn" id="prevBtn">上一个</button>
            <button class="control-btn" id="playBtn">播放</button>
            <button class="control-btn" id="pauseBtn" style="display:none;">暂停</button>
            <button class="control-btn" id="nextBtn">下一个</button>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="scene-info">
                <span id="currentScene">1</span> / <span id="totalScenes">{len(config['scenes'])}</span>
                | 总时长: {config['total_duration']}秒
            </div>
        </div>
    </div>

    <script>
        class AnimationPlayer {{
            constructor() {{
                this.scenes = document.querySelectorAll('.scene');
                this.currentIndex = 0;
                this.isPlaying = false;
                this.timer = null;
                this.sceneStartTime = 0;

                this.initControls();
                this.updateUI();
            }}

            initControls() {{
                document.getElementById('playBtn').addEventListener('click', () => this.play());
                document.getElementById('pauseBtn').addEventListener('click', () => this.pause());
                document.getElementById('prevBtn').addEventListener('click', () => this.previousScene());
                document.getElementById('nextBtn').addEventListener('click', () => this.nextScene());
            }}

            play() {{
                this.isPlaying = true;
                document.getElementById('playBtn').style.display = 'none';
                document.getElementById('pauseBtn').style.display = 'inline-block';

                this.startSceneTimer();
            }}

            pause() {{
                this.isPlaying = false;
                document.getElementById('playBtn').style.display = 'inline-block';
                document.getElementById('pauseBtn').style.display = 'none';

                if (this.timer) {{
                    clearTimeout(this.timer);
                    this.timer = null;
                }}
            }}

            startSceneTimer() {{
                if (!this.isPlaying) return;

                const currentScene = this.scenes[this.currentIndex];
                const duration = parseInt(currentScene.dataset.duration) * 1000;

                this.sceneStartTime = Date.now();
                this.updateProgress();

                this.timer = setTimeout(() => {{
                    this.nextScene();
                }}, duration);
            }}

            updateProgress() {{
                if (!this.isPlaying) return;

                const currentScene = this.scenes[this.currentIndex];
                const duration = parseInt(currentScene.dataset.duration) * 1000;
                const elapsed = Date.now() - this.sceneStartTime;
                const progress = Math.min((elapsed / duration) * 100, 100);

                document.getElementById('progressFill').style.width = progress + '%';

                if (this.isPlaying && progress < 100) {{
                    requestAnimationFrame(() => this.updateProgress());
                }}
            }}

            showScene(index) {{
                this.scenes.forEach((scene, i) => {{
                    scene.classList.toggle('active', i === index);
                }});

                this.currentIndex = index;
                this.updateUI();

                if (this.isPlaying) {{
                    if (this.timer) clearTimeout(this.timer);
                    this.startSceneTimer();
                }}
            }}

            nextScene() {{
                if (this.currentIndex < this.scenes.length - 1) {{
                    this.showScene(this.currentIndex + 1);
                }} else {{
                    this.pause();
                    this.showScene(0);
                }}
            }}

            previousScene() {{
                if (this.currentIndex > 0) {{
                    this.showScene(this.currentIndex - 1);
                }}
            }}

            updateUI() {{
                document.getElementById('currentScene').textContent = this.currentIndex + 1;
                document.getElementById('prevBtn').disabled = this.currentIndex === 0;
                document.getElementById('nextBtn').disabled = this.currentIndex === this.scenes.length - 1;
            }}
        }}

        // 初始化播放器
        const player = new AnimationPlayer();
    </script>
</body>
</html>
"""

        return player_html

    def _analyze_diary_content(self, article_data: Dict) -> Dict[str, Any]:
        """分析日记内容，提取图片、视频和文本信息"""
        try:
            print("📊 开始分析日记内容...")

            # 提取基本信息
            title = article_data.get('title', '旅游日记')
            content = article_data.get('content', '')
            location = article_data.get('location', '未知地点')

            # 解压缩内容（如果是压缩的）
            if isinstance(content, bytes):
                try:
                    import gzip
                    content = gzip.decompress(content).decode('utf-8')
                except:
                    content = str(content)

            # 直接使用路由中已经处理好的图片和视频数据
            images = article_data.get('images', [])
            videos = article_data.get('videos', [])

            # 如果路由没有提供处理好的数据，则尝试从原始字段解析
            if not images and not videos:
                print("📋 路由未提供处理好的媒体数据，尝试从原始字段解析...")

                # 提取图片URLs
                for i in range(1, 7):  # image_url, image_url_2, ..., image_url_6
                    img_key = 'image_url' if i == 1 else f'image_url_{i}'
                    img_url = article_data.get(img_key)
                    if img_url and img_url.strip():  # 确保URL不为空
                        # 如果是相对路径，转换为完整URL
                        if img_url.startswith('/uploads/'):
                            img_url = f"http://localhost:5000{img_url}"

                        images.append({
                            'url': img_url,
                            'index': i,
                            'type': 'original'
                        })
                        print(f"📸 发现图片 {i}: {img_url}")

                # 提取视频URLs
                for i in range(1, 4):  # video_url, video_url_2, video_url_3
                    vid_key = 'video_url' if i == 1 else f'video_url_{i}'
                    vid_url = article_data.get(vid_key)
                    if vid_url and vid_url.strip():
                        videos.append({
                            'url': vid_url,
                            'index': i,
                            'type': 'original'
                        })
                        print(f"🎥 发现视频 {i}: {vid_url}")
            else:
                print(f"📋 使用路由提供的媒体数据: {len(images)}张图片, {len(videos)}个视频")
                for i, image in enumerate(images):
                    print(f"📸 图片 {i+1}: {image.get('url', 'No URL')}")
                for i, video in enumerate(videos):
                    print(f"🎥 视频 {i+1}: {video.get('url', 'No URL')}")

            # 分析文本内容
            content_keywords = self._extract_content_keywords(content)
            content_sentiment = self._analyze_content_sentiment(content)

            analysis = {
                'title': title,
                'content': content,
                'location': location,
                'images': images,
                'videos': videos,
                'keywords': content_keywords,
                'sentiment': content_sentiment,
                'content_length': len(content),
                'media_count': len(images) + len(videos)
            }

            print(f"✅ 内容分析完成: {len(images)}张图片, {len(videos)}个视频, {len(content_keywords)}个关键词")
            return analysis

        except Exception as e:
            print(f"❌ 内容分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'title': article_data.get('title', '旅游日记'),
                'content': str(article_data.get('content', '')),
                'location': article_data.get('location', '未知地点'),
                'images': article_data.get('images', []),
                'videos': article_data.get('videos', []),
                'keywords': [],
                'sentiment': 'neutral',
                'content_length': 0,
                'media_count': 0
            }

    def _extract_content_keywords(self, content: str) -> List[str]:
        """从内容中提取关键词"""
        # 简单的关键词提取
        travel_keywords = [
            '风景', '美食', '建筑', '文化', '历史', '自然', '人文', '体验',
            '景点', '美丽', '壮观', '古老', '现代', '传统', '特色', '著名',
            '游览', '参观', '品尝', '欣赏', '感受', '体验', '探索', '发现'
        ]

        found_keywords = []
        for keyword in travel_keywords:
            if keyword in content:
                found_keywords.append(keyword)

        return found_keywords[:10]  # 最多返回10个关键词

    def _analyze_content_sentiment(self, content: str) -> str:
        """分析内容情感倾向"""
        positive_words = ['美丽', '壮观', '惊艳', '喜欢', '开心', '快乐', '满意', '推荐', '值得', '精彩']
        negative_words = ['失望', '糟糕', '不好', '遗憾', '可惜', '无聊', '差劲']

        positive_count = sum(1 for word in positive_words if word in content)
        negative_count = sum(1 for word in negative_words if word in content)

        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'

    def _generate_comprehensive_script(self, content_analysis: Dict, style: str, duration: str, focus_elements: str) -> Dict[str, Any]:
        """生成综合动画脚本"""
        try:
            print("📝 生成综合动画脚本...")

            title = content_analysis['title']
            location = content_analysis['location']
            images = content_analysis['images']
            videos = content_analysis['videos']
            keywords = content_analysis['keywords']

            # 构建详细的提示词
            prompt_elements = []
            prompt_elements.append(f"标题: {title}")
            prompt_elements.append(f"地点: {location}")
            prompt_elements.append(f"风格: {style}")

            if keywords:
                prompt_elements.append(f"关键元素: {', '.join(keywords)}")

            if focus_elements:
                prompt_elements.append(f"重点关注: {focus_elements}")

            if images:
                prompt_elements.append(f"包含{len(images)}张原始图片")

            if videos:
                prompt_elements.append(f"包含{len(videos)}个原始视频")

            # 生成主要视频的提示词
            main_prompt = f"""
            创建一个关于{title}的{style}风格旅游视频。
            地点: {location}
            内容要素: {', '.join(prompt_elements)}

            视频应该包含:
            1. 开场标题展示
            2. 地点全景介绍
            3. 重点景观和体验
            4. 温馨的结尾画面

            整体风格: {style}，时长: {duration}
            画面要求: 高质量、电影级别、流畅转场
            """

            script = {
                'title': f'{title} - {style}风格视频',
                'main_prompt': main_prompt.strip(),
                'style': style,
                'duration': duration,
                'scenes': [{
                    'id': 1,
                    'type': 'main_video',
                    'title': title,
                    'prompt': main_prompt.strip(),
                    'duration': 30 if duration == "短片" else 60 if duration == "中片" else 90,
                    'includes_original_media': len(images) + len(videos) > 0
                }]
            }

            print(f"✅ 脚本生成完成: {script['title']}")
            return script

        except Exception as e:
            print(f"❌ 脚本生成失败: {e}")
            return {
                'title': f'{content_analysis["title"]} - 动画脚本',
                'main_prompt': f"创建一个关于{content_analysis['title']}的{style}风格旅游视频",
                'style': style,
                'duration': duration,
                'scenes': []
            }

    def _generate_main_travel_video(self, script: Dict, content_analysis: Dict) -> Dict[str, Any]:
        """生成完整的AIGC旅游动画 - 多步骤流程"""
        try:
            print("🎬 开始完整的AIGC旅游动画生成流程...")

            # 创建动画任务ID
            animation_id = f"aigc_anim_{uuid.uuid4().hex[:8]}"

            # 初始化进度跟踪
            progress_tracker = {
                'animation_id': animation_id,
                'total_steps': 5,
                'current_step': 0,
                'steps': {
                    'ai_image_generation': {'status': 'pending', 'progress': 0, 'description': '生成AI配图'},
                    'ai_video_generation': {'status': 'pending', 'progress': 0, 'description': '生成AI视频片段'},
                    'media_integration': {'status': 'pending', 'progress': 0, 'description': '整合所有媒体'},
                    'effect_processing': {'status': 'pending', 'progress': 0, 'description': '添加特效转场'},
                    'final_composition': {'status': 'pending', 'progress': 0, 'description': '最终合成'}
                }
            }

            # 步骤1: 生成AI配图
            print("🎨 步骤1/5: 生成AI配图...")
            progress_tracker['current_step'] = 1
            progress_tracker['steps']['ai_image_generation']['status'] = 'processing'

            ai_images = self._generate_ai_images_for_travel(script, content_analysis)

            progress_tracker['steps']['ai_image_generation']['status'] = 'completed'
            progress_tracker['steps']['ai_image_generation']['progress'] = 100

            # 步骤2: 生成AI视频片段
            print("🎬 步骤2/5: 生成AI视频片段...")
            progress_tracker['current_step'] = 2
            progress_tracker['steps']['ai_video_generation']['status'] = 'processing'

            # 构建多模态内容，包含文本、用户图片和AI图片
            content_list = self._build_multimodal_content(script, content_analysis, ai_images)

            # 调用豆包视频生成API
            video_result = self._call_doubao_multimodal_video_api(content_list, 30)

            if video_result['success']:
                # 检查是否有任务ID（异步任务）
                if video_result.get('task_id'):
                    print(f"✅ 视频生成任务已创建，等待处理: {video_result.get('task_id')}")

                    # 返回包含进度信息的异步任务状态
                    progress_tracker['steps']['ai_video_generation']['status'] = 'processing'
                    progress_tracker['steps']['ai_video_generation']['progress'] = 50

                    return {
                        'animation_id': animation_id,
                        'status': 'processing',
                        'progress': progress_tracker,
                        'ai_images': ai_images,
                        'video_task': {
                            'task_id': video_result.get('task_id'),
                            'status': 'processing',
                            'platform': video_result.get('model', 'doubao'),
                            'type': video_result.get('type', 't2v')
                        },
                        'user_media': {
                            'images': content_analysis.get('images', []),
                            'videos': content_analysis.get('videos', [])
                        }
                    }
                else:
                    # 如果有直接的URL（同步完成）
                    print(f"✅ 视频生成立即完成")
                    progress_tracker['steps']['ai_video_generation']['status'] = 'completed'
                    progress_tracker['steps']['ai_video_generation']['progress'] = 100

                    # 继续后续步骤
                    return self._continue_animation_processing(
                        animation_id, progress_tracker, ai_images,
                        video_result, content_analysis, script
                    )
            else:
                print("⚠️ AI视频生成失败，使用演示内容")
                progress_tracker['steps']['ai_video_generation']['status'] = 'failed'

                # 使用演示视频继续流程
                demo_video = {
                    'url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                    'status': 'completed',
                    'platform': 'demo'
                }

                return self._continue_animation_processing(
                    animation_id, progress_tracker, ai_images,
                    demo_video, content_analysis, script
                )

        except Exception as e:
            print(f"❌ AIGC动画生成异常: {e}")
            return {
                'animation_id': f"error_{uuid.uuid4().hex[:8]}",
                'status': 'error',
                'error': str(e),
                'progress': {
                    'current_step': 0,
                    'total_steps': 5,
                    'steps': {}
                }
            }

    def _continue_animation_processing(self, animation_id: str, progress_tracker: Dict,
                                     ai_images: List[Dict], video_result: Dict,
                                     content_analysis: Dict, script: Dict) -> Dict[str, Any]:
        """继续动画处理的后续步骤"""
        try:
            # 步骤3: 整合所有媒体内容
            print("🎭 步骤3/5: 整合媒体内容...")
            progress_tracker['current_step'] = 3
            progress_tracker['steps']['media_integration']['status'] = 'processing'

            integrated_timeline = self._create_media_timeline(
                user_images=content_analysis.get('images', []),
                user_videos=content_analysis.get('videos', []),
                ai_images=ai_images,
                ai_video=video_result,
                script=script
            )

            progress_tracker['steps']['media_integration']['status'] = 'completed'
            progress_tracker['steps']['media_integration']['progress'] = 100

            # 步骤4: 添加特效和转场
            print("✨ 步骤4/5: 添加特效和转场...")
            progress_tracker['current_step'] = 4
            progress_tracker['steps']['effect_processing']['status'] = 'processing'

            enhanced_timeline = self._add_effects_and_transitions(integrated_timeline, script)

            progress_tracker['steps']['effect_processing']['status'] = 'completed'
            progress_tracker['steps']['effect_processing']['progress'] = 100

            # 步骤5: 最终合成
            print("🎬 步骤5/5: 最终合成...")
            progress_tracker['current_step'] = 5
            progress_tracker['steps']['final_composition']['status'] = 'processing'

            final_animation = self._compose_final_aigc_animation(
                animation_id, enhanced_timeline, script, content_analysis, progress_tracker
            )

            progress_tracker['steps']['final_composition']['status'] = 'completed'
            progress_tracker['steps']['final_composition']['progress'] = 100

            print("✅ 完整AIGC旅游动画生成完成!")

            return final_animation

        except Exception as e:
            print(f"❌ 动画后续处理失败: {e}")
            return {
                'animation_id': animation_id,
                'status': 'error',
                'error': str(e),
                'progress': progress_tracker
            }

    def _create_media_timeline(self, user_images: List[Dict], user_videos: List[Dict],
                              ai_images: List[Dict], ai_video: Dict, script: Dict) -> Dict[str, Any]:
        """创建媒体时间线"""
        timeline = {
            'total_duration': 30,  # 总时长30秒
            'segments': []
        }

        # 开场：AI全景图 (0-5秒)
        if ai_images:
            timeline['segments'].append({
                'start_time': 0,
                'duration': 5,
                'type': 'image_display',
                'content': ai_images[0],
                'effect': 'fade_in',
                'title': f"欢迎来到{script.get('title', '旅游地点')}"
            })

        # 主体：AI视频 (5-20秒)
        timeline['segments'].append({
            'start_time': 5,
            'duration': 15,
            'type': 'ai_video',
            'content': ai_video,
            'effect': 'smooth_transition'
        })

        # 用户内容展示 (20-25秒)
        if user_images:
            timeline['segments'].append({
                'start_time': 20,
                'duration': 5,
                'type': 'user_gallery',
                'content': user_images[:3],  # 最多3张用户图片
                'effect': 'slideshow'
            })

        # 结尾：AI地标图 (25-30秒)
        if len(ai_images) > 1:
            timeline['segments'].append({
                'start_time': 25,
                'duration': 5,
                'type': 'image_display',
                'content': ai_images[1],
                'effect': 'fade_out',
                'title': "感谢观看"
            })

        return timeline

    def _add_effects_and_transitions(self, timeline: Dict, script: Dict) -> Dict[str, Any]:
        """添加特效和转场"""
        enhanced_timeline = timeline.copy()

        # 为每个片段添加转场效果
        for i, segment in enumerate(enhanced_timeline['segments']):
            if i > 0:  # 不是第一个片段
                segment['transition_in'] = 'crossfade'

            if i < len(enhanced_timeline['segments']) - 1:  # 不是最后一个片段
                segment['transition_out'] = 'crossfade'

        # 添加背景音乐配置
        enhanced_timeline['background_music'] = {
            'style': script.get('style', '温馨'),
            'volume': 0.3,
            'fade_in': True,
            'fade_out': True
        }

        # 添加字幕配置
        enhanced_timeline['subtitles'] = {
            'enabled': True,
            'style': 'elegant',
            'position': 'bottom'
        }

        return enhanced_timeline

    def _compose_final_aigc_animation(self, animation_id: str, timeline: Dict,
                                    script: Dict, content_analysis: Dict,
                                    progress_tracker: Dict) -> Dict[str, Any]:
        """合成最终的AIGC动画"""
        return {
            'id': animation_id,
            'type': 'aigc_multimodal_animation',
            'title': script['title'],
            'status': 'completed',
            'progress': progress_tracker,
            'timeline': timeline,
            'main_video': {
                'url': timeline['segments'][1]['content'].get('url') if len(timeline['segments']) > 1 else None,
                'status': 'completed',
                'platform': 'doubao_aigc'
            },
            'ai_generated_content': {
                'images': len([s for s in timeline['segments'] if s['type'] == 'image_display']),
                'videos': len([s for s in timeline['segments'] if s['type'] == 'ai_video'])
            },
            'user_content': {
                'images': content_analysis.get('images', []),
                'videos': content_analysis.get('videos', [])
            },
            'metadata': {
                'location': content_analysis['location'],
                'style': script['style'],
                'duration': timeline['total_duration'],
                'generation_time': datetime.now().isoformat()
            },
            'preview_url': timeline['segments'][1]['content'].get('url') if len(timeline['segments']) > 1 else None
        }

    def _compose_final_video_animation(self, script: Dict, main_video: Dict, content_analysis: Dict) -> Dict[str, Any]:
        """合成最终的视频动画"""
        try:
            print("🎭 合成最终视频动画...")

            animation_id = f"video_anim_{uuid.uuid4().hex[:8]}"

            # 创建最终动画配置
            final_animation = {
                'id': animation_id,
                'type': 'single_video',
                'title': script['title'],
                'main_video': main_video,
                'original_media': {
                    'images': content_analysis['images'],
                    'videos': content_analysis['videos']
                },
                'metadata': {
                    'location': content_analysis['location'],
                    'keywords': content_analysis['keywords'],
                    'sentiment': content_analysis['sentiment'],
                    'style': script['style'],
                    'duration': script['duration']
                },
                'player_config': {
                    'autoplay': False,
                    'controls': True,
                    'width': '100%',
                    'height': 'auto'
                },
                'preview_url': main_video.get('url'),
                'status': 'completed'
            }

            print(f"✅ 最终动画合成完成: {animation_id}")
            return final_animation

        except Exception as e:
            print(f"❌ 最终动画合成失败: {e}")
            return {
                'id': f"error_{uuid.uuid4().hex[:8]}",
                'type': 'error',
                'title': script.get('title', '动画生成失败'),
                'error': str(e),
                'status': 'error'
            }

    def _generate_ai_images_for_travel(self, script: Dict, content_analysis: Dict) -> List[Dict]:
        """生成旅游相关的AI配图"""
        try:
            print("🎨 开始生成AI配图...")

            ai_images = []
            location = content_analysis.get('location', '旅游地点')
            style = script.get('style', '温馨')

            # 定义需要生成的图片场景
            image_prompts = [
                f"{location}的全景风光，{style}风格，高质量摄影",
                f"{location}的标志性建筑或景观，{style}氛围，电影级画质",
                f"{location}的特色体验场景，{style}色调，艺术摄影风格"
            ]

            for i, prompt in enumerate(image_prompts):
                print(f"🎨 生成第{i+1}张AI配图: {prompt[:50]}...")

                # 调用豆包图片生成API
                image_result = self._call_doubao_image_api(prompt)

                if image_result.get('success'):
                    ai_images.append({
                        'id': f"ai_img_{i+1}",
                        'url': image_result.get('url'),
                        'prompt': prompt,
                        'scene_type': ['panorama', 'landmark', 'experience'][i],
                        'status': 'completed'
                    })
                    print(f"✅ AI配图{i+1}生成成功")
                else:
                    print(f"⚠️ AI配图{i+1}生成失败，使用占位图")
                    ai_images.append({
                        'id': f"ai_img_{i+1}",
                        'url': f'https://picsum.photos/800/600?random={i+1}',
                        'prompt': prompt,
                        'scene_type': ['panorama', 'landmark', 'experience'][i],
                        'status': 'fallback'
                    })

            print(f"✅ AI配图生成完成，共{len(ai_images)}张")
            return ai_images

        except Exception as e:
            print(f"❌ AI配图生成失败: {e}")
            return []

    def _call_doubao_image_api(self, prompt: str) -> Dict[str, Any]:
        """调用豆包图片生成API"""
        try:
            if self.demo_mode:
                return {
                    'success': True,
                    'url': f'https://picsum.photos/800/600?random={hash(prompt) % 1000}',
                    'status': 'completed'
                }

            # 真实的豆包图片生成API调用
            url = f"{self.base_url}/api/v3/images/generations"

            data = {
                "model": self.image_model,
                "prompt": prompt,
                "size": "1024x1024",
                "quality": "standard",
                "n": 1
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            print(f"📡 发送图片生成请求: {prompt[:50]}...")

            import requests
            response = requests.post(url, headers=headers, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if result.get('data') and len(result['data']) > 0:
                    image_url = result['data'][0].get('url')
                    if image_url:
                        return {
                            'success': True,
                            'url': image_url,
                            'status': 'completed'
                        }

            print(f"⚠️ 图片生成API调用失败: {response.status_code}")
            return {'success': False}

        except Exception as e:
            print(f"❌ 图片生成异常: {e}")
            return {'success': False}

    def _build_multimodal_content(self, script: Dict, content_analysis: Dict, ai_images: List[Dict]) -> List[Dict]:
        """构建多模态内容列表"""
        content_list = []

        # 添加主要文本提示词
        main_prompt = script.get('main_prompt', f"创建一个关于{script.get('title', '旅游')}的{script.get('style', '温馨')}风格视频")
        content_list.append({
            "type": "text",
            "text": main_prompt
        })

        # 添加用户图片（最多2张）
        user_images = content_analysis.get('images', [])
        if user_images:
            for i, image in enumerate(user_images[:2]):
                if image.get('url'):
                    content_list.append({
                        "type": "text",
                        "text": f"用户图片{i+1}: 这是用户在{content_analysis.get('location', '旅游地点')}拍摄的真实照片"
                    })
                    content_list.append({
                        "type": "image_url",
                        "image_url": {"url": image['url']}
                    })

        # 添加AI生成的图片（最多1张）
        if ai_images:
            best_ai_image = ai_images[0]  # 使用第一张AI图片
            content_list.append({
                "type": "text",
                "text": f"AI配图: {best_ai_image.get('prompt', '风景图片')}"
            })
            content_list.append({
                "type": "image_url",
                "image_url": {"url": best_ai_image['url']}
            })

        return content_list

    def _call_doubao_multimodal_video_api(self, content_list: List[Dict], duration: int = 30) -> Dict[str, Any]:
        """调用豆包图生视频API - 使用用户图片生成视频"""
        try:
            if self.demo_mode:
                print("⚠️ 演示模式：模拟图生视频生成")
                return {
                    'success': True,
                    'url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                    'status': 'completed',
                    'model': 'demo'
                }

            # 真实API调用（当demo_mode=False时）
            url = f"{self.base_url}{self.video_endpoint}"

            # 检查是否有图片内容，决定使用哪个模型
            has_images = any(item.get('type') == 'image_url' for item in content_list)
            model_to_use = self.video_model_i2v if has_images else self.video_model_t2v

            print(f"🎬 选择模型: {'图生视频(i2v)' if has_images else '文生视频(t2v)'} - {model_to_use}")

            # 直接使用API Key
            if self.api_key:
                # 根据豆包官方文档构建请求参数 - 使用content数组格式
                if has_images:
                    # 图生视频模式：需要提取首帧图片和文本提示词
                    first_image = None
                    text_prompt = ""

                    for item in content_list:
                        if item.get('type') == 'image_url':
                            if not first_image:
                                first_image = item['image_url']['url']
                        elif item.get('type') == 'text':
                            text_prompt += item['text'] + " "

                    # 图生视频API参数格式 - 使用content数组
                    data = {
                        "model": model_to_use,
                        "content": [
                            {
                                "type": "text",
                                "text": text_prompt.strip()
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": first_image
                                }
                            }
                        ],
                        "video_length": "5s",
                        "resolution": "720p"
                    }
                    print(f"📸 图生视频请求 - 首帧图片: {first_image}")
                    print(f"📝 文本提示词: {text_prompt.strip()[:100]}...")
                else:
                    # 文生视频模式：只需要文本提示词
                    text_prompt = ""
                    for item in content_list:
                        if item.get('type') == 'text':
                            text_prompt += item['text'] + " "

                    # 文生视频API参数格式 - 使用content数组
                    data = {
                        "model": model_to_use,
                        "content": [
                            {
                                "type": "text",
                                "text": text_prompt.strip()
                            }
                        ],
                        "video_length": "5s",
                        "resolution": "720p"
                    }
                    print(f"📝 文生视频请求 - 提示词: {text_prompt.strip()[:100]}...")

                # 使用API Key进行Bearer Token认证
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }

                print(f"📡 发送{'图生视频' if has_images else '文生视频'}请求到: {url}")
                print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                print(f"🔑 使用API Key: {self.api_key[:10]}...")

                import requests
                response = requests.post(url, headers=headers, json=data, timeout=60)

                print(f"📡 豆包视频API响应状态: {response.status_code}")
                print(f"📋 响应内容: {response.text}")
                print(f"📋 响应头: {dict(response.headers)}")

                if response.status_code == 200:
                    result = response.json()
                    print(f"📋 解析后的响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

                    task_id = result.get("id") or result.get("task_id")
                    if task_id:
                        print(f"✅ 豆包{'图生视频' if has_images else '文生视频'}任务创建成功: {task_id}")
                        return {
                            'success': True,
                            'task_id': task_id,
                            'status': 'processing',
                            'model': model_to_use,
                            'type': 'i2v' if has_images else 't2v'
                        }
                    else:
                        print(f"❌ 响应中未找到任务ID: {result}")
                        return {
                            'success': False,
                            'error': '响应中未找到任务ID',
                            'response': result
                        }
                else:
                    print(f"❌ 豆包视频API调用失败: {response.status_code} - {response.text}")
                    try:
                        error_detail = response.json()
                        print(f"❌ 错误详情: {json.dumps(error_detail, ensure_ascii=False, indent=2)}")
                    except:
                        pass
            else:
                print("❌ 无法获取API Key，降级到演示模式")

            print("⚠️ 视频API调用失败，降级到演示模式")
            return {
                'success': True,
                'url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                'status': 'completed',
                'model': 'demo'
            }

        except Exception as e:
            print(f"❌ 视频生成异常: {e}")
            return {
                'success': True,
                'url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                'status': 'completed',
                'model': 'demo'
            }

    def query_video_status(self, task_id: str) -> Dict[str, Any]:
        """查询视频生成状态"""
        try:
            print(f"🔍 查询视频生成状态: {task_id}")

            if self.demo_mode:
                print("⚠️ 演示模式：返回模拟状态")
                return {
                    'success': True,
                    'status': 'completed',
                    'video_url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                    'progress': 100,
                    'platform': 'demo'
                }

            # 真实API调用豆包查询接口
            if self.api_key:
                url = f"{self.base_url}/api/v3/contents/generations/tasks/{task_id}"

                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                print(f"📡 查询豆包视频状态: {url}")

                import requests
                response = requests.get(url, headers=headers, timeout=30)

                print(f"📡 豆包查询API响应状态: {response.status_code}")
                print(f"📋 响应内容: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    status = result.get('status', 'unknown')

                    # 豆包API状态映射
                    status_map = {
                        'pending': 'processing',
                        'running': 'processing',
                        'succeeded': 'completed',
                        'failed': 'failed'
                    }

                    mapped_status = status_map.get(status, status)

                    response_data = {
                        'success': True,
                        'status': mapped_status,
                        'platform': 'doubao',
                        'task_id': task_id
                    }

                    # 如果完成，获取视频URL
                    if mapped_status == 'completed':
                        video_url = result.get('output', {}).get('video_url')
                        if video_url:
                            response_data['video_url'] = video_url
                            print(f"✅ 视频生成完成: {video_url}")
                        else:
                            print("⚠️ 视频完成但未找到URL")
                    elif mapped_status == 'processing':
                        progress = result.get('progress', 0)
                        response_data['progress'] = progress
                        print(f"⏳ 视频生成中: {progress}%")
                    elif mapped_status == 'failed':
                        error_msg = result.get('error', {}).get('message', '生成失败')
                        response_data['error'] = error_msg
                        print(f"❌ 视频生成失败: {error_msg}")

                    return response_data
                else:
                    print(f"❌ 豆包查询API调用失败: {response.status_code}")

            # 降级到演示模式
            print("⚠️ 降级到演示模式")
            return {
                'success': True,
                'status': 'completed',
                'video_url': 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                'progress': 100,
                'platform': 'demo'
            }

        except Exception as e:
            print(f"❌ 查询视频状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
