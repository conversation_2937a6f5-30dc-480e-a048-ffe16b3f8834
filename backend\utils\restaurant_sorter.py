"""
餐馆排序工具类

提供高效的餐馆排序算法，包括：
1. 快速排序算法
2. 堆排序算法
3. 部分排序算法（只排序前K个元素）
"""
import heapq
import random
from typing import List, Callable, Any, Dict, Union
from models.restaurant import Restaurant


class RestaurantSorter:
    """餐馆排序工具类"""

    @staticmethod
    def quick_sort(restaurants: List[Restaurant], key_func: Callable[[Restaurant], Any], reverse: bool = False) -> List[Restaurant]:
        """
        使用快速排序算法对餐馆列表进行排序

        Args:
            restaurants: 餐馆列表
            key_func: 排序关键字函数，例如 lambda x: x.popularity
            reverse: 是否降序排序，默认为False（升序）

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        # 创建副本，避免修改原始列表
        restaurants_copy = restaurants.copy()

        def _quick_sort(arr, low, high):
            if low < high:
                # 分区操作，返回分区点
                pivot_index = _partition(arr, low, high)
                # 对分区点左侧子数组进行递归排序
                _quick_sort(arr, low, pivot_index - 1)
                # 对分区点右侧子数组进行递归排序
                _quick_sort(arr, pivot_index + 1, high)

        def _partition(arr, low, high):
            # 选择随机元素作为pivot，减少最坏情况的发生
            pivot_index = random.randint(low, high)
            arr[pivot_index], arr[high] = arr[high], arr[pivot_index]

            pivot = key_func(arr[high])
            i = low - 1

            for j in range(low, high):
                # 根据reverse参数决定比较方式
                if (not reverse and key_func(arr[j]) <= pivot) or (reverse and key_func(arr[j]) >= pivot):
                    i += 1
                    arr[i], arr[j] = arr[j], arr[i]

            arr[i + 1], arr[high] = arr[high], arr[i + 1]
            return i + 1

        _quick_sort(restaurants_copy, 0, len(restaurants_copy) - 1)
        return restaurants_copy

    @staticmethod
    def heap_sort(restaurants: List[Restaurant], key_func: Callable[[Restaurant], Any], reverse: bool = False) -> List[Restaurant]:
        """
        使用堆排序算法对餐馆列表进行排序

        Args:
            restaurants: 餐馆列表
            key_func: 排序关键字函数，例如 lambda x: x.popularity
            reverse: 是否降序排序，默认为False（升序）

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        # 创建副本，避免修改原始列表
        restaurants_copy = restaurants.copy()

        # 使用Python的heapq模块实现堆排序
        if reverse:
            # 降序排序，使用负值
            heap = [(- key_func(restaurant), i, restaurant) for i, restaurant in enumerate(restaurants_copy)]
        else:
            # 升序排序
            heap = [(key_func(restaurant), i, restaurant) for i, restaurant in enumerate(restaurants_copy)]

        # 构建堆
        heapq.heapify(heap)

        # 从堆中依次取出元素，实现排序
        result = []
        while heap:
            _, _, restaurant = heapq.heappop(heap)
            result.append(restaurant)

        return result

    @staticmethod
    def top_k_sort(restaurants: List[Restaurant], k: int, key_func: Callable[[Restaurant], Any], reverse: bool = True) -> List[Restaurant]:
        """
        只排序前K个元素的高效算法

        Args:
            restaurants: 餐馆列表
            k: 需要排序的前K个元素
            key_func: 排序关键字函数，例如 lambda x: x.popularity
            reverse: 是否降序排序，默认为True（降序）

        Returns:
            排序后的前K个餐馆
        """
        if not restaurants:
            return []

        if k >= len(restaurants):
            # 如果k大于等于列表长度，直接使用完全排序
            return RestaurantSorter.quick_sort(restaurants, key_func, reverse)

        # 使用堆排序找出前K个元素
        if reverse:
            # 降序排序，使用最小堆找出最大的K个元素
            heap = [(key_func(restaurant), i, restaurant) for i, restaurant in enumerate(restaurants[:k])]
            heapq.heapify(heap)

            # 遍历剩余元素
            for i, restaurant in enumerate(restaurants[k:], k):
                key = key_func(restaurant)
                if key > heap[0][0]:
                    # 如果当前元素比堆顶元素大，替换堆顶元素
                    heapq.heapreplace(heap, (key, i, restaurant))

            # 从堆中取出元素，并按降序排列
            result = [item[2] for item in sorted(heap, reverse=True)]
        else:
            # 升序排序，使用最大堆找出最小的K个元素
            heap = [(- key_func(restaurant), i, restaurant) for i, restaurant in enumerate(restaurants[:k])]
            heapq.heapify(heap)

            # 遍历剩余元素
            for i, restaurant in enumerate(restaurants[k:], k):
                key = - key_func(restaurant)
                if key > heap[0][0]:
                    # 如果当前元素比堆顶元素大，替换堆顶元素
                    heapq.heapreplace(heap, (key, i, restaurant))

            # 从堆中取出元素，并按升序排列
            result = [item[2] for item in sorted(heap, key=lambda x: -x[0])]

        return result

    @staticmethod
    def multi_key_sort(restaurants: List[Restaurant], key_funcs: List[Callable[[Restaurant], Any]], reverse_list: List[bool] = None) -> List[Restaurant]:
        """
        多关键字排序

        Args:
            restaurants: 餐馆列表
            key_funcs: 排序关键字函数列表，按优先级排序
            reverse_list: 是否降序排序的列表，与key_funcs一一对应

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        # 创建副本，避免修改原始列表
        restaurants_copy = restaurants.copy()

        # 如果reverse_list为None，默认所有关键字都是升序排序
        if reverse_list is None:
            reverse_list = [False] * len(key_funcs)

        # 确保reverse_list长度与key_funcs一致
        if len(reverse_list) != len(key_funcs):
            reverse_list = reverse_list + [False] * (len(key_funcs) - len(reverse_list))

        # 使用Python的sorted函数实现多关键字排序
        # 从最低优先级到最高优先级依次排序
        for i in range(len(key_funcs) - 1, -1, -1):
            restaurants_copy = sorted(restaurants_copy, key=key_funcs[i], reverse=reverse_list[i])

        return restaurants_copy

    @staticmethod
    def sort_by_distance(restaurants: List[Restaurant], location_x: float, location_y: float, reverse: bool = False) -> List[Restaurant]:
        """
        按照与指定位置的距离排序

        Args:
            restaurants: 餐馆列表
            location_x: 位置x坐标
            location_y: 位置y坐标
            reverse: 是否降序排序，默认为False（升序）

        Returns:
            排序后的餐馆列表
        """
        if not restaurants:
            return []

        def distance_key(restaurant):
            # 计算欧几里得距离
            dx = restaurant.x - location_x
            dy = restaurant.y - location_y
            return (dx * dx + dy * dy) ** 0.5

        return RestaurantSorter.quick_sort(restaurants, distance_key, reverse)

    @staticmethod
    def top_k_by_distance(restaurants: List[Restaurant], k: int, location_x: float, location_y: float) -> List[Restaurant]:
        """
        找出距离指定位置最近的前K个餐馆

        Args:
            restaurants: 餐馆列表
            k: 需要的餐馆数量
            location_x: 位置x坐标
            location_y: 位置y坐标

        Returns:
            距离最近的前K个餐馆
        """
        def distance_key(restaurant):
            # 计算欧几里得距离
            dx = restaurant.x - location_x
            dy = restaurant.y - location_y
            return (dx * dx + dy * dy) ** 0.5

        return RestaurantSorter.top_k_sort(restaurants, k, distance_key, False)  # 升序排序
