<template>
  <div class="travel-animation-generator">
    <div class="generator-header">
      <h2>旅游动画生成</h2>
      <p>基于旅游日记的图片、内容、视频生成动画脚本和扩写内容</p>
    </div>

    <div class="form-section">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 选择已有日记 -->
        <el-tab-pane label="选择已有日记" name="existing">
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
            <!-- 文章选择 -->
            <el-form-item label="选择日记" prop="article" required>
              <el-select
                v-model="form.article"
                filterable
                remote
                reserve-keyword
                placeholder="请输入文章标题或地点名称进行搜索"
                :remote-method="searchArticles"
                :loading="searchLoading"
                style="width: 100%"
                @change="handleArticleChange"
              >
                <el-option
                  v-for="article in articleOptions"
                  :key="article.article_id"
                  :label="`${article.title} - ${article.location}`"
                  :value="article.article_id"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1;">
                      <div style="font-weight: 500; color: #333;">{{ article.title }}</div>
                      <div style="color: #8492a6; font-size: 12px; margin-top: 2px;">
                        <i class="el-icon-location"></i> {{ article.location }}
                        <span style="margin-left: 10px;">
                          <i class="el-icon-time"></i> {{ formatDate(article.created_at) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </el-option>
              </el-select>
              <div class="form-tip">
                <div v-if="searchLoading" style="color: #409eff;">
                  <i class="el-icon-loading"></i> 正在搜索您的日记...
                </div>
                <div v-else-if="articleOptions.length === 0" style="color: #f56c6c;">
                  <i class="el-icon-warning"></i> 暂无日记数据，请先发布一些旅游日记
                </div>
                <div v-else style="color: #67c23a;">
                  找到 {{ articleOptions.length }} 篇日记，支持输入标题或地点进行搜索
                </div>
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 上传新内容 -->
        <el-tab-pane label="上传新内容" name="upload">
          <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="120px">
            <!-- 日记标题 -->
            <el-form-item label="日记标题" prop="title">
              <el-input
                v-model="uploadForm.title"
                placeholder="请输入您的旅游日记标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <!-- 日记内容 -->
            <el-form-item label="日记内容" prop="content">
              <el-input
                v-model="uploadForm.content"
                type="textarea"
                :rows="6"
                placeholder="请详细描述您的旅游经历、感受和见闻..."
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>

            <!-- 旅游地点 -->
            <el-form-item label="旅游地点" prop="location">
              <el-input
                v-model="uploadForm.location"
                placeholder="请输入旅游地点"
                maxlength="50"
              />
            </el-form-item>

            <!-- 图片上传 -->
            <el-form-item label="日记图片">
              <el-upload
                v-model:file-list="imageFileList"
                :action="uploadImageUrl"
                list-type="picture-card"
                :on-preview="handleImagePreview"
                :on-remove="handleImageRemove"
                :before-upload="beforeImageUpload"
                :on-success="handleImageSuccess"
                :limit="6"
                accept="image/*"
                multiple
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 jpg/png/gif 格式，单个文件不超过 10MB，最多上传 6 张图片
                  </div>
                </template>
              </el-upload>
            </el-form-item>

            <!-- 视频上传 -->
            <el-form-item label="日记视频">
              <el-upload
                v-model:file-list="videoFileList"
                :action="uploadVideoUrl"
                :on-preview="handleVideoPreview"
                :on-remove="handleVideoRemove"
                :before-upload="beforeVideoUpload"
                :on-success="handleVideoSuccess"
                :limit="3"
                accept="video/*"
                multiple
              >
                <el-button type="primary">
                  <el-icon><VideoCamera /></el-icon>
                  上传视频
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 mp4/avi/mov 格式，单个文件不超过 100MB，最多上传 3 个视频
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <el-form :model="form" label-width="120px" style="margin-top: 20px;">

        <!-- 动画风格 -->
        <el-form-item label="动画风格" prop="animationStyle">
          <el-radio-group v-model="form.animationStyle">
            <el-radio label="温馨">温馨治愈</el-radio>
            <el-radio label="活泼">活泼欢快</el-radio>
            <el-radio label="文艺">文艺清新</el-radio>
            <el-radio label="震撼">震撼大气</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 动画时长 -->
        <el-form-item label="动画时长" prop="duration">
          <el-radio-group v-model="form.duration">
            <el-radio label="短片">短片 (30-60秒)</el-radio>
            <el-radio label="中等">中等 (1-3分钟)</el-radio>
            <el-radio label="长片">长片 (3-5分钟)</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 重点元素 -->
        <el-form-item label="重点元素" prop="focusElements">
          <el-checkbox-group v-model="form.focusElements">
            <el-checkbox label="风景">自然风景</el-checkbox>
            <el-checkbox label="建筑">建筑特色</el-checkbox>
            <el-checkbox label="人物">人物活动</el-checkbox>
            <el-checkbox label="美食">美食展示</el-checkbox>
            <el-checkbox label="文化">文化元素</el-checkbox>
            <el-checkbox label="情感">情感表达</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateAnimation"
            :loading="loading"
            size="large"
            style="width: 200px"
          >
            <i class="el-icon-video-camera"></i>
            {{ getLoadingText() }}
          </el-button>
          <div v-if="loading" class="loading-tips">
            <p>AI正在生成动画脚本...</p>
            <p>这可能需要30-60秒，请耐心等待</p>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 文章预览 -->
    <div v-if="selectedArticle" class="article-preview">
      <h3>日记预览</h3>
      <div class="preview-content">
        <div class="article-info">
          <h4>{{ selectedArticle.title }}</h4>
          <div class="article-meta">
            <span><i class="el-icon-location"></i> {{ selectedArticle.location }}</span>
            <span><i class="el-icon-time"></i> {{ formatDate(selectedArticle.created_at) }}</span>
          </div>
          <div class="media-count">
            <span><i class="el-icon-picture"></i> {{ getMediaCount().images }}张图片</span>
            <span><i class="el-icon-video-camera"></i> {{ getMediaCount().videos }}个视频</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedAnimation" class="result-section">
      <div class="result-header">
        <h3>🎬 生成的AIGC动画</h3>
        <div class="header-actions">
          <el-button
            v-if="generatedAnimation.animation?.player_url"
            type="primary"
            @click="openAnimationPlayer"
            icon="el-icon-video-play"
          >
            播放动画
          </el-button>
          <el-button
            v-if="generatedAnimation.animation?.preview_image"
            type="success"
            @click="previewAnimation"
            icon="el-icon-view"
          >
            预览动画
          </el-button>
          <el-button type="default" @click="copyToClipboard" icon="el-icon-document-copy">
            复制信息
          </el-button>
        </div>
      </div>

      <div class="animation-content">
        <!-- 动画播放器展示 -->
        <div v-if="generatedAnimation && generatedAnimation.animation" class="animation-player-section">
          <div class="animation-title">
            <h3>{{ generatedAnimation.animation.title || selectedArticle?.title }}</h3>
            <p class="animation-meta">
              风格: {{ form.animationStyle }} ·
              时长: {{ generatedAnimation.animation.config?.total_duration || form.duration }}秒 ·
              状态: {{ getAnimationStatus() }}
            </p>
          </div>

          <!-- MP4视频播放器 -->
          <div v-if="generatedAnimation.animation.video_url && generatedAnimation.animation.video_url.endsWith('.mp4')" class="video-player-section">
            <div class="video-container">
              <video
                :src="`http://localhost:5000${generatedAnimation.animation.video_url}`"
                controls
                preload="metadata"
                class="main-video"
                @error="handleVideoError"
              >
                您的浏览器不支持视频播放
              </video>
            </div>
            <div class="video-controls">
              <el-button
                type="primary"
                @click="downloadVideo"
                icon="el-icon-download"
              >
                下载视频
              </el-button>
              <el-button
                type="success"
                @click="shareVideo"
                icon="el-icon-share"
              >
                分享视频
              </el-button>
            </div>
          </div>

          <!-- HTML5播放器（备用） -->
          <div v-else-if="generatedAnimation.animation.video_url" class="html-player-section">
            <div class="animation-preview">
              <div class="preview-placeholder">
                <i class="el-icon-video-play" style="font-size: 4rem; color: #409eff;"></i>
                <p>点击播放动画</p>
              </div>
              <div class="preview-overlay">
                <el-button
                  type="primary"
                  size="large"
                  @click="openAnimationPlayer"
                  icon="el-icon-video-play"
                  circle
                />
              </div>
            </div>
          </div>

          <!-- 动画信息 -->
          <div class="animation-info">
            <div class="info-item">
              <span class="label">动画ID:</span>
              <span class="value">{{ generatedAnimation.animation.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">视频类型:</span>
              <span class="value">{{ getVideoType() }}</span>
            </div>
            <div class="info-item">
              <span class="label">生成状态:</span>
              <span class="value status" :class="generatedAnimation.animation.status">
                {{ getAnimationStatusText() }}
              </span>
            </div>
          </div>
        </div>

        <!-- 原有的脚本展示（作为备用） -->
        <div v-else class="script-fallback">
          <div class="animation-title">
            <h3>{{ selectedArticle?.title }} - 动画脚本</h3>
            <p class="animation-meta">风格: {{ form.animationStyle }} · 时长: {{ form.duration }}</p>
          </div>
        </div>

        <!-- 动画脚本 -->
        <div v-if="generatedAnimation.animation_script" class="script-section">
          <h4>动画脚本</h4>
          <div class="script-overview">
            <div class="overview-item">
              <span class="label">总时长:</span>
              <span class="value">{{ generatedAnimation.animation_script.total_duration }}</span>
            </div>
            <div class="overview-item">
              <span class="label">场景数:</span>
              <span class="value">{{ generatedAnimation.animation_script.scenes?.length || 0 }}个</span>
            </div>
            <div class="overview-item">
              <span class="label">风格:</span>
              <span class="value">{{ generatedAnimation.animation_script.style }}</span>
            </div>
          </div>

          <div class="scenes-list">
            <div
              v-for="scene in generatedAnimation.animation_script.scenes"
              :key="scene.scene_number"
              class="scene-item"
            >
              <div class="scene-header">
                <span class="scene-number">场景 {{ scene.scene_number }}</span>
                <span class="scene-duration">{{ scene.duration }}</span>
              </div>
              <div class="scene-content">
                <div class="scene-description">
                  <h5>场景描述</h5>
                  <p>{{ scene.description }}</p>
                </div>
                <div class="scene-details">
                  <div class="detail-item">
                    <span class="detail-label">镜头角度:</span>
                    <span>{{ scene.camera_angle }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">配乐建议:</span>
                    <span>{{ scene.music_suggestion }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI生成的图片展示 -->
        <div v-if="generatedAnimation.generated_media?.images?.length > 0" class="aigc-images-section">
          <h4>🎨 AI生成图片</h4>
          <div class="images-grid">
            <div
              v-for="(image, index) in generatedAnimation.generated_media.images"
              :key="index"
              class="image-item"
            >
              <div class="image-container">
                <img :src="image.url" :alt="image.prompt || `AI图片${index + 1}`" @error="handleImageError" />
                <div class="image-overlay">
                  <div class="image-info">
                    <div class="scene-tag">{{ image.scene_type || `图片${index + 1}` }}</div>
                    <div class="platform-tag">{{ image.platform || 'AI' }}</div>
                  </div>
                </div>
              </div>
              <div class="image-details">
                <p class="image-description">{{ image.id || `AI配图${index + 1}` }}</p>
                <p class="image-prompt" v-if="image.prompt">提示词: {{ image.prompt }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户媒体展示 -->
        <div v-if="generatedAnimation.user_media && (generatedAnimation.user_media.images?.length > 0 || generatedAnimation.user_media.videos?.length > 0)" class="user-media-section">
          <h4>📸 用户媒体</h4>

          <!-- 用户图片 -->
          <div v-if="generatedAnimation.user_media.images?.length > 0" class="user-images">
            <h5>用户图片 ({{ generatedAnimation.user_media.images.length }}张)</h5>
            <div class="images-grid">
              <div
                v-for="(image, index) in generatedAnimation.user_media.images"
                :key="`user-img-${index}`"
                class="image-item"
              >
                <div class="image-container">
                  <img :src="image.url" :alt="`用户图片${index + 1}`" @error="handleImageError" />
                  <div class="image-overlay">
                    <div class="image-info">
                      <div class="scene-tag">用户图片 {{ index + 1 }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户视频 -->
          <div v-if="generatedAnimation.user_media.videos?.length > 0" class="user-videos">
            <h5>用户视频 ({{ generatedAnimation.user_media.videos.length }}个)</h5>
            <div class="videos-grid">
              <div
                v-for="(video, index) in generatedAnimation.user_media.videos"
                :key="`user-video-${index}`"
                class="video-item"
              >
                <div class="video-container">
                  <div class="video-player">
                    <video
                      :src="video.url"
                      controls
                      preload="metadata"
                      @error="handleVideoError"
                    >
                      您的浏览器不支持视频播放
                    </video>
                  </div>
                </div>
                <div class="video-details">
                  <div class="video-header">
                    <span class="scene-tag">用户视频 {{ index + 1 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI生成的视频展示 -->
        <div v-if="generatedAnimation.generated_media?.videos?.length > 0" class="aigc-videos-section">
          <h4>🎬 AI生成视频</h4>
          <div class="videos-grid">
            <div
              v-for="(video, index) in generatedAnimation.generated_media.videos"
              :key="index"
              class="video-item"
            >
              <div class="video-container">
                <!-- 已完成的视频 -->
                <div v-if="video.status === 'completed' && video.url" class="video-player">
                  <video
                    :src="video.url"
                    controls
                    preload="metadata"
                    @error="handleVideoError"
                  >
                    您的浏览器不支持视频播放
                  </video>
                </div>

                <!-- 处理中的视频 -->
                <div v-else-if="video.status === 'processing'" class="video-processing">
                  <div class="processing-content">
                    <el-progress
                      :percentage="video.progress || 0"
                      :stroke-width="8"
                      status="success"
                    />
                    <p>视频生成中... {{ video.estimated_time ? `预计${video.estimated_time}秒` : '' }}</p>
                    <p class="task-id">任务ID: {{ video.task_id }}</p>
                  </div>
                </div>

                <!-- 失败或等待的视频 -->
                <div v-else class="video-placeholder">
                  <div class="placeholder-content">
                    <i class="el-icon-video-camera"></i>
                    <p>{{ video.status === 'failed' ? '生成失败' : '等待生成' }}</p>
                  </div>
                </div>
              </div>

              <div class="video-details">
                <div class="video-header">
                  <span class="scene-tag">场景 {{ video.scene_number }}</span>
                  <span class="status-tag" :class="video.status">{{ getVideoStatusText(video.status) }}</span>
                </div>
                <p class="video-description">{{ video.description }}</p>
                <p class="video-prompt" v-if="video.prompt">提示词: {{ video.prompt }}</p>
                <div class="video-meta" v-if="video.platform">
                  <span class="platform">平台: {{ video.platform }}</span>
                  <span class="type" v-if="video.type">类型: {{ video.type }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 扩写内容 -->
        <div v-if="generatedAnimation.expanded_content" class="expanded-section">
          <h4>扩写内容</h4>
          <div class="expanded-box">
            <p>{{ generatedAnimation.expanded_content }}</p>
          </div>
        </div>

        <!-- 视频文案 -->
        <div v-if="generatedAnimation.video_caption" class="caption-section">
          <h4>视频文案</h4>
          <div class="caption-box">
            <p>{{ generatedAnimation.video_caption }}</p>
          </div>
        </div>

        <!-- 媒体使用建议 -->
        <div v-if="generatedAnimation.media_usage" class="media-section">
          <h4>媒体使用建议</h4>
          <div class="media-grid">
            <div
              v-for="media in generatedAnimation.media_usage"
              :key="media.usage_scene"
              class="media-item"
            >
              <div class="media-type">{{ media.media_type }}</div>
              <div class="media-scene">{{ media.usage_scene }}</div>
              <div class="media-effect">{{ media.effect }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, VideoCamera, VideoPlay, Download, Share, Picture, Magic, Timer } from '@element-plus/icons-vue'
import aiApi from '@/api/ai'
import axios from 'axios'

export default {
  name: 'TravelAnimationGenerator',
  emits: ['generated'],
  setup(props, { emit }) {
    const formRef = ref(null)
    const uploadFormRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedAnimation = ref(null)
    const articleOptions = ref([])
    const selectedArticle = ref(null)
    const activeTab = ref('existing')

    // 文件上传相关
    const imageFileList = ref([])
    const videoFileList = ref([])
    const uploadImageUrl = 'http://localhost:5000/api/upload/images'
    const uploadVideoUrl = 'http://localhost:5000/api/upload/videos'

    const form = reactive({
      article: null,
      animationStyle: '温馨',
      duration: '中等',
      focusElements: ['风景', '情感']
    })

    const uploadForm = reactive({
      title: '',
      content: '',
      location: '',
      images: [],
      videos: []
    })

    const rules = {
      article: [
        { required: true, message: '请选择一篇旅游日记', trigger: 'change' }
      ]
    }

    const uploadRules = {
      title: [
        { required: true, message: '请输入日记标题', trigger: 'blur' },
        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入日记内容', trigger: 'blur' },
        { min: 10, max: 2000, message: '内容长度在 10 到 2000 个字符', trigger: 'blur' }
      ],
      location: [
        { required: true, message: '请输入旅游地点', trigger: 'blur' }
      ]
    }

    // 搜索文章
    const searchArticles = async (query) => {
      searchLoading.value = true
      try {
        console.log('搜索文章，查询词:', query)

        // 获取当前用户信息
        let userInfo = null
        let userId = null

        // 尝试多种方式获取用户信息
        try {
          // 方式1：从currentUser获取
          const currentUserStr = localStorage.getItem('currentUser')
          if (currentUserStr) {
            userInfo = JSON.parse(currentUserStr)
            userId = userInfo.id || userInfo.user_id
          }

          // 方式2：直接从userId获取
          if (!userId) {
            userId = localStorage.getItem('userId')
          }

          // 方式3：从user获取（Vuex store）
          if (!userId) {
            const userStr = localStorage.getItem('user')
            if (userStr) {
              const user = JSON.parse(userStr)
              userId = user.id || user.user_id
            }
          }

          console.log('获取到的用户信息:', { userInfo, userId })

          if (!userId) {
            console.error('用户未登录或用户信息不完整')
            ElMessage.warning('请先登录后再使用此功能')
            return
          }
        } catch (e) {
          console.error('解析用户信息失败:', e)
          ElMessage.warning('用户信息异常，请重新登录')
          return
        }

        // 如果有查询词，进行模糊搜索
        if (query && query.trim()) {
          try {
            const response = await axios.get(`http://localhost:5000/api/articles/search`, {
              params: {
                query: query.trim(),
                user_id: userId,
                limit: 20
              }
            })

            console.log('搜索响应:', response.data)

            if (response.data && response.data.code === 0) {
              articleOptions.value = response.data.data || []
            } else {
              console.warn('搜索API返回错误:', response.data?.message)
              articleOptions.value = []
            }
          } catch (searchError) {
            console.error('模糊搜索失败，尝试获取用户所有文章:', searchError)
            // 如果模糊搜索失败，获取用户所有文章并在前端过滤
            await loadUserArticles(query)
          }
        } else {
          // 如果没有查询词，加载用户所有文章
          await loadUserArticles()
        }
      } catch (error) {
        console.error('搜索文章失败:', error)
        ElMessage.error('搜索失败，请稍后重试')
        articleOptions.value = []
      } finally {
        searchLoading.value = false
      }
    }

    // 加载用户文章
    const loadUserArticles = async (filterQuery = '') => {
      try {
        // 获取当前用户信息
        let userInfo = null
        let userId = null

        // 尝试多种方式获取用户信息
        try {
          // 方式1：从currentUser获取
          const currentUserStr = localStorage.getItem('currentUser')
          if (currentUserStr) {
            userInfo = JSON.parse(currentUserStr)
            userId = userInfo.id || userInfo.user_id
          }

          // 方式2：直接从userId获取
          if (!userId) {
            userId = localStorage.getItem('userId')
          }

          // 方式3：从user获取（Vuex store）
          if (!userId) {
            const userStr = localStorage.getItem('user')
            if (userStr) {
              const user = JSON.parse(userStr)
              userId = user.id || user.user_id
            }
          }

          console.log('loadUserArticles - 获取到的用户信息:', { userInfo, userId })

          if (!userId) {
            console.error('用户未登录或用户信息不完整')
            return
          }
        } catch (e) {
          console.error('解析用户信息失败:', e)
          return
        }

        console.log('加载用户文章，用户ID:', userId)

        const response = await axios.get(`http://localhost:5000/api/articles/user/${userId}`)
        console.log('用户文章响应:', response.data)

        if (response.data && response.data.code === 0) {
          let articles = response.data.data || []

          // 如果有过滤查询词，在前端进行过滤
          if (filterQuery && filterQuery.trim()) {
            const query = filterQuery.trim().toLowerCase()
            articles = articles.filter(article =>
              (article.title && article.title.toLowerCase().includes(query)) ||
              (article.location && article.location.toLowerCase().includes(query)) ||
              (article.content && article.content.toLowerCase().includes(query))
            )
          }

          // 转换数据格式，确保包含所需字段
          articleOptions.value = articles.map(article => ({
            article_id: article.article_id,
            title: article.title || '无标题',
            location: article.location || article.location_name || '未知地点',
            content: article.content || '',
            created_at: article.created_at,
            image_url: article.image_url,
            image_url_2: article.image_url_2,
            image_url_3: article.image_url_3,
            image_url_4: article.image_url_4,
            image_url_5: article.image_url_5,
            image_url_6: article.image_url_6,
            video_url: article.video_url,
            video_url_2: article.video_url_2,
            video_url_3: article.video_url_3
          }))

          console.log('处理后的文章列表:', articleOptions.value)
        } else {
          console.warn('获取用户文章失败:', response.data?.message)
          articleOptions.value = []
        }
      } catch (error) {
        console.error('加载用户文章失败:', error)
        articleOptions.value = []
      }
    }

    // 处理文章选择变化
    const handleArticleChange = (articleId) => {
      selectedArticle.value = articleOptions.value.find(
        article => article.article_id === articleId
      )
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 获取媒体文件数量
    const getMediaCount = () => {
      if (!selectedArticle.value) return { images: 0, videos: 0 }

      const article = selectedArticle.value
      let images = 0
      let videos = 0

      // 计算图片数量
      const imageFields = ['image_url', 'image_url_2', 'image_url_3', 'image_url_4', 'image_url_5', 'image_url_6']
      imageFields.forEach(field => {
        if (article[field]) images++
      })

      // 计算视频数量
      const videoFields = ['video_url', 'video_url_2', 'video_url_3']
      videoFields.forEach(field => {
        if (article[field]) videos++
      })

      return { images, videos }
    }

    // 获取加载文本
    const getLoadingText = () => {
      return loading.value ? '生成中...' : '生成动画'
    }

    // 生成动画脚本
    const generateAnimation = async () => {
      try {
        let requestData = {}

        if (activeTab.value === 'existing') {
          // 使用已有日记
          await formRef.value.validate()
          requestData = {
            article_id: form.article,
            animation_style: form.animationStyle,
            duration: form.duration,
            focus_elements: form.focusElements.join(', ')
          }
        } else {
          // 使用上传的新内容
          await uploadFormRef.value.validate()
          requestData = {
            title: uploadForm.title,
            content: uploadForm.content,
            location: uploadForm.location,
            images: imageFileList.value.map(file => file.response?.data?.url || file.url).filter(Boolean),
            videos: videoFileList.value.map(file => file.response?.data?.url || file.url).filter(Boolean),
            animation_style: form.animationStyle,
            duration: form.duration,
            focus_elements: form.focusElements.join(', '),
            use_doubao: true // 使用豆包大模型
          }
        }

        loading.value = true
        error.value = ''
        generatedAnimation.value = null

        console.log('发送AI动画生成请求:', requestData)

        try {
          const response = await aiApi.generateTravelAnimation(requestData)
          console.log('AI API响应:', response)

          if (response.code === 0 || response.data) {
            // 处理不同的响应格式
            let animationData = response.data?.animation || response.data

            console.log('收到的动画数据:', animationData)

            // 检查是否是新的文生图+本地合成格式
            if (animationData?.video_url && animationData?.ai_images) {
              // 这是新的文生图+本地合成格式
              generatedAnimation.value = {
                animation: {
                  id: animationData.id,
                  title: animationData.title,
                  status: animationData.status,
                  video_url: animationData.video_url,
                  player_url: animationData.video_url, // HTML播放器URL
                  config: {
                    total_duration: animationData.metadata?.total_duration || 30,
                    composition_type: animationData.metadata?.composition_type
                  }
                },
                generated_media: {
                  images: animationData.ai_images || [],
                  videos: []
                },
                user_media: animationData.user_media || { images: [], videos: [] },
                metadata: animationData.metadata || {}
              }

              emit('generated', generatedAnimation.value)

              const imageCount = animationData.ai_images?.length || 0
              const userImageCount = animationData.user_media?.images?.length || 0
              const userVideoCount = animationData.user_media?.videos?.length || 0

              ElMessage.success(`🎬 AIGC旅游动画生成成功！包含${imageCount}张AI配图、${userImageCount}张用户图片、${userVideoCount}个用户视频`)
            }
            // 检查是否是真正的AIGC动画结果（旧格式兼容）
            else if (animationData?.animation || animationData?.generated_media) {
              // 这是旧的AIGC动画格式
              generatedAnimation.value = animationData
              emit('generated', generatedAnimation.value)

              const imageCount = animationData.generated_media?.images?.length || 0
              const videoCount = animationData.generated_media?.videos?.length || 0

              if (animationData.animation?.player_url) {
                ElMessage.success(`🎬 AIGC动画生成成功！包含${imageCount}张AI图片和${videoCount}个AI视频，点击"播放动画"查看`)
              } else {
                ElMessage.success(`🎨 AIGC媒体生成成功！包含${imageCount}张AI图片和${videoCount}个AI视频`)
              }

              // 如果有视频生成任务，启动状态查询
              const videoTasks = animationData.generated_media?.videos?.filter(v => v.task_id && v.status === 'processing') || []
              if (videoTasks.length > 0) {
                startVideoStatusPolling(videoTasks)
              }
            } else {
              // 兼容旧的脚本格式
              if (animationData?.animation_config) {
                animationData = {
                  animation_script: {
                    total_duration: '3分钟',
                    style: form.animationStyle,
                    scenes: animationData.scenes || []
                  },
                  expanded_content: `基于您的旅游日记《${selectedArticle.value?.title}》生成的${form.animationStyle}风格动画脚本。`,
                  video_caption: `🎬 ${selectedArticle.value?.title} | ${form.animationStyle}风格旅游动画`,
                  media_usage: [
                    {
                      media_type: '图片',
                      usage_scene: '开场和转场',
                      effect: '营造氛围，引导情绪'
                    },
                    {
                      media_type: '视频',
                      usage_scene: '主要内容展示',
                      effect: '增强真实感和沉浸感'
                    }
                  ]
                }
              }

              generatedAnimation.value = animationData
              emit('generated', generatedAnimation.value)
              ElMessage.success('旅游动画脚本生成成功！')

              // 检查是否包含AI生成的媒体
              const hasAIMedia = animationData.generated_media &&
                                (animationData.generated_media.images?.length > 0 ||
                                 animationData.generated_media.videos?.length > 0)

              if (hasAIMedia) {
                const imageCount = animationData.generated_media.images?.length || 0
                const videoCount = animationData.generated_media.videos?.length || 0
                ElMessage.success(`🎨 同时生成了${imageCount}张AI图片和${videoCount}个AI视频`)

                // 如果有视频生成任务，启动状态查询
                const videoTasks = animationData.generated_media.videos?.filter(v => v.task_id) || []
                if (videoTasks.length > 0) {
                  startVideoStatusPolling(videoTasks.map(v => v.task_id))
                }
              }
            }
          } else {
            error.value = response.message || '生成失败'
            console.error('AI生成失败:', response)
            ElMessage.error(`生成失败: ${error.value}`)
          }
        } catch (apiError) {
          console.error('API调用失败:', apiError)

          // 检查是否是网络错误
          if (apiError.code === 'ECONNABORTED' || apiError.message.includes('timeout')) {
            error.value = 'AI生成超时，请稍后重试'
            ElMessage.error('AI生成超时，请稍后重试')
          } else if (apiError.response?.data?.message) {
            error.value = `生成失败: ${apiError.response.data.message}`
            ElMessage.error(error.value)
          } else {
            error.value = '生成失败，请稍后重试'
            ElMessage.error('生成失败，请稍后重试')
          }
        }
      } catch (error) {
        console.error('生成动画脚本失败:', error)
        console.error('错误详情:', error.response?.data)

        let errorMessage = '生成失败，请稍后重试'

        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          errorMessage = 'AI生成超时，请稍后重试'
        } else if (error.response?.data?.message) {
          errorMessage = `生成失败: ${error.response.data.message}`
        } else if (error.message) {
          if (error.message.includes('Network Error')) {
            errorMessage = '网络连接错误，请检查网络连接后重试'
          } else {
            errorMessage = `生成失败: ${error.message}`
          }
        }

        error.value = errorMessage
        ElMessage.error(errorMessage)
      } finally {
        loading.value = false
      }
    }

    // 生成模拟动画数据
    const generateMockAnimation = (article, formData) => {
      const scenes = []
      const sceneCount = formData.duration === '短片' ? 3 : formData.duration === '中等' ? 5 : 7

      for (let i = 1; i <= sceneCount; i++) {
        scenes.push({
          scene_number: i,
          duration: formData.duration === '短片' ? '15-20秒' : formData.duration === '中等' ? '30-40秒' : '45-60秒',
          description: `场景${i}：展现${article.location}的${formData.focusElements.join('和')}，营造${formData.animationStyle}的氛围。`,
          camera_angle: i % 2 === 1 ? '广角镜头' : '特写镜头',
          music_suggestion: formData.animationStyle === '温馨' ? '轻柔的钢琴曲' :
                           formData.animationStyle === '活泼' ? '欢快的民谣' :
                           formData.animationStyle === '文艺' ? '古典音乐' : '大气的交响乐'
        })
      }

      return {
        animation_script: {
          total_duration: formData.duration === '短片' ? '60秒' : formData.duration === '中等' ? '3分钟' : '5分钟',
          style: formData.animationStyle,
          scenes: scenes
        },
        expanded_content: `这是一个关于${article.title}的${formData.animationStyle}风格旅游动画。通过精心设计的${sceneCount}个场景，展现了${article.location}的独特魅力。动画重点突出${formData.focusElements.join('、')}等元素，为观众带来身临其境的旅游体验。`,
        video_caption: `🎬 ${article.title} | ${formData.animationStyle}风格旅游动画\n📍 ${article.location}\n✨ 一段${formData.duration}的视觉盛宴，带你领略旅途中的美好时光`,
        media_usage: [
          {
            media_type: '图片',
            usage_scene: '开场和转场',
            effect: '营造氛围，引导情绪'
          },
          {
            media_type: '视频',
            usage_scene: '主要内容展示',
            effect: '增强真实感和沉浸感'
          }
        ]
      }
    }

    // 视频状态查询
    const startVideoStatusPolling = (taskIds) => {
      console.log('开始查询视频生成状态:', taskIds)

      const pollInterval = setInterval(async () => {
        try {
          const response = await axios.post('/api/ai/query_video_status', {
            task_ids: taskIds
          })

          if (response.data.code === 0) {
            const results = response.data.data
            let allCompleted = true

            // 更新视频状态
            if (generatedAnimation.value?.generated_media?.videos) {
              generatedAnimation.value.generated_media.videos.forEach(video => {
                if (video.task_id && results[video.task_id]) {
                  const status = results[video.task_id]
                  video.status = status.status
                  video.progress = status.progress

                  if (status.status === 'completed' && status.video_url) {
                    video.url = status.video_url
                    ElMessage.success(`视频生成完成: 场景${video.scene_number}`)
                  } else if (status.status === 'processing') {
                    allCompleted = false
                  }
                }
              })
            }

            // 如果所有视频都完成了，停止轮询
            if (allCompleted) {
              clearInterval(pollInterval)
              ElMessage.success('🎬 所有AI视频生成完成！')
            }
          }
        } catch (error) {
          console.error('查询视频状态失败:', error)
          clearInterval(pollInterval)
        }
      }, 10000) // 每10秒查询一次

      // 5分钟后停止轮询
      setTimeout(() => {
        clearInterval(pollInterval)
      }, 300000)
    }

    // 处理图片加载错误
    const handleImageError = (event) => {
      console.error('图片加载失败:', event.target.src)
      event.target.src = 'https://via.placeholder.com/400x300/f0f0f0/999999?text=图片加载失败'
    }

    // 处理视频加载错误
    const handleVideoError = (event) => {
      console.error('视频加载失败:', event.target.src)
      ElMessage.warning('视频加载失败，请稍后重试')
    }

    // 获取视频状态文本
    const getVideoStatusText = (status) => {
      const statusMap = {
        'completed': '已完成',
        'processing': '生成中',
        'failed': '失败',
        'waiting': '等待中',
        'demo': '演示'
      }
      return statusMap[status] || '未知'
    }

    // 获取动画状态
    const getAnimationStatus = () => {
      if (!generatedAnimation.value?.animation) return '未知'
      return generatedAnimation.value.animation.status || 'completed'
    }

    // 获取动画状态文本
    const getAnimationStatusText = () => {
      const status = getAnimationStatus()
      const statusMap = {
        'completed': '已完成',
        'processing': '生成中',
        'failed': '生成失败',
        'pending': '等待中'
      }
      return statusMap[status] || '未知'
    }

    // 打开动画播放器
    const openAnimationPlayer = () => {
      if (!generatedAnimation.value?.animation?.player_url) {
        ElMessage.warning('动画播放器不可用')
        return
      }

      // 检查是否是HTML文件
      const playerUrl = generatedAnimation.value.animation.player_url

      if (playerUrl.endsWith('.html')) {
        // HTML幻灯片播放器
        const fullUrl = `http://localhost:5000${playerUrl}`
        window.open(fullUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
      } else {
        // 其他类型的播放器
        const fullUrl = `http://localhost:5000${playerUrl}`
        window.open(fullUrl, '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes')
      }
    }

    // 预览动画
    const previewAnimation = () => {
      if (!generatedAnimation.value?.animation?.preview_image) {
        ElMessage.warning('动画预览不可用')
        return
      }

      // 显示预览图片的大图
      const previewUrl = generatedAnimation.value.animation.preview_image
      window.open(previewUrl, '_blank')
    }

    // 获取视频类型
    const getVideoType = () => {
      if (!generatedAnimation.value?.animation?.video_url) return '未知'

      const videoUrl = generatedAnimation.value.animation.video_url
      if (videoUrl.endsWith('.mp4')) {
        return 'MP4视频'
      } else if (videoUrl.endsWith('.html')) {
        return 'HTML5播放器'
      } else {
        return '其他格式'
      }
    }

    // 下载视频
    const downloadVideo = () => {
      if (!generatedAnimation.value?.animation?.video_url) {
        ElMessage.warning('视频不可用')
        return
      }

      const videoUrl = `http://localhost:5000${generatedAnimation.value.animation.video_url}`
      const link = document.createElement('a')
      link.href = videoUrl
      link.download = `${selectedArticle.value?.title || '旅游动画'}.mp4`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success('开始下载视频')
    }

    // 分享视频
    const shareVideo = () => {
      if (!generatedAnimation.value?.animation?.video_url) {
        ElMessage.warning('视频不可用')
        return
      }

      const videoUrl = `http://localhost:5000${generatedAnimation.value.animation.video_url}`

      if (navigator.share) {
        navigator.share({
          title: `${selectedArticle.value?.title || '旅游动画'} - AIGC生成`,
          text: `查看我用AI生成的旅游动画：${selectedArticle.value?.title}`,
          url: videoUrl
        }).then(() => {
          ElMessage.success('分享成功')
        }).catch((error) => {
          console.error('分享失败:', error)
          copyVideoLink()
        })
      } else {
        copyVideoLink()
      }
    }

    // 复制视频链接
    const copyVideoLink = async () => {
      try {
        const videoUrl = `http://localhost:5000${generatedAnimation.value.animation.video_url}`
        await navigator.clipboard.writeText(videoUrl)
        ElMessage.success('视频链接已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        const animationText = formatAnimationForCopy(generatedAnimation.value, selectedArticle.value, form)
        await navigator.clipboard.writeText(animationText)
        ElMessage.success('动画脚本已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 格式化动画脚本用于复制
    const formatAnimationForCopy = (animation, article, formData) => {
      let text = `${article?.title || '旅游日记'} - 动画脚本\n\n`
      text += `风格: ${formData.animationStyle}\n`
      text += `时长: ${formData.duration}\n`
      text += `重点元素: ${formData.focusElements.join(', ')}\n\n`

      if (animation.animation_script) {
        text += `动画脚本:\n`
        text += `总时长: ${animation.animation_script.total_duration}\n`
        text += `场景数: ${animation.animation_script.scenes?.length || 0}个\n\n`

        if (animation.animation_script.scenes) {
          animation.animation_script.scenes.forEach(scene => {
            text += `场景 ${scene.scene_number} (${scene.duration}):\n`
            text += `描述: ${scene.description}\n`
            text += `镜头: ${scene.camera_angle}\n`
            text += `配乐: ${scene.music_suggestion}\n\n`
          })
        }
      }

      if (animation.expanded_content) {
        text += `扩写内容:\n${animation.expanded_content}\n\n`
      }

      if (animation.video_caption) {
        text += `视频文案:\n${animation.video_caption}\n`
      }

      return text
    }

    // 文件上传处理函数
    const beforeImageUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt10M) {
        ElMessage.error('图片大小不能超过 10MB!')
        return false
      }
      return true
    }

    const beforeVideoUpload = (file) => {
      const isVideo = file.type.startsWith('video/')
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isVideo) {
        ElMessage.error('只能上传视频文件!')
        return false
      }
      if (!isLt100M) {
        ElMessage.error('视频大小不能超过 100MB!')
        return false
      }
      return true
    }

    const handleImageSuccess = (response, file) => {
      if (response.code === 0) {
        ElMessage.success('图片上传成功')
        uploadForm.images.push(response.data.url)
      } else {
        ElMessage.error('图片上传失败')
      }
    }

    const handleVideoSuccess = (response, file) => {
      if (response.code === 0) {
        ElMessage.success('视频上传成功')
        uploadForm.videos.push(response.data.url)
      } else {
        ElMessage.error('视频上传失败')
      }
    }

    const handleImagePreview = (file) => {
      window.open(file.url, '_blank')
    }

    const handleVideoPreview = (file) => {
      window.open(file.url, '_blank')
    }

    const handleImageRemove = (file) => {
      const url = file.response?.data?.url || file.url
      const index = uploadForm.images.indexOf(url)
      if (index > -1) {
        uploadForm.images.splice(index, 1)
      }
      ElMessage.info('图片已移除')
    }

    const handleVideoRemove = (file) => {
      const url = file.response?.data?.url || file.url
      const index = uploadForm.videos.indexOf(url)
      if (index > -1) {
        uploadForm.videos.splice(index, 1)
      }
      ElMessage.info('视频已移除')
    }

    // 初始化时加载用户文章
    onMounted(async () => {
      console.log('TravelAnimationGenerator 组件挂载，开始加载用户文章')

      // 调试：打印所有localStorage中的用户相关信息
      console.log('localStorage 调试信息:')
      console.log('- currentUser:', localStorage.getItem('currentUser'))
      console.log('- userId:', localStorage.getItem('userId'))
      console.log('- user:', localStorage.getItem('user'))
      console.log('- authToken:', localStorage.getItem('authToken'))
      console.log('- token:', localStorage.getItem('token'))

      await loadUserArticles()

      console.log('加载完成，文章数量:', articleOptions.value.length)
    })

    // 播放动画
    const playAnimation = () => {
      ElMessage.info('🎬 动画播放功能演示')
    }

    // 下载动画
    const downloadAnimation = () => {
      ElMessage.success('📥 动画下载功能演示')
    }

    // 分享动画
    const shareAnimation = () => {
      ElMessage.success('📤 动画分享功能演示')
    }

    return {
      formRef,
      uploadFormRef,
      form,
      uploadForm,
      rules,
      uploadRules,
      loading,
      searchLoading,
      error,
      generatedAnimation,
      articleOptions,
      selectedArticle,
      activeTab,
      imageFileList,
      videoFileList,
      uploadImageUrl,
      uploadVideoUrl,
      searchArticles,
      loadUserArticles,
      handleArticleChange,
      formatDate,
      getMediaCount,
      getLoadingText,
      generateAnimation,
      generateMockAnimation,
      startVideoStatusPolling,
      handleImageError,
      handleVideoError,
      getVideoStatusText,
      getAnimationStatus,
      getAnimationStatusText,
      openAnimationPlayer,
      previewAnimation,
      getVideoType,
      downloadVideo,
      shareVideo,
      copyVideoLink,
      copyToClipboard,
      beforeImageUpload,
      beforeVideoUpload,
      handleImageSuccess,
      handleVideoSuccess,
      handleImagePreview,
      handleVideoPreview,
      handleImageRemove,
      handleVideoRemove,
      playAnimation,
      downloadAnimation,
      shareAnimation,
      Plus,
      VideoCamera,
      VideoPlay,
      Download,
      Share,
      Picture,
      Magic,
      Timer
    }
  }
}
</script>

<style scoped>
.travel-animation-generator {
  max-width: 900px;
  margin: 0 auto;
}

.generator-header {
  text-align: center;
  margin-bottom: 40px;
}

.generator-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.generator-header p {
  color: #666;
  font-size: 1rem;
}

.form-section {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.loading-tips {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  color: #1e40af;
  text-align: center;
}

.loading-tips p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.loading-tips p:first-child {
  font-weight: 600;
}

.article-preview {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.article-preview h3 {
  color: #333;
  margin-bottom: 20px;
}

.preview-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
}

.article-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.article-meta,
.media-count {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

.article-meta span,
.media-count span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.result-section {
  margin-top: 30px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e9ecef;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.animation-content {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
}

.animation-title h3 {
  color: #333;
  margin-bottom: 5px;
  font-size: 1.8rem;
}

.animation-meta {
  color: #666;
  font-size: 1rem;
  margin-bottom: 20px;
}

.script-section h4,
.expanded-section h4,
.caption-section h4,
.media-section h4,
.aigc-images-section h4,
.aigc-videos-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.script-overview {
  display: flex;
  gap: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-item .label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.overview-item .value {
  color: #333;
  font-weight: 600;
  font-size: 1.1rem;
}

.scenes-list {
  margin-bottom: 20px;
}

.scene-item {
  background: white;
  border-radius: 10px;
  margin-bottom: 15px;
  overflow: hidden;
  border-left: 4px solid #e6a23c;
}

.scene-header {
  background: #fdf6ec;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scene-number {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.scene-duration {
  color: #e6a23c;
  font-weight: 600;
  font-size: 0.9rem;
}

.scene-content {
  padding: 20px;
}

.scene-description h5 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1rem;
}

.scene-description p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.scene-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.expanded-box,
.caption-box {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #67c23a;
  margin-bottom: 20px;
}

.expanded-box p,
.caption-box p {
  margin: 0;
  line-height: 1.8;
  color: #333;
  font-size: 1rem;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.media-item {
  background: white;
  padding: 20px;
  border-radius: 10px;
  border-left: 3px solid #409eff;
}

.media-type {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.media-scene {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.6;
}

.media-effect {
  color: #409eff;
  font-size: 0.9rem;
  font-style: italic;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 15px;
  }

  .article-meta,
  .media-count {
    flex-direction: column;
    gap: 10px;
  }

  .script-overview {
    flex-direction: column;
    gap: 15px;
  }

  .scene-details {
    grid-template-columns: 1fr;
  }

  .media-grid {
    grid-template-columns: 1fr;
  }
}

/* AIGC媒体展示样式 */
.aigc-images-section,
.aigc-videos-section,
.user-media-section {
  margin-bottom: 30px;
}

.user-media-section h5 {
  color: #333;
  margin: 20px 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.user-images,
.user-videos {
  margin-bottom: 25px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.image-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.image-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-container:hover img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, transparent 50%, rgba(0, 0, 0, 0.3) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  gap: 8px;
}

.scene-tag,
.platform-tag {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.platform-tag {
  background: rgba(64, 158, 255, 0.9);
  color: white;
}

.image-details {
  padding: 15px;
}

.image-description {
  color: #333;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.4;
}

.image-prompt {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 20px;
}

.video-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.video-container {
  width: 100%;
  height: 250px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-processing {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.processing-content {
  text-align: center;
  padding: 20px;
}

.processing-content .el-progress {
  margin-bottom: 15px;
}

.processing-content p {
  margin: 8px 0;
  font-size: 0.9rem;
}

.task-id {
  font-family: monospace;
  font-size: 0.8rem !important;
  opacity: 0.8;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  color: #999;
}

.placeholder-content {
  text-align: center;
}

.placeholder-content i {
  font-size: 3rem;
  margin-bottom: 10px;
  display: block;
}

.video-details {
  padding: 15px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.status-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-tag.completed {
  background: #f0f9ff;
  color: #059669;
}

.status-tag.processing {
  background: #fef3c7;
  color: #d97706;
}

.status-tag.failed {
  background: #fee2e2;
  color: #dc2626;
}

.status-tag.demo {
  background: #f3e8ff;
  color: #7c3aed;
}

.video-description {
  color: #333;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.4;
}

.video-prompt {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 10px;
}

.video-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #999;
}

@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: 1fr;
  }

  .videos-grid {
    grid-template-columns: 1fr;
  }

  .video-container {
    height: 200px;
  }
}

/* 动画播放器样式 */
.animation-player-section {
  margin-bottom: 30px;
}

/* MP4视频播放器样式 */
.video-player-section {
  margin: 20px 0;
}

.video-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  background: #000;
}

.main-video {
  width: 100%;
  height: auto;
  display: block;
  max-height: 450px;
}

.video-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
}

/* HTML5播放器样式 */
.html-player-section {
  margin: 20px 0;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preview-placeholder:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.preview-placeholder p {
  margin-top: 10px;
  font-size: 1.1rem;
  font-weight: 500;
}

.animation-preview {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 20px auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.animation-preview:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.preview-image {
  width: 100%;
  height: auto;
  display: block;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.animation-preview:hover .preview-overlay {
  opacity: 1;
}

.animation-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  border-left: 4px solid #409eff;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item .label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.info-item .value {
  font-size: 1rem;
  color: #333;
  font-weight: 600;
}

.info-item .value.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  text-align: center;
}

.info-item .value.status.completed {
  background: #f0f9ff;
  color: #059669;
}

.info-item .value.status.processing {
  background: #fef3c7;
  color: #d97706;
}

.info-item .value.status.failed {
  background: #fee2e2;
  color: #dc2626;
}

/* 新增的动画预览样式 */
.animation-preview-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-header h4 {
  color: #333;
  margin: 0;
  font-size: 1.3rem;
}

.mock-video-player {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
}

.video-placeholder {
  height: 300px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.play-icon {
  margin-bottom: 15px;
  opacity: 0.9;
}

.video-placeholder p {
  margin: 5px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.video-specs {
  font-size: 0.9rem !important;
  opacity: 0.8;
  font-weight: normal !important;
}

.video-controls {
  padding: 20px;
  display: flex;
  gap: 15px;
  justify-content: center;
  background: white;
}

.enhanced-description {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  border-left: 4px solid #67c23a;
}

.enhanced-description h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.description-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.description-content p {
  margin: 0;
  line-height: 1.8;
  color: #333;
  font-size: 1rem;
}

.animation-features {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
}

.animation-features h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.feature-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-3px);
}

.feature-icon {
  font-size: 2rem;
  color: #409eff;
  margin-bottom: 10px;
}

.feature-card h5 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1rem;
}

.feature-card p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.video-placeholder-small {
  height: 120px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}

.video-placeholder-small p {
  margin: 5px 0 0 0;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .animation-info {
    grid-template-columns: 1fr;
  }

  .video-controls {
    flex-direction: column;
    gap: 10px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
