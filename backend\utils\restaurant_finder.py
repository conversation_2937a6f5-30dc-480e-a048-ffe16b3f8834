"""
餐馆查找工具类

提供高效的餐馆查找算法，包括：
1. 精准查找
2. 模糊查询
3. 基于内容的查找
"""
from typing import List, Dict, Any, Set, Tuple
import re
from models.restaurant import Restaurant


class RestaurantFinder:
    """餐馆查找工具类"""

    @staticmethod
    def exact_match(restaurants: List[Restaurant], field: str, value: Any) -> List[Restaurant]:
        """
        精准查找餐馆

        Args:
            restaurants: 餐馆列表
            field: 查找字段，例如 'name', 'cuisine_type'
            value: 查找值

        Returns:
            匹配的餐馆列表
        """
        result = []
        for restaurant in restaurants:
            if hasattr(restaurant, field) and getattr(restaurant, field) == value:
                result.append(restaurant)
        return result

    @staticmethod
    def fuzzy_search(restaurants: List[Restaurant], keyword: str) -> List[Restaurant]:
        """
        模糊查询餐馆

        Args:
            restaurants: 餐馆列表
            keyword: 查询关键字

        Returns:
            匹配的餐馆列表
        """
        if not keyword:
            return restaurants

        # 将关键字转换为小写，用于不区分大小写的匹配
        keyword_lower = keyword.lower()

        # 分词，以便更精确地匹配
        keywords = re.findall(r'\w+', keyword_lower)

        # 计算每个餐馆的匹配分数
        restaurant_scores = []
        for restaurant in restaurants:
            score = 0
            matched = False

            # 检查餐馆名称（完全匹配得分最高）
            restaurant_name = restaurant.name.lower() if restaurant.name else ""
            if keyword_lower == restaurant_name:
                score += 100  # 完全匹配餐馆名称，得分最高
                matched = True
            elif any(kw == restaurant_name for kw in keywords):
                score += 90  # 关键词完全匹配餐馆名称
                matched = True
            elif any(kw in restaurant_name.split() for kw in keywords):
                score += 80  # 关键词是餐馆名称的一个完整单词
                matched = True
            elif any(restaurant_name.startswith(kw) for kw in keywords):
                score += 70  # 关键词是餐馆名称的前缀
                matched = True
            elif any(kw in restaurant_name for kw in keywords):
                score += 50  # 关键词是餐馆名称的一部分
                matched = True

            # 检查菜系类型
            cuisine_type = restaurant.cuisine_type.lower() if restaurant.cuisine_type else ""
            if keyword_lower == cuisine_type:
                score += 80  # 完全匹配菜系类型
                matched = True
            elif any(kw == cuisine_type for kw in keywords):
                score += 70  # 关键词完全匹配菜系类型
                matched = True
            elif any(cuisine_type.startswith(kw) for kw in keywords):
                score += 60  # 关键词是菜系类型的前缀
                matched = True
            elif any(kw in cuisine_type for kw in keywords):
                score += 40  # 关键词是菜系类型的一部分
                matched = True

            # 检查菜品名称
            dishes = [
                restaurant.dishes_name_price,
                restaurant.dishes_name1_price,
                restaurant.dishes_name2_price
            ]

            for dish in dishes:
                dish_lower = dish.lower() if dish else ""
                if keyword_lower in dish_lower:
                    score += 30  # 关键词是菜品名称的一部分
                    matched = True
                    break

            # 只添加有匹配的餐馆
            if matched:
                restaurant_scores.append((restaurant, score))

        # 按分数降序排序
        restaurant_scores.sort(key=lambda x: x[1], reverse=True)

        # 返回排序后的餐馆列表
        return [r[0] for r in restaurant_scores]

    @staticmethod
    def boyer_moore_search(text: str, pattern: str) -> bool:
        """
        使用Boyer-Moore算法进行字符串匹配

        Args:
            text: 待搜索的文本
            pattern: 搜索模式

        Returns:
            是否匹配成功
        """
        if not pattern:
            return True

        if not text:
            return False

        # 将文本和模式转换为小写，用于不区分大小写的匹配
        text = text.lower()
        pattern = pattern.lower()

        n, m = len(text), len(pattern)

        if m > n:
            return False

        # 构建坏字符规则
        bad_char = {}
        for i in range(m - 1):
            bad_char[pattern[i]] = m - 1 - i

        # 模式串中最后一个字符的偏移量
        skip = bad_char.get(pattern[m - 1], m)
        bad_char[pattern[m - 1]] = skip

        # 搜索
        i = m - 1
        while i < n:
            j = m - 1
            k = i
            while j >= 0 and text[k] == pattern[j]:
                j -= 1
                k -= 1

            if j == -1:
                return True

            i += bad_char.get(text[i], m)

        return False

    @staticmethod
    def content_based_search(restaurants: List[Restaurant], keyword: str) -> List[Tuple[Restaurant, float]]:
        """
        基于内容的餐馆查找，返回餐馆及其相关度得分

        Args:
            restaurants: 餐馆列表
            keyword: 查询关键字

        Returns:
            匹配的餐馆列表及其相关度得分
        """
        if not keyword:
            return [(restaurant, 1.0) for restaurant in restaurants]

        # 将关键字转换为小写，用于不区分大小写的匹配
        keyword_lower = keyword.lower()

        # 分词
        keywords = re.findall(r'\w+', keyword_lower)

        result = []
        for restaurant in restaurants:
            score = 0.0
            max_score = len(keywords)  # 最大可能得分

            # 检查餐馆名称
            restaurant_name = restaurant.name.lower() if restaurant.name else ""
            for kw in keywords:
                if RestaurantFinder.boyer_moore_search(restaurant_name, kw):
                    score += 1.0

            # 检查菜系类型
            cuisine_type = restaurant.cuisine_type.lower() if restaurant.cuisine_type else ""
            for kw in keywords:
                if RestaurantFinder.boyer_moore_search(cuisine_type, kw):
                    score += 0.8  # 菜系匹配的权重略低于名称匹配

            # 检查菜品名称
            dishes = [
                restaurant.dishes_name_price,
                restaurant.dishes_name1_price,
                restaurant.dishes_name2_price
            ]

            for dish in dishes:
                dish_lower = dish.lower() if dish else ""
                for kw in keywords:
                    if RestaurantFinder.boyer_moore_search(dish_lower, kw):
                        score += 0.6  # 菜品匹配的权重更低

            # 计算相关度得分（归一化）
            relevance = min(score / max_score, 1.0) if max_score > 0 else 0.0

            # 只返回相关度大于0的结果
            if relevance > 0:
                result.append((restaurant, relevance))

        # 按相关度降序排序
        result.sort(key=lambda x: x[1], reverse=True)

        return result

    @staticmethod
    def filter_by_cuisine_type(restaurants: List[Restaurant], cuisine_types: List[str]) -> List[Restaurant]:
        """
        按菜系类型过滤餐馆

        Args:
            restaurants: 餐馆列表
            cuisine_types: 菜系类型列表

        Returns:
            过滤后的餐馆列表
        """
        if not cuisine_types:
            return restaurants

        # 将菜系类型转换为小写，用于不区分大小写的匹配
        cuisine_types_lower = [ct.lower() for ct in cuisine_types]

        result = []
        for restaurant in restaurants:
            if restaurant.cuisine_type and restaurant.cuisine_type.lower() in cuisine_types_lower:
                result.append(restaurant)

        return result

    @staticmethod
    def filter_by_price_range(restaurants: List[Restaurant], min_price: float = None, max_price: float = None) -> List[Restaurant]:
        """
        按价格范围过滤餐馆

        Args:
            restaurants: 餐馆列表
            min_price: 最低价格
            max_price: 最高价格

        Returns:
            过滤后的餐馆列表
        """
        result = []
        for restaurant in restaurants:
            price = restaurant.average_price_perperson

            if price is None:
                continue

            if min_price is not None and price < min_price:
                continue

            if max_price is not None and price > max_price:
                continue

            result.append(restaurant)

        return result
