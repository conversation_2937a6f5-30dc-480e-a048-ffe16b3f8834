<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h2 {
            color: #666;
            margin-top: 0;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #fff;
            border-radius: 3px;
            border: 1px solid #eee;
        }
        .result pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI生成模块搜索功能测试</h1>
        
        <div class="test-section">
            <h2>1. 服务器连接测试</h2>
            <button onclick="testServerConnection()">测试服务器连接</button>
            <div id="serverResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. 搜索API测试</h2>
            <input type="text" id="searchQuery" placeholder="输入搜索关键词" value="北京">
            <button onclick="testSearchAPI()">测试搜索API</button>
            <div id="searchResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. 预设测试</h2>
            <button onclick="testPreset('北京')">测试"北京"</button>
            <button onclick="testPreset('大学')">测试"大学"</button>
            <button onclick="testPreset('故宫')">测试"故宫"</button>
            <button onclick="testPreset('')">测试空查询</button>
            <div id="presetResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>4. 模拟前端调用</h2>
            <button onclick="simulateFrontendCall()">模拟前端API调用</button>
            <div id="frontendResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';

        function showResult(elementId, content, status = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `
                <div class="status ${status}">${status.toUpperCase()}</div>
                <div>${content}</div>
            `;
        }

        async function testServerConnection() {
            try {
                const response = await fetch(`${API_BASE}/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    showResult('serverResult', `服务器连接成功！状态码: ${response.status}`, 'success');
                } else {
                    showResult('serverResult', `服务器连接失败！状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('serverResult', `连接错误: ${error.message}`, 'error');
            }
        }

        async function testSearchAPI() {
            const query = document.getElementById('searchQuery').value;
            try {
                const url = `${API_BASE}/api/locations/fuzzy_search?query=${encodeURIComponent(query)}&limit=5`;
                console.log('请求URL:', url);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                console.log('响应状态:', response.status);
                const data = await response.json();
                console.log('响应数据:', data);
                
                if (response.ok && data.code === 0) {
                    const resultCount = data.data ? data.data.length : 0;
                    let content = `搜索成功！找到 ${resultCount} 个结果<br>`;
                    content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    showResult('searchResult', content, 'success');
                } else {
                    showResult('searchResult', `搜索失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('搜索错误:', error);
                showResult('searchResult', `搜索错误: ${error.message}`, 'error');
            }
        }

        async function testPreset(query) {
            try {
                const url = `${API_BASE}/api/locations/fuzzy_search?query=${encodeURIComponent(query)}&limit=3`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok && data.code === 0) {
                    const resultCount = data.data ? data.data.length : 0;
                    let content = `查询"${query}"成功！找到 ${resultCount} 个结果<br>`;
                    if (data.data && data.data.length > 0) {
                        content += '<strong>结果列表:</strong><br>';
                        data.data.forEach((item, index) => {
                            content += `${index + 1}. ${item.name} (ID: ${item.location_id})<br>`;
                        });
                    }
                    showResult('presetResult', content, 'success');
                } else {
                    showResult('presetResult', `查询"${query}"失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('presetResult', `查询"${query}"错误: ${error.message}`, 'error');
            }
        }

        async function simulateFrontendCall() {
            try {
                // 模拟前端的axios调用
                const response = await fetch(`${API_BASE}/api/locations/fuzzy_search?query=北京&limit=5`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    credentials: 'include'  // 模拟withCredentials: true
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    let content = '模拟前端调用成功！<br>';
                    content += `状态码: ${response.status}<br>`;
                    content += `响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}<br>`;
                    content += `响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>`;
                    showResult('frontendResult', content, 'success');
                } else {
                    showResult('frontendResult', `模拟前端调用失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('frontendResult', `模拟前端调用错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试服务器连接
        window.onload = function() {
            console.log('页面加载完成，开始测试...');
            testServerConnection();
        };
    </script>
</body>
</html>
