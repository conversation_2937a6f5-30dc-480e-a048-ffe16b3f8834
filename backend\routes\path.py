from flask import Blueprint, request, jsonify, current_app
from services.path_planning_service import PathPlanningService
from services.chaoyang_park_service import ChaoyangParkService
from models.path_planning import Vertex, Edge, Vertex2, Edge2

path_bp = Blueprint('path', __name__)

@path_bp.route('/plan', methods=['POST'])
@path_bp.route('/plan_multi', methods=['POST'])  # 添加多目的地路径规划路由
def plan_path():
    """
    Plan a path between two points or multiple destinations
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # 使用 'start_id' in data 检查参数是否存在，而不是检查值是否为真
        if 'start_id' not in data:
            return jsonify({'error': 'Missing start_id parameter'}), 400

        start_id = data.get('start_id')

        # Check if it's a single destination or multiple destinations
        # 支持多种参数名称，但要正确处理值为0的情况
        dest_id = None
        if 'dest_id' in data:
            dest_id = data.get('dest_id')
        elif 'end_id' in data:
            dest_id = data.get('end_id')

        dest_ids = []
        if 'dest_ids' in data:
            dest_ids = data.get('dest_ids')
        elif 'destinations' in data:
            dest_ids = data.get('destinations')

        # 确保 dest_ids 是列表
        if dest_ids and not isinstance(dest_ids, list):
            try:
                dest_ids = [int(dest_ids)]
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid destinations format'}), 400

        # 检查是否提供了目的地参数
        if dest_id is None and not dest_ids:
            return jsonify({'error': 'Missing destination parameter'}), 400

        # Get strategy (0: shortest distance, 1: shortest time, 2: rideable)
        strategy = data.get('strategy', 0)

        # Get algorithm (auto, held_karp, simulated_annealing, simple)
        algorithm = data.get('algorithm', 'auto')

        # Initialize service
        service = PathPlanningService()

        # Plan path
        if dest_id:
            # Single destination
            result = service.get_shortest_path(start_id, dest_id, strategy)

            # 添加顶点信息
            if 'path' in result and not 'error' in result:
                vertex_ids = result['path']
                vertices = Vertex.query.filter(Vertex.vertex_id.in_(vertex_ids)).all()
                result['vertexes'] = [vertex.to_dict() for vertex in vertices]

                # 确保响应格式
                if 'strategy' not in result:
                    result['strategy'] = strategy
        else:
            # Multiple destinations
            result = service.get_multi_destination_path(start_id, dest_ids, strategy, algorithm)

            # 添加顶点信息
            if 'path' in result and not 'error' in result:
                vertex_ids = result['path']
                vertices = Vertex.query.filter(Vertex.vertex_id.in_(vertex_ids)).all()
                result['vertexes'] = [vertex.to_dict() for vertex in vertices]

                # 确保响应格式
                if 'strategy' not in result:
                    result['strategy'] = strategy

        if 'error' in result:
            return jsonify(result), 400

        return jsonify(result), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@path_bp.route('/vertices', methods=['GET'])  # 小程序使用的路由
@path_bp.route('/vertexes', methods=['GET'])  # 添加额外的路由以匹配测试用例
def get_vertices():
    """
    Get all vertices
    """
    try:
        vertices = Vertex.query.all()
        return jsonify([vertex.to_dict() for vertex in vertices]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@path_bp.route('/edges', methods=['GET'])
def get_edges():
    """
    Get all edges
    """
    try:
        edges = Edge.query.all()
        return jsonify([edge.to_dict() for edge in edges]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@path_bp.route('/all-types', methods=['GET'])
def get_all_types():
    types = Vertex.query.all()
    result = [
        {"value": vertex_id, "label": t.label}
        for t in types
    ]
    return jsonify({"types": result})


@path_bp.route('/spots', methods=['GET', 'POST'])
def get_spots():
    current_app.logger.info("spots API被调用")
    print("spots API被调用")
    try:
        if request.method == 'POST':
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            location_name = data.get('location_name')
            start_vertex_id = data.get('start_vertex_id')
            x = data.get('x')
            y = data.get('y')
            distance = data.get('distance', 1000)
            type_val = data.get('type')
            keyword = data.get('keyword')
            name = data.get('name')
            limit = data.get('limit', 20)
            strategy = data.get('strategy', 0)
            print(f"POST请求参数: location_name={location_name}, start_vertex_id={start_vertex_id}, x={x}, y={y}, distance={distance}, type={type_val}, keyword={keyword}, name={name}, limit={limit}, strategy={strategy}")
        else:
            location_name = request.args.get('location_name')
            start_vertex_id = request.args.get('start_vertex_id')
            x = request.args.get('x')
            y = request.args.get('y')
            distance = request.args.get('distance', 1000)
            type_val = request.args.get('type')
            keyword = request.args.get('keyword')
            name = request.args.get('name')
            limit = request.args.get('limit', 20)
            strategy = request.args.get('strategy', 0)
            print(f"GET请求参数: location_name={location_name}, start_vertex_id={start_vertex_id}, x={x}, y={y}, distance={distance}, type={type_val}, keyword={keyword}, name={name}, limit={limit}, strategy={strategy}")
        start_vertex = None
        if start_vertex_id:
            try:
                start_vertex_id = int(start_vertex_id)
                start_vertex = Vertex.query.get(start_vertex_id)
                if not start_vertex:
                    return jsonify({'error': f'Vertex with ID {start_vertex_id} not found'}), 404
            except ValueError:
                return jsonify({'error': 'Invalid start_vertex_id format'}), 400
        elif location_name:
            start_vertex = Vertex.query.filter(Vertex.label.ilike(f'%{location_name}%')).first()
            if not start_vertex:
                return jsonify({'error': f'Location "{location_name}" not found'}), 404
            start_vertex_id = start_vertex.vertex_id
        elif x and y:
            try:
                x = int(x)
                y = int(y)
                all_vertices = Vertex.query.all()
                min_distance = float('inf')
                for vertex in all_vertices:
                    euclidean_dist = ((vertex.x - x) ** 2 + (vertex.y - y) ** 2) ** 0.5
                    if euclidean_dist < min_distance:
                        min_distance = euclidean_dist
                        start_vertex = vertex
                        start_vertex_id = vertex.vertex_id

                if not start_vertex:
                    return jsonify({'error': 'No vertices founsearch-by-named in database'}), 500
            except ValueError as e:
                return jsonify({'error': f'Invalid coordinate format: {str(e)}'}), 400
        else:
            return jsonify({'error': 'Must provide either start_vertex_id, location_name, or coordinates (x, y)'}), 400

        try:
            distance = int(distance)
            limit = int(limit)
            strategy = int(strategy)
        except ValueError as e:
            return jsonify({'error': f'Invalid parameter format: {str(e)}'}), 400

        print(f"起始顶点: ID={start_vertex_id}, 名称={start_vertex.label}, 坐标=({start_vertex.x}, {start_vertex.y})")
        query = Vertex.query
        if type_val is not None:
            if isinstance(type_val, str) and type_val.isdigit():
                type_val = int(type_val)
            if isinstance(type_val, int):
                print(f"按类型 {type_val} 过滤")
                query = query.filter(Vertex.type == type_val)
            else:
                print("使用默认类型范围过滤 (2-11)")
                query = query.filter(Vertex.type >= 2, Vertex.type <= 11)
        else:
            print("使用默认类型范围过滤 (2-11)")
            query = query.filter(Vertex.type >= 2, Vertex.type <= 11)

        # 名称过滤
        if name:
            print(f"按名称 '{name}' 模糊匹配")
            query = query.filter(Vertex.label.ilike(f'%{name}%'))
        elif keyword:
            print(f"按关键词 '{keyword}' 模糊匹配")
            query = query.filter(Vertex.label.ilike(f'%{keyword}%'))

        # 排除起始顶点本身
        query = query.filter(Vertex.vertex_id != start_vertex_id)

        candidate_vertices = query.all()
        print(f"查询到 {len(candidate_vertices)} 个候选顶点")

        if not candidate_vertices:
            return jsonify([]), 200

        # 使用最短路算法计算距离
        from services.path_planning_service import PathPlanningService
        path_service = PathPlanningService()

        spots = []

        for vertex in candidate_vertices:
            try:
                # 计算从起始顶点到候选顶点的最短路径
                result = path_service.get_shortest_path(start_vertex_id, vertex.vertex_id, strategy)

                if 'error' not in result and 'total_distance' in result:
                    path_distance = result['total_distance']

                    # 如果距离在限制范围内，添加到结果中
                    if path_distance <= distance:
                        spots.append({
                            'vertex_id': vertex.vertex_id,
                            'name': vertex.label,
                            'x': vertex.x,
                            'y': vertex.y,
                            'type': vertex.type,
                            'distance': path_distance,
                            'path_length': len(result.get('path', [])),
                            'strategy_used': strategy
                        })
                        print(f"顶点 {vertex.vertex_id} ({vertex.label}) 最短路径距离: {path_distance}")
                else:
                    # 如果无法计算路径，使用欧几里得距离作为备选
                    euclidean_dist = int(((vertex.x - start_vertex.x) ** 2 + (vertex.y - start_vertex.y) ** 2) ** 0.5)
                    if euclidean_dist <= distance:
                        spots.append({
                            'vertex_id': vertex.vertex_id,
                            'name': vertex.label,
                            'x': vertex.x,
                            'y': vertex.y,
                            'type': vertex.type,
                            'distance': euclidean_dist,
                            'path_length': 0,
                            'strategy_used': -1,  # 表示使用欧几里得距离
                            'note': 'No path found, using Euclidean distance'
                        })
                        print(f"顶点 {vertex.vertex_id} ({vertex.label}) 无路径，使用欧几里得距离: {euclidean_dist}")
            except Exception as e:
                print(f"计算到顶点 {vertex.vertex_id} 的距离时出错: {str(e)}")
                continue

        print(f"距离过滤后剩余 {len(spots)} 个顶点")
        spots.sort(key=lambda spot: spot['distance'])
        spots = spots[:limit]

        return jsonify(spots), 200
    except Exception as e:
        import traceback
        print(f"处理请求时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/location-suggestions', methods=['GET'])
def get_location_suggestions():
    """
    Get location name suggestions for autocomplete
    支持前端输入框的模糊查询和自动补全
    """
    try:
        query_text = request.args.get('q', '').strip()
        limit = request.args.get('limit', 10, type=int)
        type_filter = request.args.get('type', type=int)

        print(f"地点建议API被调用: query='{query_text}', limit={limit}, type={type_filter}")

        # 使用新的附近景点服务
        from services.nearby_spots_service import NearbySpotService
        nearby_service = NearbySpotService()

        suggestions = nearby_service.get_location_suggestions(query_text, limit, type_filter)

        print(f"返回 {len(suggestions)} 个地点建议")
        return jsonify(suggestions), 200

    except Exception as e:
        import traceback
        print(f"获取地点建议时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/spots-by-start', methods=['POST'])
def get_spots_by_path():
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # 必需参数
        location_name = data.get('location_name')
        start_vertex_id = data.get('start_vertex_id')

        # 可选参数
        max_distance = data.get('distance', 500)  # 最大距离
        spot_type = data.get('type')  # 景点类型
        limit = data.get('limit', 215)  # 返回结果数量
        strategy = data.get('strategy', 0)  # 路径策略
        print(f"附近景点查询API被调用: location_name={location_name}, start_vertex_id={start_vertex_id}, max_distance={max_distance}, type={spot_type}, limit={limit}, strategy={strategy}")
 
        from services.nearby_spots_service import NearbySpotService
        nearby_service = NearbySpotService()
 
        if spot_type:
            result = nearby_service.find_nearby_spots_by_vertex_id(
                start_vertex_id, max_distance, 
                spot_type=int(spot_type),  # 确保类型转换为整数
                limit=limit, 
                strategy=strategy
            )
        else:
            result = nearby_service.find_nearby_spots_by_vertex_id(
                start_vertex_id, max_distance,
                spot_type=None,  # 明确传递空值
                limit=limit,
                strategy=strategy
            )
        if start_vertex_id:
            try:
                start_vertex_id = int(start_vertex_id)
                result = nearby_service.find_nearby_spots_by_vertex_id(
                    start_vertex_id, max_distance, spot_type, limit, strategy
                )
            except ValueError:
                return jsonify({'error': 'Invalid start_vertex_id format'}), 400

        elif location_name:
            result = nearby_service.find_nearby_spots_by_name(
                location_name, max_distance, spot_type, limit, strategy
            )
        else:
            return jsonify({'error': 'Must provide either start_vertex_id or location_name'}), 400

        # 检查是否有错误
        if 'error' in result:
            return jsonify(result), 404

        print(f"返回 {result['total_found']} 个附近景点")
        
        print(f"接收参数: distance={max_distance}, limit={limit}")
        return jsonify(result), 200

    except Exception as e:
        import traceback
        print(f"查询附近景点时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/spots-by-criteria', methods=['POST'])
def get_spots_by_criteria():
    """
    Get spots by multiple criteria
    根据多个条件查询景点
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        start_vertex_id = data.get('start_vertex_id')
        location_name = data.get('location_name')

        if not start_vertex_id and not location_name:
            return jsonify({'error': 'Must provide either start_vertex_id or location_name'}), 400

        # 新增精确匹配参数
        exact_match = data.get('exact_match', False)

        # 构建查询条件
        criteria = {
            'max_distance': data.get('distance', 500),
            'spot_types': data.get('types', []),
            'name_keywords': data.get('keywords', []),
            'limit': data.get('limit', 15),
            'strategy': data.get('strategy', 0),
            'exact_match': exact_match  # 添加精确匹配标志
        }

        print(f"多条件景点查询API被调用: start_vertex_id={start_vertex_id}, exact_match={exact_match}, criteria={criteria}")

        # 使用附近景点服务
        from services.nearby_spots_service import NearbySpotService
        nearby_service = NearbySpotService()

        # 确定起始顶点ID
        if start_vertex_id:
            try:
                start_vertex_id = int(start_vertex_id)
            except ValueError:
                return jsonify({'error': 'Invalid start_vertex_id format'}), 400
        else:
            # 修改为精确匹配查询起始点
            query = Vertex.query.filter(Vertex.label == location_name)
            if not exact_match:
                query = query.filter(Vertex.label.ilike(f'%{location_name}%'))
                
            start_vertex = query.first()
            if not start_vertex:
                return jsonify({'error': f'Location "{location_name}" not found'}), 404
            start_vertex_id = start_vertex.vertex_id

        # 执行查询（需确保服务支持exact_match参数）
        result = nearby_service.find_spots_by_multiple_criteria(start_vertex_id, criteria)

        # 添加精确匹配过滤
        if exact_match and 'nearby_spots' in result:
            result['nearby_spots'] = [spot for spot in result['nearby_spots'] 
                                    if spot['name'] == criteria['name_keywords'][0]]
            result['total_found'] = len(result['nearby_spots'])

        if 'error' in result:
            return jsonify(result), 404

        print(f"返回 {result['total_found']} 个精确匹配景点")
        return jsonify(result), 200
    except Exception as e:
        import traceback
        print(f"多条件查询失败: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500


@path_bp.route('/search-by-name', methods=['GET'])
def search_spots_by_name():
    """
    Search spots by name without distance limitation but with distance calculation
    """
    try:
        name = request.args.get('name')
        type_val = request.args.get('type')
        x = request.args.get('x')
        y = request.args.get('y')

        if not name:
            return jsonify({'error': 'Missing name parameter'}), 400

        print(f"按名称搜索场所API被调用: name={name}, type={type_val}, x={x}, y={y}")

        # 构建查询
        query = Vertex.query

        # 类型过滤
        if type_val is not None:
            if isinstance(type_val, str) and type_val.isdigit():
                type_val = int(type_val)
            if isinstance(type_val, int):
                query = query.filter(Vertex.type == type_val)
            else:
                query = query.filter(Vertex.type >= 2, Vertex.type <= 11)
        else:
            query = query.filter(Vertex.type >= 2, Vertex.type <= 11)

        # 名称过滤 - 使用不区分大小写的模糊匹配
        # 打印所有顶点的名称，用于调试
        all_vertices = Vertex.query.all()
        print(f"数据库中共有 {len(all_vertices)} 个顶点")

        # 打印包含搜索词的顶点
        matching_vertices = [v for v in all_vertices if name.lower() in v.label.lower()]
        print(f"包含 '{name}' 的顶点有 {len(matching_vertices)} 个:")
        for v in matching_vertices:
            print(f"  - ID: {v.vertex_id}, 名称: {v.label}, 类型: {v.type}")

        # 使用不区分大小写的模糊匹配
        query = query.filter(Vertex.label.ilike(f'%{name}%'))

        # 执行查询
        vertices = query.all()
        print(f"查询到 {len(vertices)} 个符合条件的顶点")

        # 打印查询结果
        for v in vertices:
            print(f"  - 查询结果: ID: {v.vertex_id}, 名称: {v.label}, 类型: {v.type}")

        # 转换为响应格式，计算距离
        spots = []

        # 如果提供了坐标，计算距离
        if x and y:
            try:
                x = int(x)
                y = int(y)

                for vertex in vertices:
                    # 计算欧几里得距离
                    vertex_distance = int(((vertex.x - x) ** 2 + (vertex.y - y) ** 2) ** 0.5)

                    spots.append({
                        'vertex_id': vertex.vertex_id,
                        'name': vertex.label,
                        'x': vertex.x,
                        'y': vertex.y,
                        'type': vertex.type,
                        'distance': vertex_distance  # 计算实际距离
                    })

                # 按距离排序
                spots.sort(key=lambda spot: spot['distance'])
            except ValueError:
                print(f"坐标转换错误: x={x}, y={y}")
                # 如果坐标转换失败，不计算距离
                for vertex in vertices:
                    spots.append({
                        'vertex_id': vertex.vertex_id,
                        'name': vertex.label,
                        'x': vertex.x,
                        'y': vertex.y,
                        'type': vertex.type,
                        'distance': 0  # 不计算距离
                    })
        else:
            # 如果没有提供坐标，不计算距离
            for vertex in vertices:
                spots.append({
                    'vertex_id': vertex.vertex_id,
                    'name': vertex.label,
                    'x': vertex.x,
                    'y': vertex.y,
                    'type': vertex.type,
                    'distance': 0  # 不计算距离
                })

        return jsonify(spots), 200
    except Exception as e:
        import traceback
        print(f"处理请求时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500
 
@path_bp.route('/all-vertices', methods=['GET'])
def get_all_vertices():
    """
    Get all vertices without filtering
    """
    try:
        print("获取所有顶点API被调用")

        # 检查数据库中是否有顶点数据
        vertex_count = Vertex.query.count()
        print(f"数据库中顶点总数: {vertex_count}")

        if vertex_count == 0:
            return jsonify({'error': 'No vertices in database'}), 500

        # 限制返回数量以避免响应过大
        limit = min(int(request.args.get('limit', 100)), 1000)
        vertices = Vertex.query.limit(limit).all()

        result = []
        for vertex in vertices:
            result.append({
                'vertex_id': vertex.vertex_id,
                'name': vertex.label,
                'x': vertex.x,
                'y': vertex.y,
                'type': vertex.type
            })

        print(f"返回 {len(result)} 个顶点")
        return jsonify({
            'total': vertex_count,
            'returned': len(result),
            'vertices': result
        }), 200
    except Exception as e:
        import traceback
        print(f"处理请求时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/vertex/<int:vertex_id>', methods=['GET'])
def get_vertex_by_id(vertex_id):
    """
    Get vertex information by ID

    Args:
        vertex_id: The ID of the vertex to retrieve

    Returns:
        Vertex information in JSON format
    """
    try:
        print(f"获取顶点信息API被调用，顶点ID: {vertex_id}")

        # 查询指定ID的顶点
        vertex = Vertex.query.get(vertex_id)

        # 如果顶点不存在，返回404错误
        if not vertex:
            print(f"顶点ID {vertex_id} 不存在")
            return jsonify({'error': f'Vertex with ID {vertex_id} not found'}), 404

        # 将顶点信息转换为字典
        vertex_info = vertex.to_dict()

        # 添加额外信息：相邻的顶点
        # 获取所有以该顶点为起点的边
        outgoing_edges = Edge.query.filter_by(src_id=vertex_id).all()
        # 获取所有以该顶点为终点的边
        incoming_edges = Edge.query.filter_by(dest_id=vertex_id).all()

        # 收集相邻顶点的ID
        connected_vertices = set()
        for edge in outgoing_edges:
            connected_vertices.add(edge.dest_id)
        for edge in incoming_edges:
            connected_vertices.add(edge.src_id)

        # 将相邻顶点的ID添加到结果中
        vertex_info['connected_vertices'] = list(connected_vertices)

        # 添加出边和入边信息
        vertex_info['outgoing_edges'] = [edge.to_dict() for edge in outgoing_edges]
        vertex_info['incoming_edges'] = [edge.to_dict() for edge in incoming_edges]

        print(f"成功获取顶点ID {vertex_id} 的信息")
        return jsonify(vertex_info), 200

    except Exception as e:
        import traceback
        print(f"处理请求时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/vertices-by-ids', methods=['GET', 'POST'])
def get_vertices_by_ids():
    """
    Get information for multiple vertices by their IDs

    GET method: Use query parameter 'ids' with comma-separated vertex IDs
    POST method: Send a JSON object with an 'ids' array of vertex IDs

    Returns:
        Dictionary with vertex IDs as keys and vertex information as values
    """
    try:
        # 获取顶点ID列表
        vertex_ids = []

        if request.method == 'GET':
            # 从查询参数中获取ID列表
            ids_param = request.args.get('ids', '')
            if ids_param:
                try:
                    # 将逗号分隔的ID字符串转换为整数列表
                    vertex_ids = [int(id_str) for id_str in ids_param.split(',') if id_str.strip()]
                except ValueError:
                    return jsonify({'error': 'Invalid vertex ID format in query parameter'}), 400
        else:  # POST
            # 从请求体中获取ID列表
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            ids_array = data.get('ids', [])
            if not ids_array:
                return jsonify({'error': 'Missing ids parameter'}), 400

            if not isinstance(ids_array, list):
                return jsonify({'error': 'ids parameter must be an array'}), 400

            vertex_ids = ids_array

        print(f"批量获取顶点信息API被调用，顶点ID列表: {vertex_ids}")

        if not vertex_ids:
            return jsonify({'error': 'No vertex IDs provided'}), 400

        # 查询所有指定ID的顶点
        vertices = Vertex.query.filter(Vertex.vertex_id.in_(vertex_ids)).all()

        # 创建ID到顶点的映射
        vertex_map = {vertex.vertex_id: vertex for vertex in vertices}

        # 查询所有相关的边
        all_edges = Edge.query.filter(
            (Edge.src_id.in_(vertex_ids)) | (Edge.dest_id.in_(vertex_ids))
        ).all()

        # 创建顶点ID到出边和入边的映射
        outgoing_edges_map = {id: [] for id in vertex_ids}
        incoming_edges_map = {id: [] for id in vertex_ids}

        for edge in all_edges:
            if edge.src_id in vertex_ids:
                outgoing_edges_map[edge.src_id].append(edge)
            if edge.dest_id in vertex_ids:
                incoming_edges_map[edge.dest_id].append(edge)

        # 构建结果
        result = {}
        for vertex_id in vertex_ids:
            if vertex_id in vertex_map:
                vertex = vertex_map[vertex_id]
                vertex_info = vertex.to_dict()

                # 添加相邻顶点信息
                outgoing = outgoing_edges_map.get(vertex_id, [])
                incoming = incoming_edges_map.get(vertex_id, [])

                # 收集相邻顶点的ID
                connected_vertices = set()
                for edge in outgoing:
                    connected_vertices.add(edge.dest_id)
                for edge in incoming:
                    connected_vertices.add(edge.src_id)

                # 将相邻顶点的ID添加到结果中
                vertex_info['connected_vertices'] = list(connected_vertices)

                # 添加出边和入边信息
                vertex_info['outgoing_edges'] = [edge.to_dict() for edge in outgoing]
                vertex_info['incoming_edges'] = [edge.to_dict() for edge in incoming]

                result[str(vertex_id)] = vertex_info
            else:
                # 顶点不存在，添加错误信息
                result[str(vertex_id)] = {'error': f'Vertex with ID {vertex_id} not found'}

        print(f"成功获取 {len(result)} 个顶点的信息")
        return jsonify(result), 200

    except Exception as e:
        import traceback
        print(f"处理请求时发生错误: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

 
# ===== 朝阳公园相关API (vertexes2) =====

@path_bp.route('/chaoyang-park/vertices', methods=['GET'])
def get_chaoyang_park_vertices():
    """
    获取朝阳公园所有地点
    """
    try:
        print("开始查询朝阳公园地点数据...")
        vertices = Vertex2.query.all()
        print(f"查询到 {len(vertices)} 个地点")

        result = []
        for vertex in vertices:
            try:
                # 检查vertex是否为None
                if vertex is None:
                    print("发现None地点记录，跳过")
                    continue

                vertex_dict = vertex.to_dict()
                result.append(vertex_dict)
            except Exception as ve:
                vertex_id = getattr(vertex, 'vertex_id', 'Unknown') if vertex else 'None'
                print(f"转换地点数据失败: {vertex_id}, 错误: {str(ve)}")
                continue

        print(f"成功转换 {len(result)} 个地点数据")
        return jsonify(result), 200
    except Exception as e:
        print(f"获取朝阳公园地点数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/chaoyang-park/vertices/by-type', methods=['GET'])
def get_chaoyang_park_vertices_by_type():
    """
    按类型获取朝阳公园地点

    Query Parameters:
        type: 地点类型 (可选)
    """
    try:
        location_type = request.args.get('type')
        print(f"按类型筛选朝阳公园地点: {location_type}")

        query = Vertex2.query
        if location_type:
            query = query.filter(Vertex2.type == location_type)

        vertices = query.all()
        print(f"筛选到 {len(vertices)} 个地点")

        result = []
        for vertex in vertices:
            try:
                # 检查vertex是否为None
                if vertex is None:
                    print("发现None地点记录，跳过")
                    continue

                vertex_dict = vertex.to_dict()
                result.append(vertex_dict)
            except Exception as ve:
                vertex_id = getattr(vertex, 'vertex_id', 'Unknown') if vertex else 'None'
                print(f"转换地点数据失败: {vertex_id}, 错误: {str(ve)}")
                continue

        print(f"成功转换 {len(result)} 个地点数据")
        return jsonify(result), 200
    except Exception as e:
        print(f"按类型获取朝阳公园地点失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/chaoyang-park/types', methods=['GET'])
def get_chaoyang_park_types():
    """
    获取朝阳公园所有地点类型
    """
    try:
        print("开始查询朝阳公园地点类型...")
        # 获取所有不同的地点类型
        types = Vertex2.query.with_entities(Vertex2.type).distinct().all()
        print(f"查询到 {len(types)} 个不同类型")

        type_list = [t.type for t in types if t.type]  # 过滤掉空值
        print(f"有效类型: {type_list}")

        return jsonify({'types': sorted(type_list)}), 200
    except Exception as e:
        print(f"获取朝阳公园地点类型失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@path_bp.route('/chaoyang-park/vertex/<int:vertex_id>', methods=['GET'])
def get_chaoyang_park_vertex(vertex_id):
    """
    获取朝阳公园指定地点信息
    """
    try:
        vertex = Vertex2.query.get(vertex_id)
        if not vertex:
            return jsonify({'error': f'Vertex with ID {vertex_id} not found'}), 404

        return jsonify(vertex.to_dict()), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


# 朝阳公园路径规划API
@path_bp.route('/chaoyang-park/plan', methods=['POST'])
def plan_chaoyang_park_path():
    """
    朝阳公园路径规划
    支持三种策略：最短距离、最短时间、智能出行
    支持三种出行方式：不限、步行、电瓶车
    """
    try:
        data = request.get_json()
        print(f"收到朝阳公园路径规划请求: {data}")

        # 验证必需字段
        if not data:
            return jsonify({'error': '未提供数据'}), 400

        if 'start_id' not in data:
            return jsonify({'error': '缺少起点参数 start_id'}), 400

        start_id = data.get('start_id')

        # 检查是单目标还是多目标
        dest_id = data.get('dest_id') or data.get('end_id')
        dest_ids = data.get('dest_ids') or data.get('destinations', [])

        # 确保 dest_ids 是列表
        if dest_ids and not isinstance(dest_ids, list):
            try:
                dest_ids = [int(dest_ids)]
            except (ValueError, TypeError):
                return jsonify({'error': '目标点格式无效'}), 400

        # 检查是否提供了目标点
        if dest_id is None and not dest_ids:
            return jsonify({'error': '缺少目标点参数'}), 400

        # 获取策略 (0: 最短距离, 1: 最短时间, 3: 智能出行)
        strategy = data.get('strategy', 0)

        # 获取出行方式 ('driving': 不限, 'walking': 步行, 'riding': 电瓶车)
        transport_mode = data.get('transport_mode', 'driving')

        # 获取算法选择
        algorithm = data.get('algorithm', 'auto')

        print(f"路径规划参数: start_id={start_id}, dest_id={dest_id}, dest_ids={dest_ids}, strategy={strategy}, transport_mode={transport_mode}")

        # 初始化朝阳公园服务
        service = ChaoyangParkService()

        # 规划路径
        if dest_id is not None:
            # 单目标路径规划
            result = service.get_shortest_path(start_id, dest_id, strategy, transport_mode)
        else:
            # 多目标路径规划
            result = service.get_multi_destination_path(start_id, dest_ids, strategy, transport_mode, algorithm)

        # 添加顶点信息
        if 'path' in result and 'error' not in result:
            vertex_ids = result['path']
            vertices = Vertex2.query.filter(Vertex2.vertex_id.in_(vertex_ids)).all()
            result['vertexes'] = [vertex.to_dict() for vertex in vertices]

        if 'error' in result:
            return jsonify(result), 400

        return jsonify(result), 200

    except Exception as e:
        print(f"朝阳公园路径规划错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500


@path_bp.route('/chaoyang-park/plan-multi', methods=['POST'])
def plan_chaoyang_park_multi_path():
    """
    朝阳公园多目标路径规划
    """
    try:
        data = request.get_json()
        print(f"收到朝阳公园多目标路径规划请求: {data}")

        # 验证必需字段
        if not data:
            return jsonify({'error': '未提供数据'}), 400

        start_id = data.get('start_id')
        dest_ids = data.get('dest_ids') or data.get('destinations', [])

        if start_id is None:
            return jsonify({'error': '缺少起点参数 start_id'}), 400

        if not dest_ids:
            return jsonify({'error': '缺少目标点列表 dest_ids'}), 400

        # 确保 dest_ids 是列表
        if not isinstance(dest_ids, list):
            return jsonify({'error': '目标点必须是列表格式'}), 400

        # 获取参数
        strategy = data.get('strategy', 0)
        transport_mode = data.get('transport_mode', 'driving')
        algorithm = data.get('algorithm', 'auto')

        print(f"多目标路径规划参数: start_id={start_id}, dest_ids={dest_ids}, strategy={strategy}, transport_mode={transport_mode}")

        # 初始化朝阳公园服务
        service = ChaoyangParkService()

        # 规划多目标路径
        result = service.get_multi_destination_path(start_id, dest_ids, strategy, transport_mode, algorithm)

        # 添加顶点信息
        if 'path' in result and 'error' not in result:
            vertex_ids = result['path']
            vertices = Vertex2.query.filter(Vertex2.vertex_id.in_(vertex_ids)).all()
            result['vertexes'] = [vertex.to_dict() for vertex in vertices]

        if 'error' in result:
            return jsonify(result), 400

        return jsonify(result), 200

    except Exception as e:
        print(f"朝阳公园多目标路径规划错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500


@path_bp.route('/chaoyang-park/scooter-line/<int:line_number>', methods=['GET'])
def get_chaoyang_park_scooter_line(line_number):
    """
    获取朝阳公园电瓶车线路数据
    """
    try:
        print(f"获取电瓶车{line_number}线数据...")

        if line_number not in [1, 2]:
            return jsonify({'error': '无效的线路号，只支持1或2'}), 400

        # 获取指定线路的所有边
        edges = Edge2.query.filter_by(is_car=line_number).all()
        print(f"找到电瓶车{line_number}线边数: {len(edges)}")

        if not edges:
            return jsonify({'error': f'未找到电瓶车{line_number}线数据'}), 404

        # 获取所有相关的顶点ID
        vertex_ids = set()
        for edge in edges:
            vertex_ids.add(edge.src_id)
            vertex_ids.add(edge.des_id)

        # 获取顶点信息
        vertices = Vertex2.query.filter(Vertex2.vertex_id.in_(vertex_ids)).all()
        print(f"找到电瓶车{line_number}线站点数: {len(vertices)}")

        # 构建返回数据
        edge_data = []
        for edge in edges:
            edge_data.append({
                'src_id': edge.src_id,
                'des_id': edge.des_id,
                'weight': edge.weight,
                'crowding': edge.crowding if edge.crowding is not None else 1.0
            })

        station_data = []
        for vertex in vertices:
            station_data.append({
                'vertex_id': vertex.vertex_id,
                'label': vertex.label,
                'x': vertex.x,
                'y': vertex.y,
                'type': vertex.type
            })

        result = {
            'line_number': line_number,
            'edges': edge_data,
            'stations': station_data
        }

        print(f"电瓶车{line_number}线数据获取成功")
        return jsonify(result), 200

    except Exception as e:
        print(f"获取电瓶车{line_number}线数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500
