<template>
  <div class="route-planner">
    <!-- 地图容器 -->
    <div id="chaoyang-map-container" class="map-container"></div>

    <!-- 优化后的控制面板 -->
    <div class="planner-panel">
      <div class="panel-header">
        <h2>北京朝阳公园导航</h2>
        <div class="panel-toggle" @click="togglePanel">
          <span class="toggle-icon">▶</span>
        </div>
      </div>

      <div class="panel-content">
        <!-- 地点类型筛选区域 -->
        <div class="location-type-filter">
          <div class="filter-title">地点类型筛选：</div>
          <div class="filter-controls">
            <div class="type-selector">
              <select v-model="selectedLocationType" @change="onLocationTypeChange" class="type-select">
                <option value="">全部类型</option>
                <option v-for="type in locationTypes" :key="type" :value="type">
                  {{ type }}
                </option>
              </select>
            </div>
            <div class="filter-actions">
              <button @click="showLocationMarkers" class="action-btn show-markers-btn" :disabled="!selectedLocationType">
                显示地点
              </button>
              <button @click="hideLocationMarkers" class="action-btn hide-markers-btn">
                隐藏地点
              </button>
            </div>
          </div>
          <div v-if="filteredLocationsByType.length > 0" class="filtered-locations-info">
            找到 {{ filteredLocationsByType.length }} 个{{ selectedLocationType }}类型的地点
          </div>
        </div>

        <!-- 地点选择区域 -->
        <div class="location-selector">
          <div class="selector-title">选择地点：</div>

          <!-- 起点选择 -->
          <div class="location-item">
            <div class="location-label">起点：</div>
            <div class="location-input">
              <div class="autocomplete-container">
                <input
                  type="text"
                  v-model="startSearchText"
                  @input="filterStartLocations"
                  @focus="showStartSuggestions = true"
                  @blur="handleStartBlur"
                  placeholder="请输入起点"
                  class="location-input-field"
                />
                <div v-if="showStartSuggestions && filteredStartLocations.length > 0" class="suggestions-container">
                  <div
                    v-for="location in filteredStartLocations"
                    :key="'start-'+location.id"
                    class="suggestion-item"
                    @mousedown="selectStartLocation(location)"
                  >
                    {{ location.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 途径点选择 -->
          <div class="waypoints-container">
            <div class="waypoints-header">
              <div class="waypoints-title">途径点：</div>
              <button @click="addWaypoint" class="add-waypoint-btn">
                <span>+</span> 添加途径点
              </button>
            </div>

            <div v-if="waypoints.length === 0" class="no-waypoints-message">
              未添加途径点，可点击上方按钮添加
            </div>

            <div v-for="(waypoint, index) in waypoints" :key="'waypoint-'+index" class="waypoint-item">
              <div class="waypoint-index">{{ index + 1 }}</div>
              <div class="waypoint-input">
                <div class="autocomplete-container">
                  <input
                    type="text"
                    v-model="waypoint.searchText"
                    @input="() => filterWaypointLocations(index)"
                    @focus="waypoint.showSuggestions = true"
                    @blur="() => handleWaypointBlur(index)"
                    placeholder="请输入途径点"
                    class="location-input-field"
                  />
                  <div v-if="waypoint.showSuggestions && waypoint.filteredLocations.length > 0" class="suggestions-container">
                    <div
                      v-for="location in waypoint.filteredLocations"
                      :key="'waypoint-'+index+'-'+location.id"
                      class="suggestion-item"
                      @mousedown="() => selectWaypointLocation(index, location)"
                    >
                      {{ location.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="waypoint-actions">
                <button @click="() => removeWaypoint(index)" class="remove-waypoint-btn">
                  <span>×</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 终点选择 -->
          <div class="location-item">
            <div class="location-label">终点：</div>
            <div class="location-input">
              <div class="autocomplete-container">
                <input
                  type="text"
                  v-model="endSearchText"
                  @input="filterEndLocations"
                  @focus="showEndSuggestions = true"
                  @blur="handleEndBlur"
                  placeholder="请输入终点"
                  class="location-input-field"
                />
                <div v-if="showEndSuggestions && filteredEndLocations.length > 0" class="suggestions-container">
                  <div
                    v-for="location in filteredEndLocations"
                    :key="'end-'+location.id"
                    class="suggestion-item"
                    @mousedown="selectEndLocation(location)"
                  >
                    {{ location.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="selector-note">
            或者直接在地图上点击选择位置
          </div>

          <!-- 路径规划策略选择 -->
          <div class="strategy-selector">
            <div class="strategy-title">规划策略：</div>
            <div class="strategy-options">
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 0 }"
                @click="selectStrategy(0)"
              >
                <span class="strategy-icon">🚗</span>
                <span>最短距离</span>
              </div>
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 1 }"
                @click="selectStrategy(1)"
              >
                <span class="strategy-icon">⏱️</span>
                <span>最短时间</span>
              </div>
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 3 }"
                @click="selectStrategy(3)"
              >
                <span class="strategy-icon">🚀</span>
                <span>智能出行</span>
              </div>
            </div>
          </div>
        </div>

        <div class="marker-controls">
          <div class="marker-mode">
            <div class="mode-title">地图标记模式：</div>
            <div class="mode-options">
              <div
                class="mode-option"
                :class="{ active: currentMode === 'start' }"
                @click="setMode('start')"
              >
                起点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'waypoint' }"
                @click="setMode('waypoint')"
              >
                途径点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'end' }"
                @click="setMode('end')"
              >
                终点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'select' }"
                @click="selectPointOnMap"
              >
                选点
              </div>
            </div>
          </div>
          <div class="marker-instructions">
            {{ getInstructions() }}
          </div>
        </div>

        <div class="marker-info">
          <div class="marker-item" v-if="startMarker">
            <div class="marker-label">起点：</div>
            <div class="marker-value">{{ getLocationDisplayName(startLocationName) || '地图选择的位置' }}</div>
          </div>

          <!-- 途径点信息 -->
          <div v-for="(waypoint, index) in visibleWaypoints" :key="'waypoint-info-'+index" class="marker-item">
            <div class="marker-label">途径点{{ getWaypointIndex(waypoint) + 1 }}：</div>
            <div class="marker-value">{{ getLocationDisplayName(waypoint.locationName) || '地图选择的位置' }}</div>
          </div>

          <div class="marker-item" v-if="endMarker">
            <div class="marker-label">终点：</div>
            <div class="marker-value">{{ getLocationDisplayName(endLocationName) || '地图选择的位置' }}</div>
          </div>
        </div>

        <!-- 坐标显示区域 -->
        <div v-if="showCoordinates && selectedPoint" class="coordinate-info">
          <div class="coordinate-title">📍 选点坐标信息</div>
          <div class="coordinate-item">
            <div class="coordinate-label">经度：</div>
            <div class="coordinate-value">{{ selectedPoint.lng.toFixed(14) }}</div>
          </div>
          <div class="coordinate-item">
            <div class="coordinate-label">纬度：</div>
            <div class="coordinate-value">{{ selectedPoint.lat.toFixed(14) }}</div>
          </div>
          <div class="coordinate-item">
            <div class="coordinate-label">X坐标：</div>
            <div class="coordinate-value">{{ selectedPoint.x }}</div>
          </div>
          <div class="coordinate-item">
            <div class="coordinate-label">Y坐标：</div>
            <div class="coordinate-value">{{ selectedPoint.y }}</div>
          </div>
          <div class="coordinate-formula">
            <div class="formula-title">转换公式：</div>
            <div class="formula-text">
              经度 = 116.47273751541184 + 0.0000115953776764 × X<br>
              纬度 = 39.933922707665424 + 0.0000089569708634 × Y
            </div>
          </div>
        </div>

        <!-- 交通方式选择 -->
        <div class="transport-mode">
          <div class="mode-title">交通方式：</div>
          <div class="transport-options">
            <div
              v-for="mode in transportModes"
              :key="mode.value"
              class="transport-option"
              :class="{
                active: selectedTransportMode === mode.value,
                disabled: selectedStrategy === 3 && mode.value !== 'driving'
              }"
              @click="selectTransportMode(mode.value)"
            >
              <span class="mode-icon">{{ mode.icon }}</span>
              <span>{{ mode.label }}</span>
            </div>
          </div>
        </div>

        <!-- 路线颜色图例 -->
        <div class="color-legend">
          <div class="legend-title">路线颜色说明：</div>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color blue"></div>
              <span>步行路段</span>
            </div>
            <div class="legend-item">
              <div class="legend-color pink"></div>
              <span>电瓶车1线</span>
            </div>
            <div class="legend-item">
              <div class="legend-color yellow"></div>
              <span>电瓶车2线</span>
            </div>
          </div>
        </div>

        <!-- 路线信息 -->
        <div class="route-info">
          <div class="route-stats">
            <div class="stat-item">
              <span class="stat-label">总距离：</span>
              <span class="stat-value">{{ routeTotalDistance }}</span>
            </div>
            <div class="stat-item" v-if="routeScooterDistance && routeScooterDistance !== '0 米'">
              <span class="stat-label">电瓶车距离：</span>
              <span class="stat-value">{{ routeScooterDistance }}</span>
            </div>
            <div class="stat-item" v-if="routeWalkingDistance && routeWalkingDistance !== '0 米'">
              <span class="stat-label">步行距离：</span>
              <span class="stat-value">{{ routeWalkingDistance }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">预计时间：</span>
              <span class="stat-value">{{ routeDuration }}</span>
            </div>
            <div class="stat-item" v-if="routeCost && routeCost !== '0 元'">
              <span class="stat-label">预计费用：</span>
              <span class="stat-value">{{ routeCost }}</span>
            </div>
          </div>

          <!-- 路线详情 -->
          <div v-if="routeSteps.length > 0" class="route-steps">
            <div class="steps-title">路线详情：</div>
            <div class="steps-list">
              <div v-for="(step, index) in routeSteps" :key="index" class="step-item">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">{{ step }}</div>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <button @click="calculateRoute" class="action-btn calculate-btn" :disabled="!canCalculate">
              规划路线
            </button>
            <button @click="startNavigation" class="action-btn navigate-btn" :disabled="!canCalculate">
              开始导航
            </button>
            <button @click="clearMarkers" class="action-btn clear-btn">
              清除标记
            </button>
          </div>

          <!-- 电瓶车线路显示按钮 -->
          <div class="scooter-line-buttons">
            <div class="scooter-line-title">电瓶车线路：</div>
            <div class="scooter-line-actions">
              <button @click="toggleScooterLine(1)" class="scooter-line-btn" :class="{ active: showingScooterLine1 }">
                <span class="line-color line1"></span>
                电瓶车1线
              </button>
              <button @click="toggleScooterLine(2)" class="scooter-line-btn" :class="{ active: showingScooterLine2 }">
                <span class="line-color line2"></span>
                电瓶车2线
              </button>
            </div>
          </div>


        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { getChaoyangParkVertices, getChaoyangParkVerticesByType, getChaoyangParkTypes } from '@/api/path'

// 创建地图实例和标记点的引用
const mapInstance = ref(null)
const startMarker = ref(null)
const endMarker = ref(null)
const polyline = ref(null)
const currentMode = ref('start') // 'start', 'waypoint' 或 'end'
const routeDistance = ref('0 米')
const routeTotalDistance = ref('0 米')
const routeScooterDistance = ref('')
const routeWalkingDistance = ref('')
const routeDuration = ref('0 分钟')
const routeCost = ref('0 元')
const routeSteps = ref([])
const selectedTransportMode = ref('driving')
const hasRoute = ref(false)

// 地点选择相关
const locations = ref([])
const selectedStartId = ref('')
const selectedEndId = ref('')
const startLocationName = ref('')
const endLocationName = ref('')

// 途径点相关
const waypoints = ref([])
const currentWaypointIndex = ref(0) // 当前选择的途径点索引

// 自动完成相关
const startSearchText = ref('')
const endSearchText = ref('')
const filteredStartLocations = ref([])
const filteredEndLocations = ref([])
const showStartSuggestions = ref(false)
const showEndSuggestions = ref(false)

// 路径规划策略
const selectedStrategy = ref(0) // 0: 最短距离, 1: 最短时间, 3: 智能出行

// 面板控制
const isPanelCollapsed = ref(false)

// 选点功能相关
const selectedPoint = ref(null)
const selectedPointMarker = ref(null)
const showCoordinates = ref(false)

// 地点类型筛选相关
const locationTypes = ref([])
const selectedLocationType = ref('')
const filteredLocationsByType = ref([])
const locationMarkers = ref([]) // 存储地点标记
const transferStationMarkers = ref([]) // 存储换乘站标记
const scooterStationMarkers = ref([]) // 存储电瓶车站点标记

// 电瓶车线路显示相关
const showingScooterLine1 = ref(false)
const showingScooterLine2 = ref(false)
const scooterLine1Markers = ref([]) // 电瓶车1线标记
const scooterLine2Markers = ref([]) // 电瓶车2线标记
const scooterLine1Polylines = ref([]) // 电瓶车1线路径
const scooterLine2Polylines = ref([]) // 电瓶车2线路径

// 朝阳公园地点数据（示例数据）
const chaoyangParkLocations = [
  { id: 1, label: '南门', x: 116.473, y: 39.9288 },
  { id: 2, label: '北门', x: 116.4735, y: 39.9355 },
  { id: 3, label: '东门', x: 116.4785, y: 39.9315 },
  { id: 4, label: '西门', x: 116.4685, y: 39.9315 },
  { id: 5, label: '湖心岛', x: 116.4735, y: 39.9315 },
  { id: 6, label: '儿童游乐场', x: 116.4745, y: 39.9325 },
  { id: 7, label: '音乐喷泉', x: 116.4725, y: 39.9305 },
  { id: 8, label: '樱花园', x: 116.4755, y: 39.9335 },
  { id: 9, label: '荷花池', x: 116.4715, y: 39.9295 },
  { id: 10, label: '健身广场', x: 116.4765, y: 39.9345 }
]

// 交通方式选项
const transportModes = [
  { value: 'driving', label: '不限', icon: '🚗' },
  { value: 'walking', label: '步行', icon: '🚶‍♂️' },
  { value: 'riding', label: '电瓶车', icon: '🛵' }
]

// 计算属性：是否可以计算路线
const canCalculate = computed(() => {
  return selectedStartId.value && selectedEndId.value
})

// 计算属性：可见的途径点（有标记的）
const visibleWaypoints = computed(() => {
  return waypoints.value.filter(waypoint => waypoint.marker)
})

// 获取途径点在原数组中的索引
const getWaypointIndex = (waypoint) => {
  return waypoints.value.findIndex(wp => wp === waypoint)
}

// 提取位置显示名称（只显示地点名称）
const getLocationDisplayName = (fullLocationName) => {
  if (!fullLocationName) return ''
  return fullLocationName
}

// 初始化地点数据
const initializeLocations = async () => {
  try {
    // 获取朝阳公园地点数据
    const response = await getChaoyangParkVertices()
    if (response.data) {
      // 转换数据格式以适配现有代码
      locations.value = response.data.map(vertex => ({
        id: vertex.vertex_id,
        label: vertex.label,
        x: convertXYToLngLat(vertex.x, vertex.y).lng,
        y: convertXYToLngLat(vertex.x, vertex.y).lat,
        type: vertex.type,
        originalX: vertex.x,
        originalY: vertex.y
      }))
    }

    // 获取地点类型
    const typesResponse = await getChaoyangParkTypes()
    if (typesResponse.data && typesResponse.data.types) {
      locationTypes.value = typesResponse.data.types
    }
  } catch (error) {
    console.error('获取朝阳公园数据失败:', error)
    // 如果API调用失败，使用示例数据
    locations.value = chaoyangParkLocations
  }
}

// 坐标转换函数：x、y坐标转换为经纬度
const convertXYToLngLat = (x, y) => {
  // 根据提供的公式转换坐标
  // 经度=116.47273751541184+0.0000115953776764 * x
  // 纬度=39.933922707665424+0.0000089569708634 * y
  const lng = 116.47273751541184 + 0.0000115953776764 * x
  const lat = 39.933922707665424 + 0.0000089569708634 * y

  return { lng, lat }
}

// 添加途径点
const addWaypoint = () => {
  waypoints.value.push({
    marker: null,
    vertexId: '',
    locationName: '',
    searchText: '',
    showSuggestions: false,
    filteredLocations: []
  })

  // 自动切换到途径点模式
  currentMode.value = 'waypoint'
  currentWaypointIndex.value = waypoints.value.length - 1
}

// 移除途径点
const removeWaypoint = (index) => {
  // 如果有标记，先从地图上移除
  if (waypoints.value[index].marker) {
    mapInstance.value.remove(waypoints.value[index].marker)
  }

  // 从数组中移除
  waypoints.value.splice(index, 1)

  // 如果移除后没有途径点，切换到起点或终点模式
  if (waypoints.value.length === 0) {
    if (!startMarker.value) {
      currentMode.value = 'start'
    } else if (!endMarker.value) {
      currentMode.value = 'end'
    }
  } else if (currentWaypointIndex.value >= waypoints.value.length) {
    // 如果当前索引超出范围，重置为最后一个
    currentWaypointIndex.value = waypoints.value.length - 1
  }

  // 如果已有路线，清除
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
    polyline.value = null
    routeDistance.value = '0 米'
    routeTotalDistance.value = '0 米'
    routeScooterDistance.value = ''
    routeWalkingDistance.value = ''
    routeCost.value = '0 元'
    routeSteps.value = []
    hasRoute.value = false
  }
}

// 过滤起点位置
const filterStartLocations = () => {
  if (!startSearchText.value.trim()) {
    filteredStartLocations.value = []
    return
  }

  const searchText = startSearchText.value.toLowerCase().trim()
  filteredStartLocations.value = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 过滤终点位置
const filterEndLocations = () => {
  if (!endSearchText.value.trim()) {
    filteredEndLocations.value = []
    return
  }

  const searchText = endSearchText.value.toLowerCase().trim()
  filteredEndLocations.value = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 过滤途径点位置
const filterWaypointLocations = (index) => {
  const waypoint = waypoints.value[index]
  if (!waypoint.searchText.trim()) {
    waypoint.filteredLocations = []
    return
  }

  const searchText = waypoint.searchText.toLowerCase().trim()
  waypoint.filteredLocations = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 选择起点位置
const selectStartLocation = (location) => {
  selectedStartId.value = location.id
  startSearchText.value = location.label
  showStartSuggestions.value = false

  console.log('选择起点:', location.label, 'ID:', location.id)

  // 在地图上标记位置
  const position = new window.AMap.LngLat(location.x, location.y)
  addStartMarker(position)

  startLocationName.value = location.label

  console.log('起点设置完成，canCalculate:', canCalculate.value)
}

// 选择终点位置
const selectEndLocation = (location) => {
  selectedEndId.value = location.id
  endSearchText.value = location.label
  showEndSuggestions.value = false

  console.log('选择终点:', location.label, 'ID:', location.id)

  // 在地图上标记位置
  const position = new window.AMap.LngLat(location.x, location.y)
  addEndMarker(position)

  endLocationName.value = location.label

  console.log('终点设置完成，canCalculate:', canCalculate.value)
}

// 选择途径点位置
const selectWaypointLocation = (index, location) => {
  const waypoint = waypoints.value[index]
  waypoint.vertexId = location.id
  waypoint.searchText = location.label
  waypoint.showSuggestions = false

  // 在地图上标记位置
  const position = new window.AMap.LngLat(location.x, location.y)
  addWaypointMarker(position, index)

  waypoint.locationName = location.label
}

// 处理起点输入框失焦事件
const handleStartBlur = () => {
  setTimeout(() => {
    showStartSuggestions.value = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!selectedStartId.value && startSearchText.value.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === startSearchText.value.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectStartLocation(matchedLocation)
      }
    }
  }, 200)
}

// 处理终点输入框失焦事件
const handleEndBlur = () => {
  setTimeout(() => {
    showEndSuggestions.value = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!selectedEndId.value && endSearchText.value.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === endSearchText.value.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectEndLocation(matchedLocation)
      }
    }
  }, 200)
}

// 处理途径点输入框失焦事件
const handleWaypointBlur = (index) => {
  setTimeout(() => {
    const waypoint = waypoints.value[index]
    waypoint.showSuggestions = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!waypoint.vertexId && waypoint.searchText.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === waypoint.searchText.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectWaypointLocation(index, matchedLocation)
      }
    }
  }, 200)
}

// 选择策略
const selectStrategy = (strategy) => {
  selectedStrategy.value = strategy

  // 如果选择智能出行，自动设置为不限模式
  if (strategy === 3) {
    selectedTransportMode.value = 'driving'
  }
}

// 选择交通方式
const selectTransportMode = (mode) => {
  // 如果是智能出行策略，只允许选择不限模式
  if (selectedStrategy.value === 3 && mode !== 'driving') {
    return
  }

  selectedTransportMode.value = mode
}

// 设置标记模式
const setMode = (mode) => {
  currentMode.value = mode

  // 如果切换到途径点模式，确保有途径点可选择
  if (mode === 'waypoint' && waypoints.value.length === 0) {
    addWaypoint()
  }
}

// 获取操作指引
const getInstructions = () => {
  switch (currentMode.value) {
    case 'start':
      return '点击地图选择起点位置'
    case 'waypoint':
      return '点击地图选择途径点位置'
    case 'end':
      return '点击地图选择终点位置'
    case 'select':
      return '点击地图选择任意位置查看坐标'
    default:
      return '请选择标记模式'
  }
}

// 面板切换
const togglePanel = () => {
  isPanelCollapsed.value = !isPanelCollapsed.value
  const panel = document.querySelector('.planner-panel')
  const toggleIcon = document.querySelector('.toggle-icon')

  if (isPanelCollapsed.value) {
    panel.classList.add('collapsed')
    toggleIcon.textContent = '◀'
  } else {
    panel.classList.remove('collapsed')
    toggleIcon.textContent = '▶'
  }
}

// 坐标转换函数：经纬度转换为x、y坐标
const convertLngLatToXY = (lng, lat) => {
  // 根据提供的公式反推x、y坐标
  // 经度=116.47273751541184+0.0000115953776764 * x
  // 纬度=39.933922707665424+0.0000089569708634 * y
  const x = (lng - 116.47273751541184) / 0.0000115953776764
  const y = (lat - 39.933922707665424) / 0.0000089569708634

  return {
    x: Math.round(x),
    y: Math.round(y)
  }
}

// 选点功能
const selectPointOnMap = () => {
  currentMode.value = 'select'
  // 清除之前的选点标记
  if (selectedPointMarker.value) {
    mapInstance.value.remove(selectedPointMarker.value)
    selectedPointMarker.value = null
  }
  selectedPoint.value = null
  showCoordinates.value = false
}

// 添加选点标记
const addSelectedPointMarker = (position) => {
  // 移除之前的标记
  if (selectedPointMarker.value) {
    mapInstance.value.remove(selectedPointMarker.value)
  }

  // 创建新的选点标记
  selectedPointMarker.value = new window.AMap.Marker({
    position: position,
    icon: new window.AMap.Icon({
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="8" fill="#FF6B6B" stroke="#fff" stroke-width="2"/>
          <circle cx="12" cy="12" r="3" fill="#fff"/>
        </svg>
      `),
      size: new window.AMap.Size(24, 24),
      imageOffset: new window.AMap.Pixel(-12, -12)
    }),
    title: '选择的点'
  })

  mapInstance.value.add(selectedPointMarker.value)

  // 保存选点信息
  const coordinates = convertLngLatToXY(position.lng, position.lat)
  selectedPoint.value = {
    lng: position.lng,
    lat: position.lat,
    x: coordinates.x,
    y: coordinates.y
  }

  showCoordinates.value = true
}

// 添加起点标记
const addStartMarker = (position) => {
  // 如果已有起点标记，先移除
  if (startMarker.value) {
    mapInstance.value.remove(startMarker.value)
  }

  // 创建新的起点标记 - 使用与北邮导航一致的简单标记
  startMarker.value = new window.AMap.Marker({
    position: position,
    title: '起点'
  })

  mapInstance.value.add(startMarker.value)
}

// 添加终点标记
const addEndMarker = (position) => {
  // 如果已有终点标记，先移除
  if (endMarker.value) {
    mapInstance.value.remove(endMarker.value)
  }

  // 创建新的终点标记 - 使用与北邮导航一致的简单标记
  endMarker.value = new window.AMap.Marker({
    position: position,
    title: '终点'
  })

  mapInstance.value.add(endMarker.value)
}

// 添加途径点标记
const addWaypointMarker = (position, index) => {
  const waypoint = waypoints.value[index]

  // 如果已有标记，先移除
  if (waypoint.marker) {
    mapInstance.value.remove(waypoint.marker)
  }

  // 创建新的途径点标记 - 使用与北邮导航一致的简单标记，带标签显示序号
  waypoint.marker = new window.AMap.Marker({
    position: position,
    title: `途径点${index + 1}`,
    label: {
      content: `<div class="waypoint-label">${index + 1}</div>`,
      direction: 'center'
    }
  })

  mapInstance.value.add(waypoint.marker)
}

// 添加换乘站标记
const addTransferStationMarkers = (transferStations) => {
  // 清除之前的换乘站标记
  clearTransferStationMarkers()

  transferStations.forEach(station => {
    const position = convertXYToLngLat(station.x, station.y)
    const lngLat = new window.AMap.LngLat(position.lng, position.lat)

    // 创建换乘站标记 - 使用地铁换乘标识
    const transferMarker = new window.AMap.Marker({
      position: lngLat,
      icon: new window.AMap.Icon({
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
            <circle cx="16" cy="16" r="14" fill="#FF6B35" stroke="#fff" stroke-width="3"/>
            <circle cx="16" cy="16" r="8" fill="#fff"/>
            <text x="16" y="20" text-anchor="middle" fill="#FF6B35" font-size="10" font-weight="bold">换</text>
          </svg>
        `),
        size: new window.AMap.Size(32, 32),
        imageOffset: new window.AMap.Pixel(-16, -16)
      }),
      title: `换乘站: ${station.label}`,
      zIndex: 1000, // 确保换乘站标记在最上层
      label: {
        content: `<div style="background: #FF6B35; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px; white-space: nowrap;">${station.label}</div>`,
        offset: new window.AMap.Pixel(0, -40),
        direction: 'top'
      }
    })

    mapInstance.value.add(transferMarker)
    transferStationMarkers.value.push(transferMarker)
  })
}

// 添加电瓶车站点标记
const addScooterStationMarkers = (scooterStations) => {
  // 清除之前的电瓶车站点标记
  clearScooterStationMarkers()

  console.log('添加电瓶车站点标记:', scooterStations)

  scooterStations.forEach(station => {
    const position = convertXYToLngLat(station.x, station.y)
    const lngLat = new window.AMap.LngLat(position.lng, position.lat)

    // 根据线路确定颜色
    let stationColor = '#3498db' // 默认蓝色
    let isTransferStation = false

    if (station.lines.includes('电瓶车1线') && station.lines.includes('电瓶车2线')) {
      stationColor = '#FF6B35' // 换乘站橙色
      isTransferStation = true
    } else if (station.lines.includes('电瓶车1线')) {
      stationColor = '#e91e63' // 1线粉色
    } else if (station.lines.includes('电瓶车2线')) {
      stationColor = '#ffc107' // 2线黄色
    }

    // 创建电瓶车站点标记
    const stationMarker = new window.AMap.Marker({
      position: lngLat,
      icon: new window.AMap.Icon({
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="${stationColor}" stroke="#fff" stroke-width="2"/>
            <circle cx="12" cy="12" r="4" fill="#fff"/>
            ${isTransferStation ? '<text x="12" y="16" text-anchor="middle" fill="#fff" font-size="8" font-weight="bold">换</text>' : ''}
          </svg>
        `),
        size: new window.AMap.Size(24, 24),
        imageOffset: new window.AMap.Pixel(-12, -12)
      }),
      title: `${station.label} (${station.lines.join(', ')})`,
      zIndex: 800,
      label: {
        content: `<div style="background: ${stationColor}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; white-space: nowrap; box-shadow: 0 1px 3px rgba(0,0,0,0.3);">${station.label}</div>`,
        offset: new window.AMap.Pixel(0, -35),
        direction: 'top'
      }
    })

    mapInstance.value.add(stationMarker)
    scooterStationMarkers.value.push(stationMarker)
  })
}

// 清除电瓶车站点标记
const clearScooterStationMarkers = () => {
  scooterStationMarkers.value.forEach(marker => {
    mapInstance.value.remove(marker)
  })
  scooterStationMarkers.value = []
}

// 清除换乘站标记
const clearTransferStationMarkers = () => {
  transferStationMarkers.value.forEach(marker => {
    mapInstance.value.remove(marker)
  })
  transferStationMarkers.value = []
}

// 切换电瓶车线路显示
const toggleScooterLine = async (lineNumber) => {
  try {
    if (lineNumber === 1) {
      if (showingScooterLine1.value) {
        // 隐藏电瓶车1线
        hideScooterLine(1)
        showingScooterLine1.value = false
      } else {
        // 显示电瓶车1线
        await showScooterLine(1)
        showingScooterLine1.value = true
      }
    } else if (lineNumber === 2) {
      if (showingScooterLine2.value) {
        // 隐藏电瓶车2线
        hideScooterLine(2)
        showingScooterLine2.value = false
      } else {
        // 显示电瓶车2线
        await showScooterLine(2)
        showingScooterLine2.value = true
      }
    }
  } catch (error) {
    console.error(`切换电瓶车${lineNumber}线失败:`, error)
    alert(`显示电瓶车${lineNumber}线失败: ${error.message}`)
  }
}

// 显示电瓶车线路
const showScooterLine = async (lineNumber) => {
  try {
    // 调用后端API获取电瓶车线路数据
    const response = await fetch(`http://localhost:5000/api/path/chaoyang-park/scooter-line/${lineNumber}`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    console.log(`电瓶车${lineNumber}线数据:`, data)

    if (data.error) {
      throw new Error(data.error)
    }

    // 绘制线路和站点
    drawScooterLine(lineNumber, data)

  } catch (error) {
    console.error(`获取电瓶车${lineNumber}线数据失败:`, error)
    throw error
  }
}

// 绘制电瓶车线路
const drawScooterLine = (lineNumber, data) => {
  const { edges, stations } = data
  const lineColor = lineNumber === 1 ? '#e91e63' : '#ffc107' // 粉色或黄色
  const markers = lineNumber === 1 ? scooterLine1Markers : scooterLine2Markers
  const polylines = lineNumber === 1 ? scooterLine1Polylines : scooterLine2Polylines

  // 绘制站点标记
  stations.forEach(station => {
    const position = convertXYToLngLat(station.x, station.y)
    const lngLat = new window.AMap.LngLat(position.lng, position.lat)

    const marker = new window.AMap.Marker({
      position: lngLat,
      icon: new window.AMap.Icon({
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
            <circle cx="10" cy="10" r="8" fill="${lineColor}" stroke="#fff" stroke-width="2"/>
            <circle cx="10" cy="10" r="3" fill="#fff"/>
          </svg>
        `),
        size: new window.AMap.Size(20, 20),
        imageOffset: new window.AMap.Pixel(-10, -10)
      }),
      title: `${station.label} (电瓶车${lineNumber}线)`,
      zIndex: 500,
      label: {
        content: `<div style="background: ${lineColor}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 11px; white-space: nowrap; box-shadow: 0 1px 3px rgba(0,0,0,0.3);">${station.label}</div>`,
        offset: new window.AMap.Pixel(0, -35),
        direction: 'top'
      }
    })

    mapInstance.value.add(marker)
    markers.value.push(marker)
  })

  // 绘制线路
  edges.forEach(edge => {
    const startStation = stations.find(s => s.vertex_id === edge.src_id)
    const endStation = stations.find(s => s.vertex_id === edge.des_id)

    if (startStation && endStation) {
      const startPos = convertXYToLngLat(startStation.x, startStation.y)
      const endPos = convertXYToLngLat(endStation.x, endStation.y)

      const polyline = new window.AMap.Polyline({
        path: [
          new window.AMap.LngLat(startPos.lng, startPos.lat),
          new window.AMap.LngLat(endPos.lng, endPos.lat)
        ],
        strokeColor: lineColor,
        strokeWeight: 4,
        strokeOpacity: 0.8,
        zIndex: 100
      })

      mapInstance.value.add(polyline)
      polylines.value.push(polyline)
    }
  })

  console.log(`电瓶车${lineNumber}线绘制完成，站点数: ${stations.length}, 线段数: ${edges.length}`)
}

// 隐藏电瓶车线路
const hideScooterLine = (lineNumber) => {
  const markers = lineNumber === 1 ? scooterLine1Markers : scooterLine2Markers
  const polylines = lineNumber === 1 ? scooterLine1Polylines : scooterLine2Polylines

  // 清除站点标记
  markers.value.forEach(marker => {
    mapInstance.value.remove(marker)
  })
  markers.value = []

  // 清除线路
  polylines.value.forEach(polyline => {
    mapInstance.value.remove(polyline)
  })
  polylines.value = []

  console.log(`电瓶车${lineNumber}线已隐藏`)
}

// 清除所有标记
const clearMarkers = () => {
  // 清除起点标记
  if (startMarker.value) {
    mapInstance.value.remove(startMarker.value)
    startMarker.value = null
    startLocationName.value = ''
    selectedStartId.value = ''
    startSearchText.value = ''
  }

  // 清除终点标记
  if (endMarker.value) {
    mapInstance.value.remove(endMarker.value)
    endMarker.value = null
    endLocationName.value = ''
    selectedEndId.value = ''
    endSearchText.value = ''
  }

  // 清除途径点标记
  waypoints.value.forEach(waypoint => {
    if (waypoint.marker) {
      mapInstance.value.remove(waypoint.marker)
    }
  })
  waypoints.value = []

  // 清除选点标记
  if (selectedPointMarker.value) {
    mapInstance.value.remove(selectedPointMarker.value)
    selectedPointMarker.value = null
  }

  // 清除换乘站标记
  clearTransferStationMarkers()

  // 清除电瓶车线路
  if (showingScooterLine1.value) {
    hideScooterLine(1)
    showingScooterLine1.value = false
  }
  if (showingScooterLine2.value) {
    hideScooterLine(2)
    showingScooterLine2.value = false
  }

  // 清除路线
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
    polyline.value = null
  }

  // 重置路线信息
  routeDistance.value = '0 米'
  routeTotalDistance.value = '0 米'
  routeScooterDistance.value = ''
  routeWalkingDistance.value = ''
  routeDuration.value = '0 分钟'
  routeCost.value = '0 元'
  routeSteps.value = []
  hasRoute.value = false

  // 重置选点信息
  selectedPoint.value = null
  showCoordinates.value = false

  // 重置模式
  currentMode.value = 'start'
}

// 计算路线（调用朝阳公园后端API）
const calculateRoute = async () => {
  console.log('开始计算朝阳公园路线...')
  console.log('起点ID:', selectedStartId.value)
  console.log('终点ID:', selectedEndId.value)

  // 检查是否有起点和终点
  if (!selectedStartId.value || !selectedEndId.value) {
    alert('请先选择起点和终点')
    return
  }

  try {
    // 显示加载状态
    routeDistance.value = '计算中...'
    routeTotalDistance.value = '计算中...'
    routeScooterDistance.value = ''
    routeWalkingDistance.value = ''
    routeDuration.value = '计算中...'
    routeCost.value = '计算中...'
    routeSteps.value = []

    // 准备请求数据
    const waypointIds = waypoints.value
      .filter(wp => wp.vertexId)
      .map(wp => parseInt(wp.vertexId))

    let requestData
    let apiUrl = 'http://localhost:5000/api/path/chaoyang-park/plan'

    if (waypointIds.length > 0) {
      // 多目标路径规划
      requestData = {
        start_id: parseInt(selectedStartId.value),
        dest_ids: waypointIds.concat([parseInt(selectedEndId.value)]), // 将终点添加到途径点列表末尾
        strategy: selectedStrategy.value,
        transport_mode: selectedTransportMode.value,
        algorithm: 'simple'
      }
    } else {
      // 单目标路径规划
      requestData = {
        start_id: parseInt(selectedStartId.value),
        dest_id: parseInt(selectedEndId.value),
        strategy: selectedStrategy.value,
        transport_mode: selectedTransportMode.value
      }
    }

    console.log('发送朝阳公园路径规划请求:', requestData)

    // 调用朝阳公园路径规划API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('朝阳公园路径规划结果:', result)

    if (result.error) {
      throw new Error(result.error)
    }

    // 处理路径规划结果
    if (result.path && result.path.length > 0) {
      // 更新距离和时间信息
      const totalDistance = result.total_distance || 0
      const totalTime = result.total_time || 0
      const cyclingDistance = result.cycling_distance || 0
      const walkingDistance = result.walking_distance || 0
      const cost = result.cost || 0

      // 格式化显示
      routeTotalDistance.value = `${(totalDistance / 1000).toFixed(2)} 公里`

      // 设置电瓶车和步行距离
      if (cyclingDistance > 0) {
        routeScooterDistance.value = `${(cyclingDistance / 1000).toFixed(2)} 公里`
      } else {
        routeScooterDistance.value = ''
      }

      if (walkingDistance > 0) {
        routeWalkingDistance.value = `${(walkingDistance / 1000).toFixed(2)} 公里`
      } else {
        routeWalkingDistance.value = ''
      }

      routeDuration.value = `${Math.ceil(totalTime)} 分钟`

      // 设置费用
      if (cost > 0) {
        routeCost.value = `${cost.toFixed(0)} 元`
      } else {
        routeCost.value = '0 元'
      }

      // 绘制路径
      await drawBackendRoute(result)

      // 设置路线步骤
      if (result.path_details && result.path_details.length > 0) {
        routeSteps.value = result.path_details.map((detail, index) => {
          const modeText = detail.travel_mode === 'riding' ? '🛴' : '🚶'
          return `${index + 1}. ${modeText} 从 ${detail.from} 到 ${detail.to} (${(detail.distance / 1000).toFixed(2)} 公里)`
        })
      } else {
        routeSteps.value = [
          `从 ${getLocationDisplayName(startLocationName.value) || '起点'} 出发`,
          ...waypoints.value.filter(wp => wp.marker).map((wp, index) =>
            `经过 ${getLocationDisplayName(wp.locationName) || `途径点${index + 1}`}`
          ),
          `到达 ${getLocationDisplayName(endLocationName.value) || '终点'}`
        ]
      }

      hasRoute.value = true
      console.log('朝阳公园路线计算成功')
    } else {
      throw new Error('未找到有效路径')
    }

  } catch (error) {
    console.error('朝阳公园路线计算失败:', error)
    alert(`路线计算失败: ${error.message}`)

    // 回退到直线距离显示
    if (startMarker.value && endMarker.value) {
      const startPos = startMarker.value.getPosition()
      const endPos = endMarker.value.getPosition()
      const distance = window.AMap.GeometryUtil.distance(startPos, endPos)
      routeDistance.value = `${(distance / 1000).toFixed(2)} 公里 (直线距离)`
      routeTotalDistance.value = `${(distance / 1000).toFixed(2)} 公里 (直线距离)`
      routeScooterDistance.value = ''
      routeWalkingDistance.value = ''
      routeDuration.value = '无法估算'
      routeCost.value = '0 元'
      routeSteps.value = ['无法获取详细路线信息']

      // 绘制直线
      drawStraightLine(startPos, endPos)
    }
  }
}



// 绘制后端返回的路径
const drawBackendRoute = async (result) => {
  try {
    // 清除现有路线
    if (polyline.value) {
      if (Array.isArray(polyline.value)) {
        polyline.value.forEach(line => mapInstance.value.remove(line))
      } else {
        mapInstance.value.remove(polyline.value)
      }
      polyline.value = null
    }

    if (!result.vertexes || result.vertexes.length === 0) {
      console.warn('没有顶点数据，无法绘制路径')
      return
    }

    // 将顶点ID路径转换为坐标路径
    const pathCoordinates = result.path.map(vertexId => {
      const vertex = result.vertexes.find(v => v.vertex_id === vertexId)
      if (vertex) {
        const coords = convertXYToLngLat(vertex.x, vertex.y)
        return new window.AMap.LngLat(coords.lng, coords.lat)
      }
      return null
    }).filter(coord => coord !== null)

    if (pathCoordinates.length < 2) {
      console.warn('路径坐标不足，无法绘制路径')
      return
    }

    console.log('绘制路径坐标:', pathCoordinates.length, '个点')

    // 检查是否有路径详情用于分段绘制
    const hasPathDetails = result.path_details && result.path_details.length > 0

    if (hasPathDetails) {
      // 分段绘制，根据路径类型使用不同颜色
      const polylines = []

      for (let i = 0; i < result.path_details.length && i + 1 < pathCoordinates.length; i++) {
        const segmentPath = [pathCoordinates[i], pathCoordinates[i + 1]]
        const detail = result.path_details[i]
        const isCarType = detail.is_car || 0  // 0: 步行道, 1: 电瓶车1线, 2: 电瓶车2线

        // 确定线条颜色和样式
        let strokeColor, strokeStyle

        // 根据路径类型设置颜色
        if (isCarType === 0) {
          // 步行路线：蓝色实线
          strokeColor = '#3498db'
          strokeStyle = 'solid'
        } else if (isCarType === 1) {
          // 电瓶车1线：粉色实线
          strokeColor = '#e91e63'
          strokeStyle = 'solid'
        } else if (isCarType === 2) {
          // 电瓶车2线：黄色实线
          strokeColor = '#ffc107'
          strokeStyle = 'solid'
        } else {
          // 默认：蓝色实线
          strokeColor = '#3498db'
          strokeStyle = 'solid'
        }

        const segmentLine = new window.AMap.Polyline({
          path: segmentPath,
          strokeColor: strokeColor,
          strokeWeight: 6,
          strokeOpacity: 0.8,
          strokeStyle: strokeStyle,
          lineJoin: 'round',
          lineCap: 'round',
          showDir: true  // 显示方向箭头
        })

        mapInstance.value.add(segmentLine)
        polylines.push(segmentLine)
      }

      polyline.value = polylines
      console.log(`已绘制分段路线，共 ${polylines.length} 段`)
    } else {
      // 单一路径绘制
      const defaultColor = selectedTransportMode.value === 'riding' ? '#27ae60' : '#3498db'

      polyline.value = new window.AMap.Polyline({
        path: pathCoordinates,
        strokeColor: defaultColor,
        strokeWeight: 6,
        strokeOpacity: 0.8,
        strokeStyle: 'solid',
        lineJoin: 'round',
        lineCap: 'round',
        showDir: true  // 显示方向箭头
      })

      mapInstance.value.add(polyline.value)
      console.log('已绘制单一路径')
    }

    // 添加换乘站标记
    if (result.transfer_stations && result.transfer_stations.length > 0) {
      console.log('发现换乘站:', result.transfer_stations)
      addTransferStationMarkers(result.transfer_stations)
    }

    // 添加电瓶车站点标记
    if (result.scooter_stations && result.scooter_stations.length > 0) {
      console.log('发现电瓶车站点:', result.scooter_stations)
      addScooterStationMarkers(result.scooter_stations)
    }

    // 调整地图视野以包含整个路径和标记
    const overlays = []
    if (startMarker.value) overlays.push(startMarker.value)
    if (endMarker.value) overlays.push(endMarker.value)

    // 添加途径点标记
    waypoints.value.forEach(waypoint => {
      if (waypoint.marker) overlays.push(waypoint.marker)
    })

    // 添加换乘站标记
    transferStationMarkers.value.forEach(marker => {
      overlays.push(marker)
    })

    // 添加电瓶车站点标记
    scooterStationMarkers.value.forEach(marker => {
      overlays.push(marker)
    })

    // 添加路线
    if (Array.isArray(polyline.value)) {
      polyline.value.forEach(line => overlays.push(line))
    } else if (polyline.value) {
      overlays.push(polyline.value)
    }

    if (overlays.length > 0) {
      mapInstance.value.setFitView(overlays, false, [50, 50, 50, 50])
    }

    console.log('路径绘制完成')
  } catch (error) {
    console.error('绘制路径失败:', error)
  }
}

// 绘制直线路径
const drawStraightLine = (startPos, endPos) => {
  try {
    // 清除现有路线
    if (polyline.value) {
      if (Array.isArray(polyline.value)) {
        polyline.value.forEach(line => mapInstance.value.remove(line))
      } else {
        mapInstance.value.remove(polyline.value)
      }
      polyline.value = null
    }

    // 根据出行方式选择颜色
    const isRidingMode = selectedTransportMode.value === 'riding'
    const lineColor = isRidingMode ? '#27ae60' : '#ff4d4f'

    // 创建直线路径
    polyline.value = new window.AMap.Polyline({
      path: [startPos, endPos],
      strokeColor: lineColor,
      strokeWeight: 4,
      strokeOpacity: 0.8,
      strokeStyle: 'dashed',
      lineJoin: 'round',
      showDir: true  // 显示方向箭头
    })

    mapInstance.value.add(polyline.value)

    // 调整地图视野包含起点、终点和路线
    const overlays = []
    if (startMarker.value) overlays.push(startMarker.value)
    if (endMarker.value) overlays.push(endMarker.value)
    overlays.push(polyline.value)

    mapInstance.value.setFitView(overlays, false, [50, 50, 50, 50])

    hasRoute.value = false // 直线不算真正的路线
  } catch (error) {
    console.error('绘制直线失败:', error)
  }
}

// 开始导航（示例功能）
const startNavigation = () => {
  if (!hasRoute.value) {
    alert('请先规划路线')
    return
  }

  alert('导航功能正在开发中，敬请期待！')
}

// 地图点击事件处理
const handleMapClick = (e) => {
  const position = e.lnglat

  switch (currentMode.value) {
    case 'start':
      addStartMarker(position)
      startLocationName.value = `地图选择的位置 (${position.lng.toFixed(6)}, ${position.lat.toFixed(6)})`
      break
    case 'waypoint': {
      if (waypoints.value.length === 0) {
        addWaypoint()
      }
      const currentWaypoint = waypoints.value[currentWaypointIndex.value]
      if (currentWaypoint) {
        addWaypointMarker(position, currentWaypointIndex.value)
        currentWaypoint.locationName = `地图选择的位置 (${position.lng.toFixed(6)}, ${position.lat.toFixed(6)})`
      }
      break
    }
    case 'end':
      addEndMarker(position)
      endLocationName.value = `地图选择的位置 (${position.lng.toFixed(6)}, ${position.lat.toFixed(6)})`
      break
    case 'select':
      addSelectedPointMarker(position)
      break
  }
}

// 初始化地图
const initMap = async () => {
  try {
    console.log('开始加载朝阳公园地图 API...')

    // 加载高德地图 API
    window.AMap = await AMapLoader.load({
      key: '9ac93278af733b48f3c31aacb870082f', // 高德地图API密钥
      version: '2.0',
      plugins: ['AMap.GeometryUtil', 'AMap.ToolBar', 'AMap.Scale']
    })

    console.log('高德地图 API 加载成功')

    // 检查地图容器是否存在
    const mapContainer = document.getElementById('chaoyang-map-container')
    if (!mapContainer) {
      console.error('找不到地图容器元素')
      return
    }

    // 创建地图实例
    mapInstance.value = new window.AMap.Map('chaoyang-map-container', {
      zoom: 16,
      center: [116.4785, 39.9345], // 朝阳公园东北区域位置
      viewMode: '2D',
      mapStyle: 'amap://styles/normal',
      // 确保地图交互功能启用
      dragEnable: true,
      zoomEnable: true,
      doubleClickZoom: true,
      keyboardEnable: true,
      scrollWheel: true,
      touchZoom: true,
      touchZoomCenter: 1
    })

    // 添加控件
    mapInstance.value.addControl(new window.AMap.ToolBar())
    mapInstance.value.addControl(new window.AMap.Scale())

    console.log('朝阳公园地图实例创建成功')

    // 添加地图点击事件
    mapInstance.value.on('click', handleMapClick)

    // 初始化地点数据
    initializeLocations()

    console.log('朝阳公园地图初始化完成')
  } catch (error) {
    console.error('朝阳公园地图初始化失败:', error)
  }
}

// 地点类型筛选相关方法
const onLocationTypeChange = async () => {
  console.log(`地点类型变更为: ${selectedLocationType.value}`)

  // 先清除之前的标记
  hideLocationMarkers()

  if (!selectedLocationType.value) {
    filteredLocationsByType.value = []
    console.log('清空筛选结果')
    return
  }

  try {
    console.log(`开始获取类型为 "${selectedLocationType.value}" 的地点`)
    const response = await getChaoyangParkVerticesByType(selectedLocationType.value)

    if (response.data) {
      filteredLocationsByType.value = response.data.map(vertex => ({
        id: vertex.vertex_id,
        label: vertex.label,
        x: convertXYToLngLat(vertex.x, vertex.y).lng,
        y: convertXYToLngLat(vertex.x, vertex.y).lat,
        type: vertex.type,
        originalX: vertex.x,
        originalY: vertex.y
      }))

      console.log(`成功获取 ${filteredLocationsByType.value.length} 个 "${selectedLocationType.value}" 类型的地点`)
    } else {
      console.log('API返回数据为空')
      filteredLocationsByType.value = []
    }
  } catch (error) {
    console.error('获取筛选地点失败:', error)
    // 如果API调用失败，从本地数据筛选
    filteredLocationsByType.value = locations.value.filter(
      location => location.type === selectedLocationType.value
    )
    console.log(`API失败，从本地数据筛选到 ${filteredLocationsByType.value.length} 个地点`)
  }
}

// 显示地点标记
const showLocationMarkers = () => {
  console.log('显示地点标记按钮被点击')
  console.log('当前选择的地点类型:', selectedLocationType.value)
  console.log('筛选的地点数量:', filteredLocationsByType.value.length)

  if (filteredLocationsByType.value.length === 0) {
    console.log('没有筛选到地点，显示提示')
    alert('没有找到该类型的地点，请重新选择')
    return
  }

  if (!mapInstance.value) {
    console.error('地图实例不存在')
    alert('地图未初始化，请刷新页面重试')
    return
  }

  console.log(`准备显示 ${filteredLocationsByType.value.length} 个地点标记`)

  // 清除之前的地点标记
  hideLocationMarkers()

  let successCount = 0

  // 添加新的地点标记
  filteredLocationsByType.value.forEach((location, index) => {
    try {
      const position = new window.AMap.LngLat(location.x, location.y)
      console.log(`添加标记 ${index + 1}: ${location.label} at (${location.x}, ${location.y})`)

      const marker = new window.AMap.Marker({
        position: position,
        icon: new window.AMap.Icon({
          image: 'data:image/svg+xml;base64,' + btoa(`
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
              <circle cx="10" cy="10" r="8" fill="#FFA500" stroke="#fff" stroke-width="2"/>
              <circle cx="10" cy="10" r="3" fill="#fff"/>
            </svg>
          `),
          size: new window.AMap.Size(20, 20),
          imageOffset: new window.AMap.Pixel(-10, -10)
        }),
        title: location.label,
        label: {
          content: location.label,
          offset: new window.AMap.Pixel(0, -30),
          direction: 'top'
        }
      })

      mapInstance.value.add(marker)
      locationMarkers.value.push(marker)
      successCount++
      console.log(`成功添加标记 ${index + 1}: ${location.label}`)
    } catch (error) {
      console.error(`添加标记失败: ${location.label}`, error)
    }
  })

  console.log(`成功添加 ${successCount} 个标记到地图，总标记数: ${locationMarkers.value.length}`)

  // 不自动调整地图视野，保持用户当前的视野和交互能力
  if (successCount > 0) {
    console.log(`成功在地图上显示了 ${successCount} 个 "${selectedLocationType.value}" 类型的地点`)

    // 确保地图交互功能始终启用
    setTimeout(() => {
      try {
        mapInstance.value.setStatus({
          dragEnable: true,
          zoomEnable: true,
          doubleClickZoom: true,
          scrollWheel: true,
          touchZoom: true
        })
        console.log('地图交互功能已确保启用')
      } catch (error) {
        console.error('设置地图交互状态失败:', error)
      }
    }, 100)
  }
}

// 隐藏地点标记
const hideLocationMarkers = () => {
  console.log(`准备清除 ${locationMarkers.value.length} 个地点标记`)

  locationMarkers.value.forEach((marker, index) => {
    try {
      if (marker && mapInstance.value) {
        mapInstance.value.remove(marker)
        console.log(`清除标记 ${index + 1}`)
      }
    } catch (error) {
      console.error(`清除标记失败 ${index + 1}:`, error)
    }
  })

  locationMarkers.value = []
  console.log('所有地点标记已清除')
}

// 组件挂载时初始化地图
onMounted(() => {
  // 使用 setTimeout 确保 DOM 已经完全渲染
  setTimeout(() => {
    initMap()
  }, 500) // 延迟 500ms 确保 DOM 已渲染
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (mapInstance.value) {
    mapInstance.value.destroy()
  }
})
</script>

<style scoped>
.route-planner {
  position: relative;
  width: 100%;
  height: calc(100vh - 20px);
  margin-top: 20px;
  display: flex;
}

.map-container {
  flex: 1;
  height: 100%;
  background-color: #f0f0f0;
}

.planner-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 380px;
  max-height: calc(100vh - 40px);
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: transform 0.3s ease;
  overflow: hidden;
}

.planner-panel.collapsed {
  transform: translateX(340px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.panel-toggle {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  transition: background 0.2s;
}

.panel-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.toggle-icon {
  font-size: 14px;
  font-weight: bold;
}

.panel-content {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 20px;
}

/* 地点类型筛选区域样式 */
.location-type-filter {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-selector {
  flex: 1;
}

.type-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.type-select:focus {
  outline: none;
  border-color: #28a745;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.show-markers-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
  flex: 1;
}

.show-markers-btn:hover:not(:disabled) {
  background: #218838;
}

.show-markers-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.hide-markers-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
  flex: 1;
}

.hide-markers-btn:hover {
  background: #c82333;
}

.filtered-locations-info {
  font-size: 13px;
  color: #28a745;
  font-weight: 500;
  text-align: center;
  margin-top: 8px;
  padding: 8px;
  background: #d4edda;
  border-radius: 6px;
}

/* 地点选择区域样式 */
.location-selector {
  margin-bottom: 20px;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.location-label {
  min-width: 50px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.location-input {
  flex: 1;
}

.autocomplete-container {
  position: relative;
}

.location-input-field {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.location-input-field:focus {
  outline: none;
  border-color: #667eea;
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1001;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

/* 途径点样式 */
.waypoints-container {
  margin: 16px 0;
}

.waypoints-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.waypoints-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.add-waypoint-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.add-waypoint-btn:hover {
  background: #218838;
}

.no-waypoints-message {
  color: #6c757d;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.waypoint-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.waypoint-index {
  min-width: 24px;
  height: 24px;
  background: #0066ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.waypoint-input {
  flex: 1;
}

.waypoint-actions {
  display: flex;
  gap: 4px;
}

.remove-waypoint-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.remove-waypoint-btn:hover {
  background: #c82333;
}

.selector-note {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  margin: 12px 0;
  font-style: italic;
}

/* 策略选择样式 */
.strategy-selector {
  margin: 16px 0;
}

.strategy-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.strategy-options {
  display: flex;
  gap: 6px;
}

.strategy-option {
  flex: 1;
  min-width: 70px;
  max-width: 110px;
  padding: 8px 4px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 11px;
  line-height: 1.2;
}

.strategy-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.strategy-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.strategy-icon {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

/* 标记控制样式 */
.marker-controls {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.marker-mode {
  margin-bottom: 12px;
}

.mode-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.mode-options {
  display: flex;
  gap: 8px;
}

.mode-option {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.mode-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.mode-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.marker-instructions {
  font-size: 13px;
  color: #6c757d;
  text-align: center;
  font-style: italic;
}

/* 标记信息样式 */
.marker-info {
  margin: 16px 0;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.marker-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 13px;
}

.marker-item:last-child {
  margin-bottom: 0;
}

.marker-label {
  min-width: 60px;
  font-weight: 500;
  color: #555;
}

.marker-value {
  flex: 1;
  color: #333;
}

/* 坐标信息样式 */
.coordinate-info {
  margin: 16px 0;
  padding: 16px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #FF6B6B;
}

.coordinate-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.coordinate-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
}

.coordinate-item:last-of-type {
  margin-bottom: 12px;
}

.coordinate-label {
  min-width: 60px;
  font-weight: 500;
  color: #555;
}

.coordinate-value {
  flex: 1;
  color: #333;
  font-family: 'Courier New', monospace;
  background: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e1e5e9;
}

.coordinate-formula {
  margin-top: 12px;
  padding: 12px;
  background: #fff3cd;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
}

.formula-title {
  font-size: 13px;
  font-weight: 600;
  color: #856404;
  margin-bottom: 6px;
}

.formula-text {
  font-size: 12px;
  color: #856404;
  font-family: 'Courier New', monospace;
  line-height: 1.4;
}

/* 交通方式样式 */
.transport-mode {
  margin: 16px 0;
}

.transport-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.transport-option {
  flex: 1;
  min-width: 80px;
  padding: 10px 8px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.transport-option:hover:not(.disabled) {
  border-color: #667eea;
  background: #f8f9ff;
}

.transport-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.transport-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.mode-icon {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

/* 路线信息样式 */
.route-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.route-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 路线步骤样式 */
.route-steps {
  margin: 16px 0;
}

.steps-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.steps-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 13px;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  min-width: 20px;
  height: 20px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  margin-right: 8px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  line-height: 1.4;
  color: #333;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  margin: 16px 0;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.calculate-btn {
  background: #28a745;
  color: white;
}

.calculate-btn:hover:not(:disabled) {
  background: #218838;
}

.navigate-btn {
  background: #007bff;
  color: white;
}

.navigate-btn:hover:not(:disabled) {
  background: #0056b3;
}

.clear-btn {
  background: #6c757d;
  color: white;
}

.clear-btn:hover {
  background: #545b62;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 电瓶车线路按钮样式 */
.scooter-line-buttons {
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.scooter-line-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.scooter-line-actions {
  display: flex;
  gap: 8px;
}

.scooter-line-btn {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.scooter-line-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.scooter-line-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.line-color {
  width: 12px;
  height: 3px;
  border-radius: 2px;
}

.line-color.line1 {
  background-color: #e91e63;
}

.line-color.line2 {
  background-color: #ffc107;
}

/* 颜色图例样式 */
.color-legend {
  margin: 15px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.legend-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: #555;
}

.legend-color {
  width: 20px;
  height: 3px;
  border-radius: 2px;
}

.legend-color.blue {
  background-color: #3498db;
}

.legend-color.pink {
  background-color: #e91e63;
}

.legend-color.yellow {
  background-color: #ffc107;
}

/* 标记标签样式 */
.marker-label {
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.start-label {
  color: #27ae60;
  border-color: #27ae60;
}

.end-label {
  color: #e74c3c;
  border-color: #e74c3c;
}

.waypoint-label {
  color: #3498db;
  border-color: #3498db;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .planner-panel {
    width: calc(100% - 40px);
    max-width: 400px;
  }

  .strategy-options {
    flex-direction: column;
  }

  .strategy-option {
    min-width: auto;
  }

  .transport-options {
    flex-direction: column;
  }

  .transport-option {
    min-width: auto;
  }

  .route-stats {
    gap: 6px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.steps-list::-webkit-scrollbar,
.suggestions-container::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.steps-list::-webkit-scrollbar-track,
.suggestions-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.steps-list::-webkit-scrollbar-thumb,
.suggestions-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.steps-list::-webkit-scrollbar-thumb:hover,
.suggestions-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 途径点标签样式 - 与北邮导航保持一致 */
.waypoint-label {
  background: #722ed1;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
</style>
