<template>
  <div class="smart-travel-animation-generator">
    <div class="generator-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 选择日记 -->
        <el-form-item label="选择日记" prop="articleId" required>
          <el-select
            v-model="form.articleId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入日记标题进行搜索"
            :remote-method="searchArticles"
            :loading="searchLoading"
            style="width: 100%;"
            @change="handleArticleChange"
          >
            <el-option
              v-for="article in articleOptions"
              :key="article.article_id"
              :label="article.title"
              :value="article.article_id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                  <div style="font-weight: 500; color: #333;">{{ article.title }}</div>
                  <div style="color: #8492a6; font-size: 12px; margin-top: 2px;">
                    <i class="el-icon-location"></i> {{ article.location }}
                    <span style="margin-left: 10px;">
                      <i class="el-icon-time"></i> {{ formatDate(article.created_at) }}
                    </span>
                    <span style="margin-left: 10px;">
                      <i class="el-icon-picture"></i> {{ getMediaCount(article).images }}张图片
                      <i class="el-icon-video-camera" style="margin-left: 5px;"></i> {{ getMediaCount(article).videos }}个视频
                    </span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <div v-if="searchLoading" style="color: #409eff;">
              <i class="el-icon-loading"></i> 正在搜索您的日记...
            </div>
            <div v-else-if="articleOptions.length === 0" style="color: #f56c6c;">
              <i class="el-icon-warning"></i> 暂无日记数据，请先发布一些包含图片或视频的旅游日记
            </div>
            <div v-else style="color: #67c23a;">
              找到 {{ articleOptions.length }} 篇日记，建议选择包含丰富媒体内容的日记
            </div>
          </div>
        </el-form-item>

        <!-- 动画风格 -->
        <el-form-item label="动画风格" prop="animationStyle">
          <el-radio-group v-model="form.animationStyle">
            <el-radio label="温馨">温馨治愈</el-radio>
            <el-radio label="活泼">活泼欢快</el-radio>
            <el-radio label="文艺">文艺清新</el-radio>
            <el-radio label="震撼">震撼大气</el-radio>
            <el-radio label="怀旧">怀旧复古</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 动画时长 -->
        <el-form-item label="动画时长" prop="duration">
          <el-radio-group v-model="form.duration">
            <el-radio label="短片">短片 (30-60秒)</el-radio>
            <el-radio label="中等">中等 (1-3分钟)</el-radio>
            <el-radio label="长片">长片 (3-5分钟)</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 重点元素 -->
        <el-form-item label="重点元素" prop="focusElements">
          <el-checkbox-group v-model="form.focusElements">
            <el-checkbox label="风景">自然风景</el-checkbox>
            <el-checkbox label="建筑">建筑特色</el-checkbox>
            <el-checkbox label="人物">人物活动</el-checkbox>
            <el-checkbox label="美食">美食展示</el-checkbox>
            <el-checkbox label="文化">文化元素</el-checkbox>
            <el-checkbox label="情感">情感表达</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 生成按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="generateAnimation"
            :loading="loading"
            size="large"
            style="width: 200px;"
          >
            <i class="el-icon-video-camera"></i>
            {{ getLoadingText() }}
          </el-button>
          <div v-if="loading" class="loading-tips">
            <p>AI正在分析您的日记内容和媒体文件...</p>
            <p>这可能需要1-3分钟，请耐心等待</p>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 日记预览 -->
    <div v-if="selectedArticle" class="article-preview">
      <h3>日记预览</h3>
      <div class="preview-content">
        <div class="article-info">
          <h4>{{ selectedArticle.title }}</h4>
          <div class="article-meta">
            <span><i class="el-icon-location"></i> {{ selectedArticle.location }}</span>
            <span><i class="el-icon-time"></i> {{ formatDate(selectedArticle.created_at) }}</span>
          </div>
          <div class="media-count">
            <span><i class="el-icon-picture"></i> {{ getMediaCount(selectedArticle).images }}张图片</span>
            <span><i class="el-icon-video-camera"></i> {{ getMediaCount(selectedArticle).videos }}个视频</span>
          </div>
          <div class="article-content">
            {{ selectedArticle.content?.substring(0, 200) }}{{ selectedArticle.content?.length > 200 ? '...' : '' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 生成结果 -->
    <div v-if="generatedAnimation" class="result-section">
      <div class="result-header">
        <h3>🎞️ 生成的旅游动画视频</h3>
        <div class="header-actions">
          <el-button @click="downloadVideo" type="primary" v-if="generatedAnimation.animation?.main_video?.url">
            <i class="el-icon-download"></i>
            下载视频
          </el-button>
          <el-button @click="shareAnimation" type="success">
            <i class="el-icon-share"></i>
            分享动画
          </el-button>
        </div>
      </div>

      <div class="animation-content">
        <div class="animation-title">
          <h2>{{ selectedArticle?.title }} - AI生成动画</h2>
          <p class="animation-meta">风格: {{ form.animationStyle }} · 时长: {{ form.duration }} · 类型: 单视频动画</p>
        </div>

        <!-- 主要视频展示 -->
        <div v-if="generatedAnimation.animation?.main_video" class="main-video-section">
          <h4>🎬 AI生成的旅游视频</h4>
          <div class="main-video-container">
            <div v-if="generatedAnimation.animation.main_video.status === 'completed'" class="video-player-wrapper">
              <video
                :src="generatedAnimation.animation.main_video.url"
                controls
                preload="metadata"
                class="main-travel-video"
                poster="/images/video-poster.jpg"
              >
                您的浏览器不支持视频播放
              </video>
              <div class="video-info-overlay">
                <div class="video-stats">
                  <span><i class="el-icon-time"></i> {{ generatedAnimation.animation.main_video.duration }}秒</span>
                  <span><i class="el-icon-cpu"></i> {{ generatedAnimation.animation.main_video.platform }}</span>
                  <span><i class="el-icon-check"></i> {{ generatedAnimation.animation.main_video.type }}</span>
                </div>
              </div>
            </div>
            <div v-else-if="generatedAnimation.animation.main_video.status === 'processing'" class="video-processing-state">
              <div class="processing-animation">
                <i class="el-icon-loading"></i>
              </div>
              <h5>AI正在生成您的专属旅游视频...</h5>
              <p>这可能需要2-5分钟，请耐心等待</p>
              <div class="progress-info">
                <p>任务ID: {{ generatedAnimation.animation.main_video.task_id }}</p>
                <div class="progress-bar" v-if="generatedAnimation.animation.main_video.progress">
                  <el-progress :percentage="generatedAnimation.animation.main_video.progress" :show-text="true"></el-progress>
                </div>
                <div class="status-actions">
                  <el-button @click="checkVideoStatus" type="primary" size="small">
                    <i class="el-icon-refresh"></i>
                    检查状态
                  </el-button>
                  <el-button @click="startAutoCheck" type="success" size="small" v-if="!autoCheckTimer">
                    <i class="el-icon-time"></i>
                    自动检查
                  </el-button>
                  <el-button @click="stopAutoCheck" type="warning" size="small" v-else>
                    <i class="el-icon-close"></i>
                    停止检查
                  </el-button>
                </div>
              </div>
            </div>
            <div v-else class="video-error-state">
              <i class="el-icon-warning"></i>
              <h5>视频生成遇到问题</h5>
              <p>{{ generatedAnimation.animation.main_video.error || '未知错误' }}</p>
              <el-button @click="retryVideoGeneration" type="primary">
                <i class="el-icon-refresh"></i>
                重新生成
              </el-button>
            </div>
          </div>
        </div>

        <!-- 内容分析结果 -->
        <div v-if="generatedAnimation.content_analysis" class="content-analysis-section">
          <h4>📊 内容分析结果</h4>
          <div class="analysis-grid">
            <div class="analysis-item">
              <div class="analysis-label">原始图片</div>
              <div class="analysis-value">{{ generatedAnimation.content_analysis.images?.length || 0 }}张</div>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">原始视频</div>
              <div class="analysis-value">{{ generatedAnimation.content_analysis.videos?.length || 0 }}个</div>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">关键词</div>
              <div class="analysis-value">{{ generatedAnimation.content_analysis.keywords?.length || 0 }}个</div>
            </div>
            <div class="analysis-item">
              <div class="analysis-label">情感倾向</div>
              <div class="analysis-value sentiment" :class="generatedAnimation.content_analysis.sentiment">
                {{ getSentimentText(generatedAnimation.content_analysis.sentiment) }}
              </div>
            </div>
          </div>

          <!-- 关键词展示 -->
          <div v-if="generatedAnimation.content_analysis.keywords?.length > 0" class="keywords-section">
            <h5>提取的关键词</h5>
            <div class="keywords-list">
              <el-tag
                v-for="keyword in generatedAnimation.content_analysis.keywords"
                :key="keyword"
                type="info"
                effect="plain"
                class="keyword-tag"
              >
                {{ keyword }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 动画脚本 -->
        <div v-if="generatedAnimation.animation_script" class="script-section">
          <h4>动画脚本</h4>
          <div class="script-overview">
            <div class="overview-item">
              <span class="label">总时长:</span>
              <span class="value">{{ generatedAnimation.animation_script.total_duration }}</span>
            </div>
            <div class="overview-item">
              <span class="label">场景数:</span>
              <span class="value">{{ generatedAnimation.animation_script.scenes?.length || 0 }}个</span>
            </div>
            <div class="overview-item">
              <span class="label">风格:</span>
              <span class="value">{{ generatedAnimation.animation_script.style }}</span>
            </div>
          </div>

          <div class="scenes-list">
            <div
              v-for="scene in generatedAnimation.animation_script.scenes"
              :key="scene.scene_number"
              class="scene-item"
            >
              <div class="scene-header">
                <span class="scene-number">场景 {{ scene.scene_number }}</span>
                <span class="scene-duration">{{ scene.duration }}</span>
              </div>
              <div class="scene-content">
                <div class="scene-description">
                  <h5>场景描述</h5>
                  <p>{{ scene.description }}</p>
                </div>
                <div class="scene-details">
                  <div class="detail-item">
                    <span class="detail-label">镜头角度:</span>
                    <span>{{ scene.camera_angle }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">配乐建议:</span>
                    <span>{{ scene.music_suggestion }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 扩写内容 -->
        <div v-if="generatedAnimation.expanded_content" class="expanded-section">
          <h4>扩写内容</h4>
          <div class="expanded-box">
            <p>{{ generatedAnimation.expanded_content }}</p>
          </div>
        </div>

        <!-- 视频文案 -->
        <div v-if="generatedAnimation.video_caption" class="caption-section">
          <h4>视频文案</h4>
          <div class="caption-box">
            <p>{{ generatedAnimation.video_caption }}</p>
          </div>
        </div>

        <!-- AI生成的媒体内容 -->
        <div v-if="generatedAnimation.generated_media" class="generated-media-section">
          <h4>AI生成的媒体内容</h4>

          <!-- AI生成的图片 -->
          <div v-if="generatedAnimation.generated_media.images && generatedAnimation.generated_media.images.length > 0" class="ai-images-section">
            <h5>AI生成图片</h5>
            <div class="ai-images-grid">
              <div
                v-for="image in generatedAnimation.generated_media.images"
                :key="image.scene_number"
                class="ai-image-item"
              >
                <div class="image-container">
                  <img
                    :src="image.url"
                    :alt="`场景${image.scene_number}生成图片`"
                    @error="handleImageError"
                    class="ai-generated-image"
                  />
                  <div class="image-overlay">
                    <span class="scene-tag">场景 {{ image.scene_number }}</span>
                  </div>
                </div>
                <div class="image-info">
                  <p class="image-description">{{ image.description }}</p>
                  <p class="image-prompt">提示词: {{ image.prompt }}</p>
                  <span class="image-type" :class="image.type">{{ image.type === 'ai_generated' ? 'AI生成' : '备用图片' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- AI生成的视频 -->
          <div v-if="generatedAnimation.generated_media.videos && generatedAnimation.generated_media.videos.length > 0" class="ai-videos-section">
            <h5>AI生成视频</h5>
            <div class="ai-videos-grid">
              <div
                v-for="video in generatedAnimation.generated_media.videos"
                :key="video.scene_number"
                class="ai-video-item"
              >
                <div class="video-container">
                  <div v-if="video.status === 'completed'" class="video-player">
                    <video
                      :src="video.url"
                      controls
                      preload="metadata"
                      class="ai-generated-video"
                    >
                      您的浏览器不支持视频播放
                    </video>
                  </div>
                  <div v-else-if="video.status === 'processing'" class="video-processing">
                    <div class="processing-icon">
                      <i class="el-icon-loading"></i>
                    </div>
                    <p>视频生成中...</p>
                    <p class="estimated-time">预计完成时间: {{ video.estimated_time }}</p>
                  </div>
                  <div v-else class="video-placeholder">
                    <i class="el-icon-video-camera"></i>
                    <p>视频生成失败</p>
                  </div>
                  <div class="video-overlay">
                    <span class="scene-tag">场景 {{ video.scene_number }}</span>
                  </div>
                </div>
                <div class="video-info">
                  <p class="video-prompt">提示词: {{ video.prompt }}</p>
                  <div class="video-meta">
                    <span class="video-status" :class="video.status">{{ getVideoStatusText(video.status) }}</span>
                    <span v-if="video.task_id" class="task-id">任务ID: {{ video.task_id }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 媒体使用建议 -->
        <div v-if="generatedAnimation.media_usage" class="media-section">
          <h4>媒体使用建议</h4>
          <div class="media-grid">
            <div
              v-for="media in generatedAnimation.media_usage"
              :key="media.usage_scene"
              class="media-item"
            >
              <div class="media-type">{{ media.media_type }}</div>
              <div class="media-scene">{{ media.usage_scene }}</div>
              <div class="media-effect">{{ media.effect }}</div>
            </div>
          </div>
        </div>

        <!-- 创作信息 -->
        <div v-if="generatedAnimation.creation_info" class="creation-info-section">
          <h4>创作信息</h4>
          <div class="creation-info-grid">
            <div class="info-item">
              <span class="info-label">创建时间:</span>
              <span class="info-value">{{ formatCreationTime(generatedAnimation.creation_info.created_at) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">AI模型:</span>
              <span class="info-value">{{ generatedAnimation.creation_info.ai_model || 'GPT-4' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">生成耗时:</span>
              <span class="info-value">{{ generatedAnimation.creation_info.generation_time || '约30秒' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">媒体生成:</span>
              <span class="info-value">{{ generatedAnimation.creation_info.media_generation_enabled ? '已启用' : '未启用' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { getCurrentUserId } from '@/utils/userUtils'

export default {
  name: 'SmartTravelAnimationGenerator',
  setup() {
    const formRef = ref(null)
    const loading = ref(false)
    const searchLoading = ref(false)
    const error = ref('')
    const generatedAnimation = ref(null)
    const articleOptions = ref([])
    const selectedArticle = ref(null)
    const autoCheckTimer = ref(null)

    const form = reactive({
      articleId: null,
      animationStyle: '温馨',
      duration: '中等',
      focusElements: ['风景', '情感']
    })

    const rules = {
      articleId: [
        { required: true, message: '请选择一篇旅游日记', trigger: 'change' }
      ]
    }



    // 搜索文章
    const searchArticles = async (query) => {
      searchLoading.value = true
      try {
        const userId = getCurrentUserId()
        if (!userId) {
          ElMessage.warning('请先登录后再使用此功能')
          return
        }

        const response = await axios.get(`http://localhost:5000/api/articles/user/${userId}`)

        if (response.data && response.data.code === 0) {
          let articles = response.data.data || []

          // 如果有搜索词，进行前端过滤
          if (query && query.trim()) {
            const searchQuery = query.trim().toLowerCase()
            articles = articles.filter(article =>
              (article.title && article.title.toLowerCase().includes(searchQuery)) ||
              (article.location && article.location.toLowerCase().includes(searchQuery))
            )
          }

          // 转换数据格式，确保包含所需字段
          articleOptions.value = articles.map(article => ({
            article_id: article.article_id,
            title: article.title || '无标题',
            location: article.location || article.location_name || '未知地点',
            content: article.content || '',
            created_at: article.created_at,
            image_url: article.image_url,
            image_url_2: article.image_url_2,
            image_url_3: article.image_url_3,
            image_url_4: article.image_url_4,
            image_url_5: article.image_url_5,
            image_url_6: article.image_url_6,
            video_url: article.video_url,
            video_url_2: article.video_url_2,
            video_url_3: article.video_url_3
          }))
        } else {
          articleOptions.value = []
        }
      } catch (error) {
        console.error('搜索文章失败:', error)
        articleOptions.value = []
        ElMessage.error('搜索文章失败，请稍后重试')
      } finally {
        searchLoading.value = false
      }
    }

    // 处理文章选择变化
    const handleArticleChange = (articleId) => {
      selectedArticle.value = articleOptions.value.find(
        article => article.article_id === articleId
      )
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 获取媒体文件数量
    const getMediaCount = (article) => {
      if (!article) return { images: 0, videos: 0 }

      let images = 0
      let videos = 0

      // 计算图片数量
      const imageFields = ['image_url', 'image_url_2', 'image_url_3', 'image_url_4', 'image_url_5', 'image_url_6']
      imageFields.forEach(field => {
        if (article[field]) images++
      })

      // 计算视频数量
      const videoFields = ['video_url', 'video_url_2', 'video_url_3']
      videoFields.forEach(field => {
        if (article[field]) videos++
      })

      return { images, videos }
    }

    // 获取加载文本
    const getLoadingText = () => {
      return loading.value ? '生成中...' : '生成动画'
    }

    // 生成动画脚本
    const generateAnimation = async () => {
      try {
        await formRef.value.validate()

        loading.value = true
        error.value = ''
        generatedAnimation.value = null

        const requestData = {
          article_id: form.articleId,
          animation_style: form.animationStyle,
          duration: form.duration,
          focus_elements: form.focusElements.join(', ')
        }

        const response = await axios.post('http://localhost:5000/api/ai/generate_travel_animation', requestData)

        if (response.data.code === 0) {
          generatedAnimation.value = response.data.data

          // 检查视频状态，如果是处理中则启动自动检查
          if (generatedAnimation.value?.animation?.main_video?.status === 'processing') {
            ElMessage.info('视频生成任务已创建，开始自动检查状态...')
            startAutoCheck()
          } else {
            ElMessage.success('旅游动画脚本生成成功！')
          }
        } else {
          error.value = response.data.message || '生成失败'
          ElMessage.error(error.value)
        }
      } catch (error) {
        console.error('生成动画失败:', error)
        error.value = '生成失败，请稍后重试'
        ElMessage.error('生成失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 处理图片加载错误
    const handleImageError = (event) => {
      event.target.src = '/images/placeholder-image.jpg'
      event.target.alt = '图片加载失败'
    }

    // 获取视频状态文本
    const getVideoStatusText = (status) => {
      const statusMap = {
        'completed': '已完成',
        'processing': '生成中',
        'failed': '生成失败',
        'pending': '等待中'
      }
      return statusMap[status] || '未知状态'
    }

    // 格式化创建时间
    const formatCreationTime = (timestamp) => {
      if (!timestamp) return '未知'
      return new Date(timestamp).toLocaleString('zh-CN')
    }

    // 复制到剪贴板
    const copyToClipboard = async () => {
      try {
        let scriptText = `${selectedArticle.value?.title} - 动画脚本\n\n`

        if (generatedAnimation.value.animation_script) {
          scriptText += `总时长: ${generatedAnimation.value.animation_script.total_duration}\n`
          scriptText += `风格: ${generatedAnimation.value.animation_script.style}\n\n`

          generatedAnimation.value.animation_script.scenes?.forEach(scene => {
            scriptText += `场景 ${scene.scene_number} (${scene.duration}):\n`
            scriptText += `${scene.description}\n`
            scriptText += `镜头角度: ${scene.camera_angle}\n`
            scriptText += `配乐建议: ${scene.music_suggestion}\n\n`
          })
        }

        if (generatedAnimation.value.expanded_content) {
          scriptText += `扩写内容:\n${generatedAnimation.value.expanded_content}\n\n`
        }

        if (generatedAnimation.value.video_caption) {
          scriptText += `视频文案:\n${generatedAnimation.value.video_caption}\n\n`
        }

        // 添加AI生成媒体信息
        if (generatedAnimation.value.generated_media) {
          if (generatedAnimation.value.generated_media.images?.length > 0) {
            scriptText += `AI生成图片 (${generatedAnimation.value.generated_media.images.length}张):\n`
            generatedAnimation.value.generated_media.images.forEach(image => {
              scriptText += `- 场景${image.scene_number}: ${image.description}\n`
            })
            scriptText += '\n'
          }

          if (generatedAnimation.value.generated_media.videos?.length > 0) {
            scriptText += `AI生成视频 (${generatedAnimation.value.generated_media.videos.length}个):\n`
            generatedAnimation.value.generated_media.videos.forEach(video => {
              scriptText += `- 场景${video.scene_number}: ${getVideoStatusText(video.status)}\n`
            })
            scriptText += '\n'
          }
        }

        await navigator.clipboard.writeText(scriptText)
        ElMessage.success('动画脚本已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        ElMessage.error('复制失败')
      }
    }

    // 获取情感倾向文本
    const getSentimentText = (sentiment) => {
      const sentimentMap = {
        'positive': '积极',
        'negative': '消极',
        'neutral': '中性'
      }
      return sentimentMap[sentiment] || '未知'
    }

    // 下载视频
    const downloadVideo = () => {
      if (generatedAnimation.value?.animation?.main_video?.url) {
        const link = document.createElement('a')
        link.href = generatedAnimation.value.animation.main_video.url
        link.download = `${selectedArticle.value?.title || '旅游动画'}.mp4`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        ElMessage.success('开始下载视频')
      } else {
        ElMessage.error('视频URL不可用')
      }
    }

    // 分享动画
    const shareAnimation = () => {
      if (navigator.share && generatedAnimation.value?.animation?.main_video?.url) {
        navigator.share({
          title: `${selectedArticle.value?.title} - AI生成旅游动画`,
          text: `查看我用AI生成的旅游动画：${selectedArticle.value?.title}`,
          url: generatedAnimation.value.animation.main_video.url
        }).then(() => {
          ElMessage.success('分享成功')
        }).catch(() => {
          copyShareLink()
        })
      } else {
        copyShareLink()
      }
    }

    // 复制分享链接
    const copyShareLink = async () => {
      try {
        const shareText = `查看我用AI生成的旅游动画：${selectedArticle.value?.title}\n${generatedAnimation.value?.animation?.main_video?.url || window.location.href}`
        await navigator.clipboard.writeText(shareText)
        ElMessage.success('分享链接已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败，请手动复制')
      }
    }

    // 检查视频状态
    const checkVideoStatus = async () => {
      if (!generatedAnimation.value?.animation?.main_video?.task_id) {
        ElMessage.error('无效的任务ID')
        return
      }

      try {
        const response = await axios.get(`http://localhost:5000/api/ai/query_aigc_video_status/${generatedAnimation.value.animation.main_video.task_id}`)

        if (response.data.code === 0) {
          const status = response.data.data
          generatedAnimation.value.animation.main_video.status = status.status

          if (status.video_url) {
            generatedAnimation.value.animation.main_video.url = status.video_url
          }

          if (status.progress) {
            generatedAnimation.value.animation.main_video.progress = status.progress
          }

          // 如果视频完成或失败，停止自动检查
          if (status.status === 'completed') {
            stopAutoCheck()
            ElMessage.success('🎉 视频生成完成！可以播放了')
          } else if (status.status === 'failed') {
            stopAutoCheck()
            ElMessage.error('❌ 视频生成失败')
          } else {
            ElMessage.info(`📹 视频状态: ${getVideoStatusText(status.status)}`)
          }
        } else {
          ElMessage.error('查询状态失败')
        }
      } catch (error) {
        console.error('查询视频状态失败:', error)
        ElMessage.error('查询状态失败，请稍后重试')
      }
    }

    // 重新生成视频
    const retryVideoGeneration = async () => {
      ElMessage.info('重新生成功能开发中...')
    }

    // 开始自动检查
    const startAutoCheck = () => {
      if (autoCheckTimer.value) return

      ElMessage.info('开始自动检查视频状态，每30秒检查一次')
      autoCheckTimer.value = setInterval(() => {
        checkVideoStatus()
      }, 30000) // 每30秒检查一次
    }

    // 停止自动检查
    const stopAutoCheck = () => {
      if (autoCheckTimer.value) {
        clearInterval(autoCheckTimer.value)
        autoCheckTimer.value = null
        ElMessage.info('已停止自动检查')
      }
    }

    // 组件卸载时清理定时器
    onUnmounted(() => {
      stopAutoCheck()
    })

    // 初始化时加载用户文章
    onMounted(() => {
      searchArticles('')
    })

    return {
      formRef,
      form,
      rules,
      loading,
      searchLoading,
      error,
      generatedAnimation,
      articleOptions,
      selectedArticle,
      searchArticles,
      handleArticleChange,
      formatDate,
      getMediaCount,
      getLoadingText,
      generateAnimation,
      handleImageError,
      getVideoStatusText,
      formatCreationTime,
      copyToClipboard,
      getSentimentText,
      downloadVideo,
      shareAnimation,
      checkVideoStatus,
      retryVideoGeneration,
      autoCheckTimer,
      startAutoCheck,
      stopAutoCheck
    }
  }
}
</script>

<style scoped>
.smart-travel-animation-generator {
  max-width: 1200px;
  margin: 0 auto;
}

.generator-form {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.form-tip {
  margin-top: 8px;
  font-size: 12px;
}

.loading-tips {
  margin-top: 15px;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.loading-tips p {
  margin: 5px 0;
  color: #1976d2;
  font-size: 0.9rem;
}

.article-preview {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
}

.article-preview h3 {
  color: #333;
  margin-bottom: 20px;
}

.preview-content {
  background: white;
  padding: 20px;
  border-radius: 10px;
}

.article-info h4 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.article-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.media-count {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #667eea;
  font-weight: 600;
}

.media-count span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.article-content {
  color: #666;
  line-height: 1.6;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.result-section {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  margin: 0;
  font-size: 1.4rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-actions .el-button {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.header-actions .el-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.animation-content {
  padding: 30px;
}

.animation-title h2 {
  color: #333;
  margin-bottom: 10px;
}

.animation-meta {
  color: #666;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.script-section {
  margin-bottom: 30px;
}

.script-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.script-overview {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 25px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.overview-item .label {
  font-size: 0.9rem;
  color: #666;
}

.overview-item .value {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.scenes-list {
  display: grid;
  gap: 20px;
}

.scene-item {
  border: 1px solid #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.scene-header {
  background: #667eea;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scene-number {
  font-weight: 600;
  font-size: 1.1rem;
}

.scene-duration {
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
}

.scene-content {
  padding: 20px;
}

.scene-description h5 {
  color: #333;
  margin-bottom: 10px;
}

.scene-description p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.scene-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  gap: 10px;
}

.detail-label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.expanded-section,
.caption-section {
  margin-bottom: 30px;
}

.expanded-section h4,
.caption-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.expanded-box,
.caption-box {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.expanded-box p,
.caption-box p {
  color: #666;
  line-height: 1.7;
  margin: 0;
}

/* AI生成媒体内容样式 */
.generated-media-section {
  margin-bottom: 30px;
}

.generated-media-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.generated-media-section h5 {
  color: #667eea;
  margin: 20px 0 15px 0;
  font-size: 1.1rem;
}

/* AI生成图片样式 */
.ai-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.ai-image-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.ai-image-item:hover {
  transform: translateY(-2px);
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.ai-generated-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ai-image-item:hover .ai-generated-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
}

.scene-tag {
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.image-info {
  padding: 15px;
}

.image-description {
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.image-prompt {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
  line-height: 1.4;
}

.image-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.image-type.ai_generated {
  background: #e8f5e8;
  color: #2e7d32;
}

.image-type.fallback {
  background: #fff3e0;
  color: #f57c00;
}

/* 主要视频样式 */
.main-video-section {
  margin-bottom: 30px;
}

.main-video-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.main-video-container {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  position: relative;
}

.video-player-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.main-travel-video {
  width: 100%;
  height: auto;
  min-height: 300px;
  background: #000;
  border-radius: 12px;
}

.video-info-overlay {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.video-stats {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
}

.video-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.video-processing-state,
.video-error-state {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  border: 2px dashed #e0e0e0;
}

.processing-animation {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 20px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.video-processing-state h5,
.video-error-state h5 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.video-processing-state p,
.video-error-state p {
  color: #666;
  margin-bottom: 20px;
}

.progress-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.progress-info p {
  font-family: monospace;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

/* 内容分析样式 */
.content-analysis-section {
  margin-bottom: 30px;
}

.content-analysis-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.analysis-item {
  background: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.analysis-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
}

.analysis-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.analysis-value.sentiment {
  font-size: 1.2rem;
}

.analysis-value.sentiment.positive {
  color: #4caf50;
}

.analysis-value.sentiment.negative {
  color: #f44336;
}

.analysis-value.sentiment.neutral {
  color: #ff9800;
}

.keywords-section {
  margin-top: 20px;
}

.keywords-section h5 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.keyword-tag {
  margin: 0;
  font-size: 0.9rem;
  padding: 6px 12px;
  border-radius: 20px;
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

/* AI生成视频样式 */
.ai-videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.ai-video-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-container {
  position: relative;
  width: 100%;
  height: 200px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-generated-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-processing,
.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  text-align: center;
}

.processing-icon {
  font-size: 2rem;
  color: #667eea;
  margin-bottom: 10px;
}

.processing-icon i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.estimated-time {
  font-size: 0.8rem;
  color: #999;
  margin-top: 5px;
}

.video-placeholder i {
  font-size: 2rem;
  color: #ccc;
  margin-bottom: 10px;
}

.video-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
}

.video-info {
  padding: 15px;
}

.video-prompt {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
  line-height: 1.4;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.video-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.video-status.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.video-status.processing {
  background: #e3f2fd;
  color: #1976d2;
}

.video-status.failed {
  background: #ffebee;
  color: #d32f2f;
}

.video-status.pending {
  background: #fff3e0;
  color: #f57c00;
}

.task-id {
  font-size: 0.8rem;
  color: #999;
  font-family: monospace;
}

/* 创作信息样式 */
.creation-info-section {
  margin-bottom: 30px;
}

.creation-info-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.creation-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.info-value {
  color: #333;
  font-weight: 600;
}

.media-section h4 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.media-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.media-type {
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8px;
}

.media-scene {
  color: #333;
  margin-bottom: 5px;
}

.media-effect {
  color: #666;
  font-size: 0.9rem;
}

.error-section {
  margin-top: 20px;
}

@media (max-width: 768px) {
  .article-meta {
    flex-direction: column;
    gap: 10px;
  }

  .media-count {
    flex-direction: column;
    gap: 10px;
  }

  .script-overview {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .scene-details {
    grid-template-columns: 1fr;
  }

  .media-grid {
    grid-template-columns: 1fr;
  }

  .ai-images-grid {
    grid-template-columns: 1fr;
  }

  .ai-videos-grid {
    grid-template-columns: 1fr;
  }

  .creation-info-grid {
    grid-template-columns: 1fr;
  }

  .video-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .result-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
