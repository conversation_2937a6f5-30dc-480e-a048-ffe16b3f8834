"""
简化版基于日记内容的旅游动画生成服务

该模块提供一个轻量级的动画生成方案，不依赖复杂的视频处理库
主要功能：
1. 分析日记内容和媒体资源
2. 生成动画脚本和故事板
3. 创建HTML5/CSS3动画预览
4. 提供动画配置和自定义选项
"""

import os
import json
import uuid
from typing import List, Dict, Optional
from datetime import datetime
from models.article import Article
from services.ai_service import AIService

class SimpleDiaryAnimationService:
    """简化版日记动画生成服务"""
    
    def __init__(self):
        """初始化服务"""
        self.ai_service = AIService()
        self.output_dir = os.path.join('static', 'diary_animations')
        
        # 确保目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 动画配置
        self.config = {
            'default_duration': 30,  # 总时长30秒
            'image_duration': 4.0,   # 图片显示时长
            'text_duration': 3.0,    # 文字显示时长
            'transition_duration': 1.0,  # 转场时长
        }
    
    def generate_diary_animation_config(self, article_id: int, style: str = 'cinematic') -> Optional[Dict]:
        """
        生成日记动画配置
        
        Args:
            article_id: 文章ID
            style: 动画风格
            
        Returns:
            动画配置字典，包含所有必要的信息用于前端渲染
        """
        try:
            # 获取文章数据
            article = Article.query.get(article_id)
            if not article:
                return None
            
            # 提取文章内容
            content_data = self._extract_article_content(article)
            if not content_data:
                return None
            
            # 生成动画脚本
            animation_script = self._generate_animation_script(content_data, style)
            
            # 创建动画配置
            animation_config = {
                'id': f"diary_animation_{article_id}_{uuid.uuid4().hex[:8]}",
                'article_id': article_id,
                'title': content_data['title'],
                'style': style,
                'duration': self._calculate_total_duration(animation_script),
                'scenes': animation_script.get('scenes', []),
                'media': {
                    'images': content_data['images'],
                    'videos': content_data['videos']
                },
                'metadata': {
                    'location': content_data['location'],
                    'created_at': content_data['created_at'].isoformat() if content_data['created_at'] else None,
                    'user_id': content_data['user_id'],
                    'content_length': len(content_data['content'])
                },
                'config': self.config,
                'preview_url': self._generate_preview_url(article_id, style)
            }
            
            # 保存配置文件
            config_filename = f"animation_config_{article_id}_{uuid.uuid4().hex[:8]}.json"
            config_path = os.path.join(self.output_dir, config_filename)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(animation_config, f, ensure_ascii=False, indent=2)
            
            animation_config['config_url'] = f"/static/diary_animations/{config_filename}"
            
            return animation_config
            
        except Exception as e:
            print(f"Error generating diary animation config: {e}")
            return None
    
    def _extract_article_content(self, article: Article) -> Optional[Dict]:
        """提取文章内容"""
        try:
            # 解压文章内容
            content = article.content
            if article.huffman_codes:
                from services.article_service import ArticleService
                service = ArticleService()
                huffman_codes = json.loads(article.huffman_codes)
                content = service.decompress_text(article.content, huffman_codes)
            
            if isinstance(content, bytes):
                content = content.decode('utf-8')
            
            # 收集所有图片URL
            images = []
            for i in range(1, 7):
                if i == 1:
                    url = article.image_url
                else:
                    url = getattr(article, f'image_url_{i}', None)
                if url:
                    images.append(url)
            
            # 收集所有视频URL
            videos = []
            for i in range(1, 4):
                if i == 1:
                    url = article.video_url
                else:
                    url = getattr(article, f'video_url_{i}', None)
                if url:
                    videos.append(url)
            
            return {
                'article_id': article.article_id,
                'title': article.title,
                'content': content,
                'location': article.location,
                'images': images,
                'videos': videos,
                'created_at': article.created_at,
                'user_id': article.user_id
            }
            
        except Exception as e:
            print(f"Error extracting article content: {e}")
            return None
    
    def _generate_animation_script(self, content_data: Dict, style: str) -> Dict:
        """生成动画脚本"""
        try:
            # 构建AI提示词
            prompt = f"""
            请为以下旅游日记生成一个{style}风格的动画脚本：
            
            标题: {content_data['title']}
            地点: {content_data['location']}
            内容摘要: {content_data['content'][:500]}...
            图片数量: {len(content_data['images'])}
            视频数量: {len(content_data['videos'])}
            
            请生成一个动画脚本，包含以下场景类型：
            1. title_scene - 标题场景
            2. location_scene - 地点介绍
            3. image_scene - 图片展示
            4. video_scene - 视频播放
            5. text_scene - 文字叙述
            6. ending_scene - 结尾场景
            
            请以JSON格式返回，包含scenes数组，每个scene包含：
            - type: 场景类型
            - content: 显示内容
            - duration: 持续时间（秒）
            - animation: 动画效果
            - position: 位置信息
            
            只返回JSON，不要其他文字。
            """
            
            try:
                ai_response = self.ai_service.generate_text(prompt, max_tokens=1000)
                script = json.loads(ai_response)
                return script
            except (json.JSONDecodeError, Exception):
                # 如果AI生成失败，使用默认脚本
                return self._generate_default_script(content_data, style)
                
        except Exception as e:
            print(f"Error generating animation script: {e}")
            return self._generate_default_script(content_data, style)
    
    def _generate_default_script(self, content_data: Dict, style: str) -> Dict:
        """生成默认动画脚本"""
        scenes = []
        
        # 开场标题
        scenes.append({
            'type': 'title_scene',
            'content': content_data['title'],
            'duration': 3.0,
            'animation': 'fadeIn',
            'position': 'center',
            'style': {
                'fontSize': '48px',
                'color': '#ffffff',
                'textShadow': '2px 2px 4px rgba(0,0,0,0.5)'
            }
        })
        
        # 地点介绍
        if content_data['location']:
            scenes.append({
                'type': 'location_scene',
                'content': f"📍 {content_data['location']}",
                'duration': 2.5,
                'animation': 'slideUp',
                'position': 'bottom',
                'style': {
                    'fontSize': '32px',
                    'color': '#ffdd44'
                }
            })
        
        # 图片展示
        for i, image_url in enumerate(content_data['images']):
            scenes.append({
                'type': 'image_scene',
                'content': image_url,
                'duration': 4.0,
                'animation': 'crossfade' if i > 0 else 'fadeIn',
                'position': 'center',
                'style': {
                    'width': '100%',
                    'height': '100%',
                    'objectFit': 'cover'
                }
            })
        
        # 视频展示
        for video_url in content_data['videos']:
            scenes.append({
                'type': 'video_scene',
                'content': video_url,
                'duration': 6.0,
                'animation': 'fadeIn',
                'position': 'center',
                'style': {
                    'width': '100%',
                    'height': '100%'
                }
            })
        
        # 内容文字（提取关键句子）
        key_sentences = self._extract_key_sentences(content_data['content'])
        for sentence in key_sentences[:3]:  # 最多3个关键句子
            scenes.append({
                'type': 'text_scene',
                'content': sentence,
                'duration': 3.5,
                'animation': 'typewriter',
                'position': 'center',
                'style': {
                    'fontSize': '28px',
                    'color': '#ffffff',
                    'textAlign': 'center',
                    'padding': '20px'
                }
            })
        
        # 结尾
        scenes.append({
            'type': 'ending_scene',
            'content': '感谢观看 ✨',
            'duration': 2.0,
            'animation': 'fadeIn',
            'position': 'center',
            'style': {
                'fontSize': '36px',
                'color': '#ffdd44'
            }
        })
        
        return {
            'scenes': scenes,
            'style': style,
            'total_scenes': len(scenes)
        }
    
    def _extract_key_sentences(self, content: str) -> List[str]:
        """提取关键句子"""
        try:
            # 简单的句子分割和筛选
            sentences = content.split('。')
            key_sentences = []
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10 and len(sentence) < 100:
                    # 过滤掉太短或太长的句子
                    key_sentences.append(sentence + '。')
                    if len(key_sentences) >= 5:
                        break
            
            return key_sentences
            
        except Exception as e:
            print(f"Error extracting key sentences: {e}")
            return ["这是一次美好的旅行。", "留下了难忘的回忆。"]
    
    def _calculate_total_duration(self, script: Dict) -> float:
        """计算总时长"""
        try:
            scenes = script.get('scenes', [])
            total_duration = sum(scene.get('duration', 3.0) for scene in scenes)
            return total_duration
        except:
            return 30.0  # 默认30秒
    
    def _generate_preview_url(self, article_id: int, style: str) -> str:
        """生成预览URL"""
        return f"/api/ai/preview_diary_animation?article_id={article_id}&style={style}"
    
    def generate_html_preview(self, article_id: int, style: str) -> Optional[str]:
        """生成HTML预览页面"""
        try:
            config = self.generate_diary_animation_config(article_id, style)
            if not config:
                return None
            
            # 生成HTML预览页面
            html_content = self._create_html_preview(config)
            
            # 保存HTML文件
            html_filename = f"preview_{article_id}_{uuid.uuid4().hex[:8]}.html"
            html_path = os.path.join(self.output_dir, html_filename)
            
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return f"/static/diary_animations/{html_filename}"
            
        except Exception as e:
            print(f"Error generating HTML preview: {e}")
            return None
    
    def _create_html_preview(self, config: Dict) -> str:
        """创建HTML预览内容"""
        scenes_json = json.dumps(config['scenes'], ensure_ascii=False)
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{config['title']} - 动画预览</title>
            <style>
                body {{ margin: 0; padding: 0; background: #000; font-family: Arial, sans-serif; }}
                .animation-container {{ 
                    width: 100vw; height: 100vh; position: relative; overflow: hidden;
                    display: flex; align-items: center; justify-content: center;
                }}
                .scene {{ 
                    position: absolute; width: 100%; height: 100%;
                    display: flex; align-items: center; justify-content: center;
                    opacity: 0; transition: opacity 1s ease-in-out;
                }}
                .scene.active {{ opacity: 1; }}
                .scene img {{ max-width: 100%; max-height: 100%; object-fit: cover; }}
                .scene video {{ max-width: 100%; max-height: 100%; }}
                .scene .text {{ 
                    text-align: center; padding: 20px; max-width: 80%;
                    background: rgba(0,0,0,0.7); border-radius: 10px;
                }}
                .controls {{ 
                    position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%);
                    background: rgba(0,0,0,0.8); padding: 10px; border-radius: 5px;
                }}
                .controls button {{ 
                    margin: 0 5px; padding: 5px 10px; background: #007bff; 
                    color: white; border: none; border-radius: 3px; cursor: pointer;
                }}
            </style>
        </head>
        <body>
            <div class="animation-container" id="animationContainer">
                <!-- 场景将通过JavaScript动态生成 -->
            </div>
            
            <div class="controls">
                <button onclick="playAnimation()">播放</button>
                <button onclick="pauseAnimation()">暂停</button>
                <button onclick="resetAnimation()">重置</button>
                <span id="progress">0 / {len(config['scenes'])}</span>
            </div>
            
            <script>
                const scenes = {scenes_json};
                let currentScene = 0;
                let isPlaying = false;
                let animationTimer = null;
                
                function initializeAnimation() {{
                    const container = document.getElementById('animationContainer');
                    container.innerHTML = '';
                    
                    scenes.forEach((scene, index) => {{
                        const sceneElement = document.createElement('div');
                        sceneElement.className = 'scene';
                        sceneElement.id = `scene-${{index}}`;
                        
                        if (scene.type === 'image_scene') {{
                            sceneElement.innerHTML = `<img src="${{scene.content}}" alt="Scene ${{index}}">`;
                        }} else if (scene.type === 'video_scene') {{
                            sceneElement.innerHTML = `<video src="${{scene.content}}" autoplay muted></video>`;
                        }} else {{
                            sceneElement.innerHTML = `<div class="text" style="${{Object.entries(scene.style || {{}}).map(([k,v]) => k + ':' + v).join(';')}}">${{scene.content}}</div>`;
                        }}
                        
                        container.appendChild(sceneElement);
                    }});
                }}
                
                function showScene(index) {{
                    document.querySelectorAll('.scene').forEach(scene => scene.classList.remove('active'));
                    const scene = document.getElementById(`scene-${{index}}`);
                    if (scene) {{
                        scene.classList.add('active');
                        document.getElementById('progress').textContent = `${{index + 1}} / ${{scenes.length}}`;
                    }}
                }}
                
                function playAnimation() {{
                    if (isPlaying) return;
                    isPlaying = true;
                    
                    function nextScene() {{
                        if (currentScene < scenes.length) {{
                            showScene(currentScene);
                            const duration = scenes[currentScene].duration * 1000;
                            currentScene++;
                            
                            animationTimer = setTimeout(nextScene, duration);
                        }} else {{
                            isPlaying = false;
                            currentScene = 0;
                        }}
                    }}
                    
                    nextScene();
                }}
                
                function pauseAnimation() {{
                    isPlaying = false;
                    if (animationTimer) {{
                        clearTimeout(animationTimer);
                        animationTimer = null;
                    }}
                }}
                
                function resetAnimation() {{
                    pauseAnimation();
                    currentScene = 0;
                    showScene(0);
                }}
                
                // 初始化
                initializeAnimation();
                showScene(0);
            </script>
        </body>
        </html>
        """
        
        return html_template
