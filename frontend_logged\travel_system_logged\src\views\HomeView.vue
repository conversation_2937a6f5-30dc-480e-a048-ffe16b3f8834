<template>
    <div class="app-container">
      <!-- 主体内容 -->

      <main class="main-content">

        <!-- 轮播图区域 -->
     <div class="carousel">
          <el-carousel :interval="5000" arrow="always">
            <el-carousel-item v-for="(item, index) in carouselList" :key="index">
              <img :src="item.img" :alt="item.title" class="carousel-img" />
              <h3 class="carousel-title">{{ item.title }}</h3>
            </el-carousel-item>
          </el-carousel>
        </div>

        <!-- 功能卡片区域 -->
        <div class="features">
          <h2 class="section-title">我们的服务</h2>
          <div class="feature-cards-grid">
            <!-- 第一行卡片 -->
            <div class="feature-cards-row">
              <el-card class="feature-card" v-for="feature in features.slice(0, 3)" :key="feature.id">
                <div class="icon-container">
                  <component :is="feature.icon" />
                </div>
                <h3>{{ feature.title }}</h3>
                <p>{{ feature.description }}</p>
                <el-button type="primary" @click="goTo(feature.route)">
                  立即使用
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </el-card>
            </div>

            <!-- 第二行卡片 -->
            <div class="feature-cards-row">
              <el-card class="feature-card" v-for="feature in features.slice(3, 6)" :key="feature.id">
                <div class="icon-container">
                  <component :is="feature.icon" />
                </div>
                <h3>{{ feature.title }}</h3>
                <p>{{ feature.description }}</p>
                <el-button type="primary" @click="goTo(feature.route)">
                  立即使用
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </el-card>
            </div>
          </div>
        </div>
      </main>

      <!-- 底部信息 -->
      <footer class="footer">
        <!-- <p>© 2024 旅行助手. 保留所有权利</p> -->
      </footer>
    </div>
  </template>

  <script setup>
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    Star,
    Location,
    Position,
    Ticket,
    Notebook,
    KnifeFork,
    ArrowRight
  } from '@element-plus/icons-vue';

  const router = useRouter();

  const carouselList = ref([
    {
      title: '热门景点推荐',
      description: '发现全球热门旅游景点',
      img: require('@/assets/forbidden_city.jpg'),
    },
    {
      img: require('@/assets/special_route.jpg'),
      title: '特色路线精选',
      description: '专家推荐的最佳旅行路线'
    },
    {
      img: require('@/assets/map_example.jpg'),
      title: '最优路径规划',
      description: '智能导航为您规划最佳出行路线'
    }
  ]);

  const features = ref([
    {
      id: 1,
      icon: Star,
      title: '智能推荐',
      description: '根据您的偏好智能推荐最佳景点和旅行体验',
      route: '/recommend'
    },
    {
      id: 2,
      icon: Location,
      title: '路线规划',
      description: '智能算法规划最优旅行路线，节省时间与精力',
      route: '/route'
    },
    {
      id: 3,
      icon: Position,
      title: '场所查询',
      description: '实时查询景区开放信息、设施与周边服务',
      route: '/search'
    },
    {
      id: 4,
      icon: Ticket,
      title: '机票预订',
      description: '便捷预订国内外航班，享受专属优惠价格',
      route: '/flight'
    },
    {
      id: 5,
      icon: KnifeFork,
      title: '美食推荐',
      description: '发现当地特色美食，享受舌尖上的旅行',
      route: '/food'
    },
    {
      id: 6,
      icon: Notebook,
      title: '游记社区',
      description: '分享精彩旅行故事，获取真实旅行体验',
      route: '/diary'
    }
  ]);

  // 导航菜单已移至App.vue

  const goTo = (route) => {
    router.push(route);
  };
  </script>

  <style scoped>
   .app-container {
    min-height: 100vh;
  }

  /* 导航栏样式已移至App.vue */

  .features {
    margin-top: 3rem;
    padding: 3rem 2rem;
    max-width: 1500px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9) 0%, rgba(229, 240, 250, 0.9) 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
      0 20px 50px rgba(0, 0, 0, 0.05),
      0 10px 30px rgba(31, 119, 201, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
  }

  /* 主背景图案 */
  .features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/assets/map_example.jpg') no-repeat center center;
    background-size: cover;
    opacity: 0.07;
    filter: blur(6px);
    z-index: 0;
    mix-blend-mode: overlay;
  }

  /* 添加装饰性图案 */
  .features::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 10% 20%, rgba(64, 158, 255, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 90% 80%, rgba(100, 181, 246, 0.08) 0%, transparent 50%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 80%);
    z-index: 0;
    pointer-events: none;
  }

  .feature-cards-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding-top: 10px;
  }

  .feature-cards-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
    position: relative;
    z-index: 1;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
    letter-spacing: -0.02em;
  }

  .section-title:after {
    content: '';
    display: block;
    width: 100px;
    height: 5px;
    background: linear-gradient(90deg, #409EFF, #64B5F6, #81D4FA);
    margin: 1rem auto 3rem;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(64, 158, 255, 0.2);
  }

  .feature-card {
    flex: 1;
    width: calc(33.33% - 14px); /* 三等分减去间距 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem;
    transition: all 0.3s ease;
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
    z-index: 0;
  }

  .feature-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(64, 158, 255, 0.08) 100%);
    z-index: 0;
  }

  .feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    background-color: rgba(255, 255, 255, 0.95);
  }

  .feature-card > * {
    position: relative;
    z-index: 1;
  }

  .icon-container {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(100, 181, 246, 0.1) 100%);
    position: relative;
    box-shadow: 0 4px 15px rgba(64, 158, 255, 0.1);
  }

  .icon-container::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }

  .feature-card:hover .icon-container {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.2);
  }

  .feature-card .icon-container svg {
    font-size: 2.8rem;
    width: 2.8rem;
    height: 2.8rem;
    color: #409EFF;
    position: relative;
    z-index: 1;
  }

  .feature-card .el-button .el-icon {
    margin-left: 5px;
    vertical-align: middle;
  }

  .feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
    text-align: center;
    font-weight: 600;
  }

  .feature-card p {
    color: #666;
    margin-bottom: 1.8rem;
    flex-grow: 1;
    font-size: 1rem;
    line-height: 1.6;
    text-align: center;
  }

  .feature-card .el-button {
    width: auto;
    padding: 10px 24px;
    font-size: 1rem;
    border-radius: 6px;
    margin: 0 auto;
    display: block;
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.3s ease;
  }

  .feature-card .el-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(64, 158, 255, 0.3);
  }

  .footer {
    background-color: #f5f7fa;
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
  }

  .main-content {
    padding: 2rem 2rem 0;
    position: relative;
    flex:1;
  }

  .carousel {
    height: 50vh !important;
    min-height: 600px !important;
    position: relative;
    overflow: hidden;
    flex:1;
  }

  .carousel-img {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  .carousel-title {
    position: absolute;
    bottom: 2rem;
    left: 2rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    font-size: 2rem;
    z-index: 10;
  }
  :deep(.el-carousel__container) {
    height: 600px !important;
  }

  .el-carousel__item {
    height: 100% !important;
  }

  .header-logo {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}

/* 导航栏相关样式已移至App.vue */

/* 响应式调整 */
@media (max-width: 1200px) {
  .feature-cards-row {
    flex-wrap: wrap;
    justify-content: center;
  }

  .feature-card {
    width: calc(50% - 20px); /* 两列布局 */
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .feature-card {
    width: 100%;
    max-width: 400px;
    margin-bottom: 20px;
  }

  .feature-cards-row {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .feature-cards-grid {
    gap: 15px;
  }

  .feature-cards-row {
    gap: 15px;
  }

  .feature-card {
    width: 100%;
    max-width: 320px;
  }
}

</style>