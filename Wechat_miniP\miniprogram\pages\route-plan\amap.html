<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>高德地图 - 路线规划</title>
    <script src="https://webapi.amap.com/maps?v=2.0&key=9ac93278af733b48f3c31aacb870082f&plugin=AMap.Driving,AMap.Walking,AMap.Riding,AMap.Transfer"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        #map-container {
            width: 100%;
            height: 100vh;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            color: #666;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="loading" id="loading">地图加载中...</div>
    <div id="map-container"></div>

    <script>
        let map = null;
        let markers = [];
        let polylines = [];
        
        // 初始化地图
        function initMap() {
            try {
                map = new AMap.Map('map-container', {
                    center: [116.3588, 39.9615], // 北京邮电大学坐标
                    zoom: 17,
                    viewMode: '2D',
                    mapStyle: 'amap://styles/normal'
                });

                // 添加控件
                map.addControl(new AMap.ToolBar());
                map.addControl(new AMap.Scale());

                // 隐藏加载提示
                document.getElementById('loading').style.display = 'none';

                // 地图点击事件
                map.on('click', function(e) {
                    sendMessageToMiniProgram({
                        type: 'mapClick',
                        data: {
                            longitude: e.lnglat.lng,
                            latitude: e.lnglat.lat
                        }
                    });
                });

                // 通知小程序地图已加载完成
                sendMessageToMiniProgram({
                    type: 'mapReady',
                    data: {}
                });

                console.log('高德地图初始化成功');
            } catch (error) {
                console.error('地图初始化失败:', error);
                document.getElementById('loading').textContent = '地图加载失败';
            }
        }

        // 向小程序发送消息
        function sendMessageToMiniProgram(message) {
            if (window.wx && window.wx.miniProgram) {
                wx.miniProgram.postMessage({
                    data: message
                });
            }
        }

        // 接收小程序消息
        function receiveMessage(data) {
            if (!map) return;

            switch (data.type) {
                case 'setCenter':
                    map.setCenter([data.longitude, data.latitude]);
                    break;
                    
                case 'setZoom':
                    map.setZoom(data.zoom);
                    break;
                    
                case 'addMarkers':
                    addMarkers(data.markers);
                    break;
                    
                case 'clearMarkers':
                    clearMarkers();
                    break;
                    
                case 'drawPolyline':
                    drawPolyline(data.polyline);
                    break;
                    
                case 'clearPolylines':
                    clearPolylines();
                    break;
                    
                case 'fitView':
                    fitView(data.points);
                    break;
                    
                case 'planRoute':
                    planRoute(data.start, data.end, data.waypoints, data.mode);
                    break;
            }
        }

        // 添加标记
        function addMarkers(markerData) {
            clearMarkers();
            
            markerData.forEach(markerInfo => {
                const marker = new AMap.Marker({
                    position: [markerInfo.longitude, markerInfo.latitude],
                    title: markerInfo.title,
                    content: markerInfo.content || createMarkerContent(markerInfo)
                });

                marker.setMap(map);
                markers.push(marker);

                // 标记点击事件
                marker.on('click', function() {
                    sendMessageToMiniProgram({
                        type: 'markerClick',
                        data: {
                            id: markerInfo.id,
                            longitude: markerInfo.longitude,
                            latitude: markerInfo.latitude
                        }
                    });
                });
            });
        }

        // 创建标记内容
        function createMarkerContent(markerInfo) {
            const div = document.createElement('div');
            div.style.cssText = `
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: ${markerInfo.bgColor || '#409EFF'};
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                border: 2px solid white;
                box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            `;
            div.textContent = markerInfo.label || '●';
            return div;
        }

        // 清除标记
        function clearMarkers() {
            markers.forEach(marker => {
                marker.setMap(null);
            });
            markers = [];
        }

        // 绘制路线
        function drawPolyline(polylineData) {
            clearPolylines();
            
            if (Array.isArray(polylineData)) {
                // 多条路线
                polylineData.forEach(lineData => {
                    const polyline = new AMap.Polyline({
                        path: lineData.points.map(p => [p.longitude, p.latitude]),
                        strokeColor: lineData.color || '#3498db',
                        strokeWeight: lineData.width || 6,
                        strokeOpacity: 0.8,
                        strokeStyle: lineData.dottedLine ? 'dashed' : 'solid',
                        showDir: lineData.arrowLine || false
                    });
                    
                    polyline.setMap(map);
                    polylines.push(polyline);
                });
            } else {
                // 单条路线
                const polyline = new AMap.Polyline({
                    path: polylineData.points.map(p => [p.longitude, p.latitude]),
                    strokeColor: polylineData.color || '#3498db',
                    strokeWeight: polylineData.width || 6,
                    strokeOpacity: 0.8,
                    strokeStyle: polylineData.dottedLine ? 'dashed' : 'solid',
                    showDir: polylineData.arrowLine || false
                });
                
                polyline.setMap(map);
                polylines.push(polyline);
            }
        }

        // 清除路线
        function clearPolylines() {
            polylines.forEach(polyline => {
                polyline.setMap(null);
            });
            polylines = [];
        }

        // 调整视野
        function fitView(points) {
            if (points && points.length > 0) {
                const bounds = new AMap.Bounds();
                points.forEach(point => {
                    bounds.extend([point.longitude, point.latitude]);
                });
                map.setBounds(bounds);
            }
        }

        // 路线规划
        function planRoute(start, end, waypoints, mode) {
            let driving;
            
            switch (mode) {
                case 'walking':
                    driving = new AMap.Walking({
                        map: map,
                        panel: null
                    });
                    break;
                case 'riding':
                    driving = new AMap.Riding({
                        map: map,
                        panel: null
                    });
                    break;
                default:
                    driving = new AMap.Driving({
                        map: map,
                        panel: null
                    });
                    break;
            }

            const startPoint = new AMap.LngLat(start.longitude, start.latitude);
            const endPoint = new AMap.LngLat(end.longitude, end.latitude);
            const waypointArray = waypoints ? waypoints.map(wp => new AMap.LngLat(wp.longitude, wp.latitude)) : [];

            driving.search(startPoint, endPoint, {
                waypoints: waypointArray
            }, function(status, result) {
                if (status === 'complete') {
                    sendMessageToMiniProgram({
                        type: 'routeResult',
                        data: {
                            status: 'success',
                            result: result
                        }
                    });
                } else {
                    sendMessageToMiniProgram({
                        type: 'routeResult',
                        data: {
                            status: 'error',
                            error: result
                        }
                    });
                }
            });
        }

        // 监听来自小程序的消息
        window.addEventListener('message', function(event) {
            if (event.data) {
                receiveMessage(event.data);
            }
        });

        // 页面加载完成后初始化地图
        window.onload = function() {
            setTimeout(initMap, 100);
        };
    </script>
</body>
</html>
