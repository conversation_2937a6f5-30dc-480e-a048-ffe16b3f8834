from flask import Blueprint, request
from models.location_favorite import LocationFavorite
from models.location import Location
from models.user import User
from utils.database import db
from utils.response import success, error

location_favorites_bp = Blueprint('location_favorites', __name__)

@location_favorites_bp.route('/add', methods=['POST'])
def add_favorite():
    """添加景点到收藏"""
    try:
        data = request.get_json()
        
        if not data or not data.get('user_id') or not data.get('location_id'):
            return error('Missing required fields')
        
        user_id = data['user_id']
        location_id = data['location_id']
        
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return error('User not found')
        
        # 检查景点是否存在
        location = Location.query.get(location_id)
        if not location:
            return error('Location not found')
        
        # 检查是否已经收藏
        existing_favorite = LocationFavorite.query.filter_by(
            user_id=user_id, 
            location_id=location_id
        ).first()
        
        if existing_favorite:
            return error('Location already in favorites')
        
        # 添加收藏
        favorite = LocationFavorite(user_id=user_id, location_id=location_id)
        db.session.add(favorite)
        db.session.commit()
        
        return success({}, 'Location added to favorites')
    except Exception as e:
        db.session.rollback()
        return error(f'Error adding location to favorites: {str(e)}')

@location_favorites_bp.route('/remove', methods=['POST'])
def remove_favorite():
    """从收藏中移除景点"""
    try:
        data = request.get_json()
        
        if not data or not data.get('user_id') or not data.get('location_id'):
            return error('Missing required fields')
        
        user_id = data['user_id']
        location_id = data['location_id']
        
        # 检查收藏是否存在
        favorite = LocationFavorite.query.filter_by(
            user_id=user_id, 
            location_id=location_id
        ).first()
        
        if not favorite:
            return error('Location not in favorites')
        
        # 移除收藏
        db.session.delete(favorite)
        db.session.commit()
        
        return success({}, 'Location removed from favorites')
    except Exception as e:
        db.session.rollback()
        return error(f'Error removing location from favorites: {str(e)}')

@location_favorites_bp.route('/list/<int:user_id>', methods=['GET'])
def list_favorites(user_id):
    """获取用户的景点收藏列表"""
    try:
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return error('User not found')
        
        # 获取用户的收藏
        favorites = LocationFavorite.query.filter_by(user_id=user_id).all()
        
        # 获取收藏的景点详情
        locations = []
        for favorite in favorites:
            location = Location.query.get(favorite.location_id)
            if location:
                location_dict = location.to_dict()
                location_dict['favorited_at'] = favorite.created_at.isoformat() if favorite.created_at else None
                locations.append(location_dict)
        
        return success({'favorites': locations}, 'Favorites retrieved successfully')
    except Exception as e:
        return error(f'Error retrieving favorites: {str(e)}')

@location_favorites_bp.route('/check', methods=['POST'])
def check_favorite():
    """检查景点是否已收藏"""
    try:
        data = request.get_json()
        
        if not data or not data.get('user_id') or not data.get('location_id'):
            return error('Missing required fields')
        
        user_id = data['user_id']
        location_id = data['location_id']
        
        # 检查是否已经收藏
        favorite = LocationFavorite.query.filter_by(
            user_id=user_id, 
            location_id=location_id
        ).first()
        
        is_favorite = favorite is not None
        
        return success({'is_favorite': is_favorite}, 'Favorite status checked')
    except Exception as e:
        return error(f'Error checking favorite status: {str(e)}')
