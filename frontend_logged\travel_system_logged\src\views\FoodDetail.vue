<template>
  <div class="food-detail-container" v-loading="loading">
    <div class="back-button">
      <el-button type="primary" plain @click="goBack">
        <el-icon><ArrowLeft /></el-icon> 返回
      </el-button>
    </div>
    <template v-if="foodData">
      <div class="food-header">
        <div class="food-image">
          <img :src="foodData.image_url || defaultFoodImage" :alt="foodData.name" />
        </div>
        <div class="food-info">
          <h1>{{ foodData.name }}</h1>
          <div class="food-tags">
            <el-tag size="large" type="success">{{ foodData.cuisine_type }}</el-tag>
            <el-tag size="large" type="info">{{ foodData.region }}</el-tag>
          </div>
          <div class="food-rating">
            <el-rate
              v-model="foodData.rating"
              disabled
              text-color="#ff9900"
              :colors="['#FF9900', '#FF9900', '#FF9900']"
              :allow-half="false"
            />
            <span>{{ foodData.rating.toFixed(1) }}</span>
            <span class="rating-count">({{ foodData.rating_count || 0 }}人评分)</span>
          </div>
          <div class="food-stats">
            <el-tag type="info" effect="plain">
              <i class="el-icon-view"></i> 浏览量: {{ foodData.view_count || 0 }}
            </el-tag>
          </div>
          <div class="food-actions">
            <el-button type="danger" @click="toggleFavorite">
              <el-icon>
                <component :is="isFavorite ? StarFilled : Star" />
              </el-icon>
              {{ isFavorite ? '取消收藏' : '收藏' }}
            </el-button>
          </div>
        </div>
      </div>

      <div class="food-content">
        <el-tabs>
          <el-tab-pane label="美食介绍">
            <div class="food-description">
              <h2>简介</h2>
              <p>{{ foodData.description }}</p>

              <h2>特色</h2>
              <p>{{ foodData.features }}</p>

              <h2>制作方法</h2>
              <p>{{ foodData.cooking_method }}</p>

              <h2>招牌菜品</h2>


              <div class="dish-gallery" v-if="validDishes.length > 0">
                <div class="dish-gallery-item" v-for="dish in validDishes" :key="dish.name">
                  <el-image
                    :src="dish.image_url ? `http://localhost:5000${dish.image_url}` : defaultFoodImage"
                    :preview-src-list="dish.image_url ? [`http://localhost:5000${dish.image_url}`] : [defaultFoodImage]"
                    fit="cover"
                    class="dish-image"
                  />
                  <div class="dish-info">
                    <h3 class="dish-title">{{ dish.name }}</h3>
                    <div class="dish-price">¥{{ dish.price }}</div>
                    <div class="dish-description">{{ dish.description || `精选${dish.name}，口感鲜美` }}</div>
                  </div>
                  <div class="dish-overlay">
                    <el-button
                      type="danger"
                      round
                      size="small"
                      class="order-button"
                      @click="addToCart(dish)"
                    >
                      <i class="el-icon-shopping-cart-2"></i> 加入购物车
                    </el-button>
                  </div>
                </div>
              </div>
              <p v-else>暂无菜品信息</p>
            </div>
          </el-tab-pane>
          <el-tab-pane label="用户评价">
            <div class="review-list">
              <div v-if="foodData.reviews && foodData.reviews.length > 0">
                <div class="review-card" v-for="review in foodData.reviews" :key="review.id">
                  <div class="review-header">
                    <img :src="review.user_avatar ? `http://localhost:5000${review.user_avatar}` : defaultAvatar" class="user-avatar" />
                    <div class="review-user-info">
                      <h4>{{ review.username || `用户${review.user_id}` }}</h4>
                      <el-rate
                        v-model="review.rating"
                        disabled
                        :colors="['#FF9900', '#FF9900', '#FF9900']"
                        :allow-half="false"
                      ></el-rate>
                    </div>
                    <span class="review-date">{{ formatDate(review.created_at) }}</span>
                  </div>
                  <p class="review-content">{{ review.content }}</p>
                </div>
              </div>
              <el-empty v-else description="暂无用户评价" />

              <div class="add-review">
                <h3>添加评价</h3>
                <el-input
                  v-model="newReview.content"
                  type="textarea"
                  :rows="4"
                  placeholder="分享您对这道美食的看法..."
                ></el-input>
                <div class="review-rating">
                  <span>您的评分:</span>
                  <el-rate
                    v-model="newReview.rating"
                    :allow-half="false"
                    :colors="['#FF9900', '#FF9900', '#FF9900']"
                  ></el-rate>
                </div>
                <el-button type="primary" @click="submitReview">提交评价</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="related-foods">
        <h2>相关美食推荐</h2>
        <div class="related-food-list">
          <div class="related-food-card" v-for="food in relatedFoods" :key="food.food_id" @click="goToFoodDetail(food.food_id)">
            <img :src="food.image_url || defaultFoodImage" :alt="food.name" />
            <div class="related-food-info">
              <h3>{{ food.name }}</h3>
              <el-rate
                v-model="food.rating"
                disabled
                :colors="['#FF9900', '#FF9900', '#FF9900']"
                :allow-half="false"
              ></el-rate>
            </div>
          </div>
        </div>
      </div>
    </template>
    <el-empty v-else description="未找到美食信息" />


  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Star, StarFilled, ArrowLeft } from '@element-plus/icons-vue';
import { getFoodDetail, getFoodRecommendations } from '@/api/food';
import {
  favoriteRestaurant,
  unfavoriteRestaurant,
  checkRestaurantFavorite,
  commentRestaurant,
  getRestaurantComments,
  getRestaurantRating
} from '@/api/restaurant';
import { addToCart as addToCartApi } from '@/api/cart';
import { getCurrentUserId } from '@/utils/userUtils';
import defaultFoodImage from '@/assets/forbidden_city.jpg';
import defaultAvatar from '@/assets/belog.jpg';

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const foodData = ref(null);
const relatedFoods = ref([]);
const isFavorite = ref(false);

const restaurantRating = ref({
  average_rating: 0,
  rating_count: 0,
  user_rating: 0
});

const newReview = ref({
  content: '',
  rating: 0
});

// 计算属性：有效的菜品列表（过滤掉null和undefined）
const validDishes = computed(() => {
  if (!foodData.value || !foodData.value.dishes) {
    return [];
  }

  // 打印菜品数据，用于调试
  console.log('菜品数据:', foodData.value.dishes);

  // 过滤有效的菜品（必须有名称）
  const dishes = foodData.value.dishes.filter(dish => dish && dish.name);

  // 打印过滤后的菜品数据
  console.log('有效菜品数据:', dishes);

  return dishes;
});

// 菜品图片处理已经在后端完成，不再需要前端处理

// 菜品图片处理已经在模板中通过v-if条件渲染处理

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 获取美食详情
const fetchFoodDetail = async () => {
  loading.value = true;
  try {
    const restaurantId = route.params.id;
    const response = await getFoodDetail(restaurantId);

    if (response && response.code === 0 && response.data) {
      // 处理后端返回的数据
      const data = response.data;

      // 构建foodData对象
      foodData.value = {
        food_id: data.id,
        name: data.name,
        cuisine_type: data.cuisine_type,
        rating: data.rating || data.evaluation || 0,
        rating_count: data.rating_count || 0,
        view_count: data.number_of_view || 0,
        image_url: data.image_url ? `http://localhost:5000${data.image_url}` : defaultFoodImage,
        description: data.description || `${data.name}是一家提供${data.cuisine_type}的餐厅。`,
        features: data.features || '特色菜品丰富多样。',
        cooking_method: data.cooking_method || '采用传统烹饪方法。',
        region: '北京',
        average_price: data.average_price || 0,

        // 餐厅信息
        restaurants: [
          {
            id: data.id,
            name: data.name,
            address: data.address || '北京市',
            phone: data.phone || '010-12345678',
            average_price: data.average_price || 0,
            rating: data.rating || data.evaluation || 0
          }
        ],

        // 环境图片
        environment_images: data.environment_images || [],

        // 菜品图片
        dish_images: data.dish_images || [],

        // 菜品信息
        dishes: data.dishes || [],

        // 打印菜品信息，用于调试
        dishesDebug: JSON.stringify(data.dishes)
      };

      // 检查是否已收藏
      checkIfFavorite();
      // 获取相关美食推荐
      fetchRelatedFoods();
    } else {
      ElMessage.error('获取美食详情失败');
    }
  } catch (error) {
    console.error('获取美食详情出错:', error);
    ElMessage.error('获取美食详情出错');
  } finally {
    loading.value = false;
  }
};

// 检查是否已收藏
const checkIfFavorite = async () => {
  try {
    const userId = localStorage.getItem('userId');
    if (!userId) return;

    // 使用正确的参数调用API
    const response = await checkRestaurantFavorite(userId, route.params.id);

    isFavorite.value = response && response.code === 0 && response.data && response.data.is_favorite;
  } catch (error) {
    console.error('检查收藏状态出错:', error);
  }
};

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      ElMessage.warning('请先登录后再收藏');
      return;
    }

    const restaurantId = route.params.id;
    let response;

    if (isFavorite.value) {
      response = await unfavoriteRestaurant({
        user_id: userId,
        restaurant_id: restaurantId
      });

      if (response && response.code === 0) {
        isFavorite.value = false;
        ElMessage.success('已取消收藏');
      }
    } else {
      response = await favoriteRestaurant({
        user_id: userId,
        restaurant_id: restaurantId
      });

      if (response && response.code === 0) {
        isFavorite.value = true;
        ElMessage.success('收藏成功');
      }
    }
  } catch (error) {
    console.error('操作收藏出错:', error);
    ElMessage.error('操作失败，请稍后再试');
  }
};



// 提交评价
const submitReview = async () => {
  try {
    const userId = getCurrentUserId();
    if (!userId) {
      ElMessage.warning('请先登录后再评价');
      return;
    }

    if (!newReview.value.content) {
      ElMessage.warning('请输入评价内容');
      return;
    }

    if (newReview.value.rating <= 0) {
      ElMessage.warning('请选择评分');
      return;
    }

    const response = await commentRestaurant({
      restaurant_id: route.params.id,
      user_id: userId,
      rating: newReview.value.rating,
      content: newReview.value.content
    });

    // 由于我们修改了axios拦截器，response现在直接是后端返回的数据
    if (response && response.code === 0) {
      ElMessage.success('评价成功');

      // 立即将新评价添加到评论列表中，避免需要刷新页面
      const newComment = {
        id: response.data.id,
        user_id: userId,
        restaurant_id: route.params.id,
        content: newReview.value.content,
        rating: newReview.value.rating,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        username: '我', // 当前用户
        user_avatar: '/uploads/avatars/default.jpg'
      };

      // 将新评价添加到评论列表的开头
      if (foodData.value && foodData.value.reviews) {
        foodData.value.reviews.unshift(newComment);
      } else if (foodData.value) {
        foodData.value.reviews = [newComment];
      }

      // 重置评价表单
      newReview.value = {
        content: '',
        rating: 0
      };

      // 异步刷新数据，确保数据一致性
      setTimeout(() => {
        fetchRestaurantComments();
        fetchRestaurantRating();
      }, 500);

    } else {
      ElMessage.error(response?.message || '评价失败');
      console.error('评价失败，响应数据:', response);
    }
  } catch (error) {
    console.error('提交评价出错:', error);
    ElMessage.error('评价失败，请稍后再试');
  }
};

// 获取相关美食推荐
const fetchRelatedFoods = async () => {
  try {
    if (!foodData.value || !foodData.value.cuisine_type) {
      return;
    }

    // 获取相同菜系的其他餐馆
    const response = await getFoodRecommendations({
      cuisine_type: foodData.value.cuisine_type,
      limit: 5,
      sort_by: 'rating',
      order: 'desc'
    });

    if (response && response.code === 0 && response.data) {
      // 过滤掉当前餐馆
      const filteredData = response.data.filter(item => item.id !== parseInt(route.params.id));

      // 转换数据格式
      relatedFoods.value = filteredData.map(item => ({
        food_id: item.id,
        name: item.name,
        rating: item.evaluation || 0,
        image_url: item.image_url ? `http://localhost:5000${item.image_url}` : defaultFoodImage
      }));
    }
  } catch (error) {
    console.error('获取相关美食出错:', error);
  }
};

// 跳转到其他美食详情页
const goToFoodDetail = (foodId) => {
  // 如果是同一个页面，使用replace而不是push，避免返回时重复增加浏览量
  if (route.params.id == foodId) {
    router.replace(`/food/detail/${foodId}`);
  } else {
    router.push(`/food/detail/${foodId}`);
  }
};

// 返回上一页
const goBack = () => {
  router.push('/food');
};

// 添加菜品到购物车
const addToCart = async (dish) => {
  try {
    const userId = localStorage.getItem('userId');
    if (!userId) {
      ElMessage.warning('请先登录后再添加到购物车');
      return;
    }

    const cartData = {
      user_id: parseInt(userId),
      dish_name: dish.name,
      dish_price: dish.price,
      dish_image: dish.image_url,
      restaurant_id: parseInt(route.params.id)
    };

    const response = await addToCartApi(cartData);

    if (response && response.code === 0) {
      ElMessage.success({
        message: '已添加到购物车',
        type: 'success',
        duration: 2000
      });
    } else {
      ElMessage.error(response?.message || '添加到购物车失败');
    }
  } catch (error) {
    console.error('添加到购物车出错:', error);
    ElMessage.error('添加到购物车失败，请稍后再试');
  }
};

// 获取餐馆评论
const fetchRestaurantComments = async () => {
  try {
    const restaurantId = route.params.id;
    const response = await getRestaurantComments(restaurantId);

    // 由于我们修改了axios拦截器，response现在直接是后端返回的数据
    if (response && response.code === 0 && response.data) {
      if (foodData.value) {
        foodData.value.reviews = response.data;
      }
    }
  } catch (error) {
    console.error('获取餐馆评论出错:', error);
  }
};

// 获取餐馆评分
const fetchRestaurantRating = async () => {
  try {
    const restaurantId = route.params.id;
    const userId = localStorage.getItem('userId');

    const response = await getRestaurantRating(restaurantId, userId);

    // 由于我们修改了axios拦截器，response现在直接是后端返回的数据
    if (response && response.code === 0 && response.data) {
      restaurantRating.value = response.data;



      // 更新foodData中的评分
      if (foodData.value) {
        foodData.value.rating = response.data.average_rating;
        foodData.value.rating_count = response.data.rating_count;
      }
    }
  } catch (error) {
    console.error('获取餐馆评分出错:', error);
  }
};

// 注意：不再需要单独的增加浏览量函数
// 因为fetchFoodDetail已经会增加浏览量
// 这样可以避免重复增加浏览量

// 页面加载时获取数据
onMounted(() => {
  // 滚动到页面顶部
  window.scrollTo(0, 0);

  fetchFoodDetail(); // 这个API调用已经会增加浏览量
  fetchRestaurantComments();
  fetchRestaurantRating(); // 获取餐馆评分
  // 移除了increaseViewCount()调用，避免重复增加浏览量
});
</script>

<style scoped>
.food-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.back-button {
  margin-bottom: 20px;
}

.food-header {
  display: flex;
  margin-bottom: 30px;
  gap: 30px;
}

.food-image {
  width: 40%;
  height: 300px;
  overflow: hidden;
  border-radius: 8px;
}

.food-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.food-info {
  flex: 1;
}

.food-info h1 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 2rem;
}

.food-tags {
  margin-bottom: 15px;
}

.food-tags .el-tag {
  margin-right: 10px;
}

.food-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.food-rating span {
  margin-left: 10px;
  color: #ff9900;
  font-weight: bold;
}

.rating-count {
  color: #999 !important;
  font-weight: normal !important;
  font-size: 0.9rem;
}

.food-stats {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.food-actions {
  margin-top: 30px;
}

.food-actions .el-button {
  margin-right: 15px;
}

.food-content {
  margin-bottom: 30px;
}

.food-description h2 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 1.5rem;
  color: #333;
}

.food-description p {
  line-height: 1.6;
  color: #666;
}

.restaurant-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.restaurant-card {
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.restaurant-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.restaurant-card p {
  margin: 5px 0;
  color: #666;
}

.review-list {
  margin-top: 20px;
}

.review-card {
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  background-color: white;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.review-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ff7e5f;
  transform: translateY(-3px);
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.review-card:hover .user-avatar {
  border-color: #ff7e5f;
}

.review-user-info {
  flex: 1;
}

.review-user-info h4 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}

.review-date {
  color: #999;
  font-size: 0.9rem;
  margin-left: auto;
  padding-left: 15px;
}

.review-content {
  color: #444;
  line-height: 1.6;
  font-size: 1rem;
  padding-left: 65px;
}

.dishes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.dish-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.dish-item h3 {
  margin: 0 0 10px;
  font-size: 1.1rem;
  color: #333;
}

.dish-price {
  color: #e74c3c;
  font-weight: bold;
  margin: 0;
}

.dish-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin: 25px 0;
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.dish-gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  background-color: #fff;
  height: 400px;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.dish-gallery-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.dish-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
  transition: transform 0.6s;
}

.dish-gallery-item:hover .dish-image {
  transform: scale(1.08);
}

.dish-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  background: linear-gradient(to bottom, rgba(255,255,255,0.95), #fff);
}

.dish-title {
  margin: 0 0 10px;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
  position: relative;
  padding-bottom: 10px;
}

.dish-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, #ff7e5f, #feb47b);
  border-radius: 3px;
}

.dish-price {
  font-size: 1.2rem;
  color: #e74c3c;
  font-weight: 700;
  margin-bottom: 10px;
  display: inline-block;
  background: rgba(231, 76, 60, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
}

.dish-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-top: 10px;
  font-style: italic;
}

.dish-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: all 0.4s ease;
  backdrop-filter: blur(2px);
}

.dish-gallery-item:hover .dish-overlay {
  opacity: 1;
}

.order-button {
  transform: translateY(20px);
  transition: all 0.4s ease;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 16px;
  border: none;
  background: linear-gradient(to right, #ff7e5f, #feb47b);
  box-shadow: 0 4px 15px rgba(255, 126, 95, 0.4);
  position: relative;
  z-index: 10;
}

.dish-gallery-item:hover .order-button {
  transform: translateY(0);
}

.order-button:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(255, 126, 95, 0.5);
}

.order-button:active {
  transform: scale(0.95) !important;
  box-shadow: 0 2px 10px rgba(255, 126, 95, 0.3);
}

.add-review {
  margin-top: 40px;
  padding: 25px;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border-top: 4px solid #ff7e5f;
  transition: all 0.3s ease;
}

.add-review:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.add-review h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.review-rating {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.review-rating span {
  margin-right: 15px;
  font-weight: 500;
  color: #555;
}

.related-foods {
  margin-top: 30px;
}

.related-foods h2 {
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.related-food-list {
  display: flex;
  overflow-x: auto;
  gap: 20px;
  padding-bottom: 10px;
}

.related-food-card {
  min-width: 200px;
  max-width: 250px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s;
}

.related-food-card:hover {
  transform: translateY(-5px);
}

.related-food-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.related-food-info {
  padding: 10px;
}

.related-food-info h3 {
  margin: 0 0 10px 0;
  font-size: 1rem;
}

.rating-dialog-content {
  text-align: center;
}

.dialog-rating {
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-rating span {
  margin-right: 10px;
}
</style>