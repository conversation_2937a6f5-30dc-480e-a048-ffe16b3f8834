from flask import Blueprint, request, jsonify
from models.location import Location
from models.location_browse_count import LocationBrowseCount
from models.user import User
from models.location_browse import LocationBrowseHistory
from utils.database import db
from utils.response import success, error
from sqlalchemy import text

location_bp = Blueprint('location', __name__)

@location_bp.route('', methods=['GET'])
def get_locations():
    """
    Get all locations
    """
    try:
        locations = Location.query.all()
        location_list = [location.to_dict() for location in locations]
        return success(location_list, 'Locations retrieved successfully')
    except Exception as e:
        return error(str(e))

@location_bp.route('/<int:location_id>', methods=['GET'])
def get_location(location_id):
    """
    Get location by ID
    """
    try:
        location = Location.query.get(location_id)
        if not location:
            return jsonify({'error': 'Location not found'}), 404
        return success(location.to_dict(), 'Location retrieved successfully')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@location_bp.route('/recommend/<int:user_id>', methods=['GET'])
def get_recommend_locations(user_id):
    """
    Get recommended locations for a user
    """
    try:
        # Get browse counts for this user
        browse_counts = LocationBrowseCount.query.filter_by(user_id=user_id).all()

        # Create user view counts dictionary
        user_view_counts = {bc.location_id: bc.count for bc in browse_counts}

        # Get all locations
        all_locations = Location.query.all()

        # If user has browse history, use location-based recommendation
        if user_view_counts:
            from utils.location_based_recommend import location_based_recommend
            recommended_locations = location_based_recommend(user_view_counts, all_locations)

            # Limit to 10 locations
            locations = recommended_locations[:10]
        else:
            # Get locations by popularity if no browse history
            locations = Location.query.order_by(Location.popularity.desc()).limit(10).all()

        return jsonify([location.to_dict() for location in locations]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@location_bp.route('/query', methods=['GET'])
def query_locations():
    """
    Query locations by name, type, and keyword
    Equivalent to Java's LocationController.getQueryLocation()
    """
    try:
        # 获取查询参数
        name = request.args.get('name', '')
        type_val = request.args.get('type', '')
        keyword = request.args.get('keyword', '')
        sort_order = request.args.get('sortOrder', '0')

        print(f"Query parameters: name={name}, type={type_val}, keyword={keyword}, sortOrder={sort_order}")

        # 构建基本查询
        locations = []

        # 构建SQL查询字符串
        params = {}
        sql_parts = ["SELECT * FROM locations WHERE 1=1"]

        # 添加名称过滤
        if name:
            sql_parts.append("AND name LIKE :name")
            params['name'] = f"%{name}%"
            print(f"Added name filter: {name}")

        # 添加类型过滤
        if type_val and type_val.isdigit():
            sql_parts.append("AND type = :type")
            params['type'] = int(type_val)
            print(f"Added type filter: {type_val}")

        # 添加关键词过滤
        if keyword:
            # 同时搜索名称和关键词字段
            sql_parts.append("AND (name LIKE :keyword OR keyword LIKE :keyword)")
            params['keyword'] = f"%{keyword}%"
            print(f"Added keyword filter: {keyword}")

        # 添加排序
        if sort_order and sort_order.isdigit():
            sort_int = int(sort_order)
            if sort_int == 0:  # 按人气排序
                sql_parts.append("ORDER BY popularity DESC")
                print("Added sorting: popularity (desc)")
            elif sort_int == 1:  # 按评价排序
                sql_parts.append("ORDER BY evaluation DESC")
                print("Added sorting: evaluation (desc)")
        else:
            # 默认按人气排序
            sql_parts.append("ORDER BY popularity DESC")
            print("Added default sorting: popularity (desc)")

        # 组合SQL查询
        sql_query = text(" ".join(sql_parts))

        # 执行查询
        print(f"Executing SQL: {sql_query} with params: {params}")
        result = db.session.execute(sql_query, params)

        # 处理结果
        for row in result:
            location = {
                'location_id': row[0],
                'name': row[1],
                'type': row[2],
                'keyword': row[3],
                'popularity': row[4],
                'evaluation': row[5]
            }
            locations.append(location)

        print(f"Query returned {len(locations)} locations")

        # 如果没有结果且有关键词，尝试更宽松的搜索
        if not locations and keyword:
            print("No results found, trying more relaxed search...")

            # 拆分关键词
            keywords = keyword.split()
            if keywords:
                # 构建OR条件
                keyword_conditions = []
                for i, kw in enumerate(keywords):
                    if len(kw) >= 2:  # 只使用长度至少为2的关键词
                        param_name = f"kw{i}"
                        keyword_conditions.append(f"name LIKE :{param_name} OR keyword LIKE :{param_name}")
                        params[param_name] = f"%{kw}%"

                if keyword_conditions:
                    # 重新构建查询
                    sql_query = "SELECT * FROM locations WHERE " + " OR ".join(keyword_conditions)

                    # 添加排序
                    if sort_order and sort_order.isdigit():
                        sort_int = int(sort_order)
                        if sort_int == 0:
                            sql_query += " ORDER BY popularity DESC"
                        elif sort_int == 1:
                            sql_query += " ORDER BY evaluation DESC"
                    else:
                        sql_query += " ORDER BY popularity DESC"

                    print(f"Executing relaxed SQL: {sql_query} with params: {params}")
                    result = db.session.execute(sql_query, params)

                    # 清空之前的结果并添加新结果
                    locations = []
                    for row in result:
                        location = {
                            'location_id': row[0],
                            'name': row[1],
                            'type': row[2],
                            'keyword': row[3],
                            'popularity': row[4],
                            'evaluation': row[5]
                        }
                        locations.append(location)

                    print(f"Relaxed query returned {len(locations)} locations")

        return jsonify(locations), 200
    except Exception as e:
        import traceback
        traceback.print_exc()
        error_message = str(e)
        print(f"Error in query_locations: {error_message}")
        return jsonify({'error': error_message}), 500

@location_bp.route('/browse/<int:location_id>', methods=['POST'])
@location_bp.route('/browse', methods=['POST'])
def update_browse_count(location_id=None):
    """
    Update browse count for a location

    Can be called in two ways:
    1. POST /browse/<int:location_id> with user_id in request body
    2. POST /browse with user_id and location_id in request body
    """
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        # If location_id is not in URL, get it from request body
        if location_id is None:
            location_id = data.get('location_id')
            if not location_id:
                return jsonify({'error': 'Missing location_id parameter'}), 400

        if not user_id:
            return jsonify({'error': 'Missing user_id parameter'}), 400

        # Check if location and user exist
        location = Location.query.get(location_id)
        user = User.query.get(user_id)

        if not location:
            return jsonify({'error': 'Location not found'}), 404

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Update browse count
        browse_count = LocationBrowseCount.query.filter_by(
            location_id=location_id, user_id=user_id).first()

        if browse_count:
            browse_count.count += 1
        else:
            browse_count = LocationBrowseCount(
                location_id=location_id,
                user_id=user_id,
                count=1
            )
            db.session.add(browse_count)

        # Add browse history record
        browse_history = LocationBrowseHistory(
            user_id=user_id,
            location_id=location_id
        )
        db.session.add(browse_history)

        # Update location popularity
        location.popularity += 1

        db.session.commit()

        return success({}, 'Browse count updated successfully')
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@location_bp.route('/browse/<int:location_id>', methods=['GET'])
def get_browse_count(location_id):
    """
    Get browse count for a location
    """
    try:
        # Sum all browse counts for this location
        total_count = db.session.query(db.func.sum(LocationBrowseCount.count)).filter(
            LocationBrowseCount.location_id == location_id).scalar() or 0

        return jsonify({'count': total_count}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 路由已合并到 /query 中，通过 keyword 参数搜索地点

# 路由已合并到 /browse/<int:location_id> 中，通过请求体参数获取 user_id

@location_bp.route('/fuzzy_search', methods=['GET'])
def fuzzy_search_locations():
    """
    地点模糊查询 - 用于AI生成模块的自动完成
    """
    try:
        query = request.args.get('query', '').strip()
        limit = int(request.args.get('limit', 10))

        print(f"模糊搜索API被调用: query='{query}', limit={limit}")

        if not query:
            # 如果没有查询词，返回热门地点
            locations = Location.query.order_by(Location.popularity.desc()).limit(limit).all()
            print(f"返回热门地点: {len(locations)} 个")
        else:
            # 模糊搜索地点名称和关键词
            # 使用ilike进行不区分大小写的搜索
            locations = Location.query.filter(
                db.or_(
                    Location.name.ilike(f'%{query}%'),
                    Location.keyword.ilike(f'%{query}%') if Location.keyword else False
                )
            ).limit(limit).all()
            print(f"模糊搜索 '{query}' 返回: {len(locations)} 个地点")

            # 如果没有找到结果，尝试更宽泛的搜索
            if len(locations) == 0:
                # 尝试分词搜索
                keywords = query.split()
                for keyword in keywords:
                    if len(keyword) >= 2:  # 只搜索长度大于等于2的关键词
                        locations = Location.query.filter(
                            db.or_(
                                Location.name.ilike(f'%{keyword}%'),
                                Location.keyword.ilike(f'%{keyword}%') if Location.keyword else False
                            )
                        ).limit(limit).all()
                        if len(locations) > 0:
                            print(f"分词搜索 '{keyword}' 找到: {len(locations)} 个地点")
                            break

            # 如果还是没有结果，返回一些示例地点
            if len(locations) == 0:
                print("未找到匹配地点，返回示例地点")
                locations = Location.query.limit(5).all()
                print(f"返回示例地点: {len(locations)} 个")

        result = []
        for location in locations:
            # 使用Location模型的to_dict方法，确保图片URL正确处理
            location_dict = location.to_dict()
            result.append(location_dict)

        print(f"最终返回结果: {len(result)} 个地点")
        if result:
            print(f"第一个结果示例: {result[0].get('name', 'N/A')}")

        return success(result, 'Fuzzy search completed successfully')

    except Exception as e:
        print(f"模糊搜索出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error in fuzzy search: {str(e)}')
