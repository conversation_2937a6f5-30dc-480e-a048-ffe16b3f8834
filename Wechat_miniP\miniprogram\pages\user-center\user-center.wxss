/* user-center.wxss */

.container {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.avatar-container {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #e0e0e0;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.login-btn {
  font-size: 28rpx;
  color: #409EFF;
  padding: 10rpx 30rpx;
  border: 1px solid #409EFF;
  border-radius: 30rpx;
}

.menu-list {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  color: #409EFF;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 30rpx;
  color: #999;
}

.placeholder {
  margin-top: 50rpx;
  text-align: center;
}

.placeholder-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.placeholder-desc {
  font-size: 28rpx;
  color: #666;
}
