#!/usr/bin/env python3
"""
附近景点查询功能测试脚本
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5000/api/path"

def test_location_suggestions():
    """测试地点名称建议API"""
    print("=== 测试地点名称建议API ===")
    
    test_queries = ["清华", "北京", "博物馆", "公园"]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        try:
            response = requests.get(f"{BASE_URL}/location-suggestions", params={
                'q': query,
                'limit': 5
            })
            
            if response.status_code == 200:
                suggestions = response.json()
                print(f"找到 {len(suggestions)} 个建议:")
                for suggestion in suggestions:
                    print(f"  - {suggestion['label']} (ID: {suggestion['vertex_id']}, 类型: {suggestion['type']})")
            else:
                print(f"错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

def test_nearby_spots():
    """测试附近景点查询API"""
    print("\n=== 测试附近景点查询API ===")
    
    test_cases = [
        {
            "name": "清华大学附近的博物馆",
            "data": {
                "location_name": "清华大学",
                "distance": 1000,
                "type": 3,  # 博物馆
                "limit": 10,
                "strategy": 0
            }
        },
        {
            "name": "北京大学附近的公园",
            "data": {
                "location_name": "北京大学",
                "distance": 800,
                "type": 2,  # 公园
                "limit": 5,
                "strategy": 1
            }
        },
        {
            "name": "通过顶点ID查询",
            "data": {
                "start_vertex_id": 1,
                "distance": 500,
                "limit": 8,
                "strategy": 2
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        try:
            response = requests.post(f"{BASE_URL}/nearby-spots", 
                                   json=test_case['data'],
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                print(f"起始位置: {result['start_location']['name']}")
                print(f"找到 {result['total_found']} 个附近景点:")
                
                for spot in result['nearby_spots']:
                    print(f"  - {spot['name']} (距离: {spot['path_distance']}, 类型: {spot['type']})")
                    
            else:
                print(f"错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

def test_spots_by_criteria():
    """测试多条件景点查询API"""
    print("\n=== 测试多条件景点查询API ===")
    
    test_data = {
        "location_name": "清华大学",
        "distance": 1200,
        "types": [3, 4, 5],  # 博物馆、古建筑、寺庙
        "keywords": ["博物馆", "艺术"],
        "limit": 15,
        "strategy": 0
    }
    
    try:
        response = requests.post(f"{BASE_URL}/spots-by-criteria", 
                               json=test_data,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            print(f"起始位置: {result['start_location']['name']}")
            print(f"查询条件: {result['criteria']}")
            print(f"找到 {result['total_found']} 个符合条件的景点:")
            
            for spot in result['nearby_spots']:
                print(f"  - {spot['name']} (距离: {spot['path_distance']}, 类型: {spot['type']})")
                
        else:
            print(f"错误: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

def test_improved_spots_api():
    """测试改进的spots API"""
    print("\n=== 测试改进的spots API ===")
    
    test_cases = [
        {
            "name": "通过地点名称查询",
            "data": {
                "location_name": "清华大学",
                "distance": 600,
                "type": 3,
                "limit": 8,
                "strategy": 0
            }
        },
        {
            "name": "通过坐标查询（兼容模式）",
            "data": {
                "x": 100,
                "y": 200,
                "distance": 500,
                "name": "博物馆",
                "limit": 5
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试用例: {test_case['name']}")
        try:
            response = requests.post(f"{BASE_URL}/spots", 
                                   json=test_case['data'],
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                print(f"找到 {len(result)} 个景点:")
                
                for spot in result[:3]:  # 只显示前3个
                    print(f"  - {spot['name']} (距离: {spot['distance']})")
                    
            else:
                print(f"错误: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"请求失败: {str(e)}")

def performance_test():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    test_data = {
        "location_name": "清华大学",
        "distance": 1000,
        "limit": 20,
        "strategy": 0
    }
    
    print("测试查询性能...")
    start_time = time.time()
    
    try:
        response = requests.post(f"{BASE_URL}/nearby-spots", 
                               json=test_data,
                               headers={'Content-Type': 'application/json'})
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"查询完成，耗时: {elapsed_time:.2f}秒")
            print(f"返回 {result['total_found']} 个结果")
        else:
            print(f"查询失败: {response.status_code}")
            
    except Exception as e:
        print(f"性能测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试附近景点查询功能...")
    print("请确保后端服务正在运行在 http://localhost:5000")
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 运行所有测试
    test_location_suggestions()
    test_nearby_spots()
    test_spots_by_criteria()
    test_improved_spots_api()
    performance_test()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
