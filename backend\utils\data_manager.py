"""
数据管理模块

该模块负责加载和管理景点数据，提供内存中的数据结构供算法使用。
"""

from typing import List, Dict, Optional
import time
from models.location import Location
from models.location_browse_count import LocationBrowseCount
from models.user import User
from flask import current_app


class DataManager:
    """数据管理类"""

    _instance = None
    _last_refresh_time = 0
    _refresh_interval = 300  # 5分钟刷新一次

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(DataManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化"""
        if self._initialized:
            return

        self._locations = []
        self._locations_map = {}
        self._user_browse_history = {}
        self._initialized = True
        self.refresh_data()

    def refresh_data(self, force: bool = False) -> None:
        """
        刷新数据

        Args:
            force: 是否强制刷新
        """
        current_time = time.time()

        # 如果距离上次刷新时间不足刷新间隔，且不是强制刷新，则跳过
        if not force and (current_time - self._last_refresh_time < self._refresh_interval):
            return

        # 加载所有景点
        self._load_locations()

        # 加载用户浏览历史
        self._load_user_browse_history()

        # 更新刷新时间
        self._last_refresh_time = current_time

        print(f"数据已刷新，共加载 {len(self._locations)} 个景点，{len(self._user_browse_history)} 个用户的浏览历史")

    def _load_locations(self) -> None:
        """加载所有景点"""
        try:
            # 从数据库加载所有景点
            print("开始从数据库加载景点...")
            locations = Location.query.all()
            print(f"从数据库加载了 {len(locations)} 个景点")

            # 转换为字典列表
            self._locations = [location.to_dict() for location in locations]
            print(f"转换为字典列表后有 {len(self._locations)} 个景点")

            # 确保评分是整数
            for loc in self._locations:
                if 'evaluation' in loc and loc['evaluation'] is not None:
                    loc['evaluation'] = int(round(loc['evaluation']))

            # 检查是否包含北京邮电大学
            bupt_locations = [loc for loc in self._locations if loc.get('name') == '北京邮电大学']
            print(f"其中包含 {len(bupt_locations)} 个北京邮电大学")
            if bupt_locations:
                print(f"北京邮电大学的详细信息: {bupt_locations[0]}")

            # 创建ID到景点的映射
            self._locations_map = {loc['location_id']: loc for loc in self._locations}
            print(f"创建了 {len(self._locations_map)} 个景点的映射")

            print(f"成功加载 {len(self._locations)} 个景点")
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"加载景点失败: {str(e)}")

    def _load_user_browse_history(self) -> None:
        """加载用户浏览历史"""
        try:
            # 从数据库加载所有浏览计数
            print("开始从数据库加载用户浏览历史...")
            browse_counts = LocationBrowseCount.query.all()
            print(f"从数据库加载了 {len(browse_counts)} 条浏览记录")

            # 构建用户浏览历史字典
            self._user_browse_history = {}

            for count in browse_counts:
                if count.user_id not in self._user_browse_history:
                    self._user_browse_history[count.user_id] = {}

                self._user_browse_history[count.user_id][count.location_id] = count.count

            print(f"成功加载 {len(self._user_browse_history)} 个用户的浏览历史")

            # 打印每个用户的浏览历史数量
            for user_id, history in self._user_browse_history.items():
                print(f"用户 {user_id} 浏览了 {len(history)} 个景点")
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"加载用户浏览历史失败: {str(e)}")

    def get_all_locations(self) -> List[Dict]:
        """
        获取所有景点

        Returns:
            所有景点的列表
        """
        # 确保数据是最新的
        self.refresh_data()
        return self._locations.copy()

    def get_location_by_id(self, location_id: int) -> Optional[Dict]:
        """
        根据ID获取景点

        Args:
            location_id: 景点ID

        Returns:
            景点信息，如果不存在则返回None
        """
        # 确保数据是最新的
        self.refresh_data()
        return self._locations_map.get(location_id)

    def get_user_browse_history(self, user_id: int) -> Dict[int, int]:
        """
        获取用户浏览历史

        Args:
            user_id: 用户ID

        Returns:
            用户浏览历史 {location_id: count}
        """
        # 确保数据是最新的
        self.refresh_data()
        return self._user_browse_history.get(user_id, {}).copy()

    def get_all_users_browse_history(self) -> Dict[int, Dict[int, int]]:
        """
        获取所有用户的浏览历史

        Returns:
            所有用户的浏览历史 {user_id: {location_id: count}}
        """
        # 确保数据是最新的
        self.refresh_data()
        return self._user_browse_history.copy()
