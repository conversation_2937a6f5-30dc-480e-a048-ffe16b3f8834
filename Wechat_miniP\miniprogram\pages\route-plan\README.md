# 微信小程序路径规划页面

## 功能概述

这个路径规划页面实现了与网页端相同的功能，包括：

### 主要功能
1. **地图显示**：使用微信小程序原生地图组件显示地图
2. **地点选择**：支持通过下拉选择器选择起点和终点
3. **途径点管理**：支持添加和删除多个途径点
4. **交通方式选择**：支持步行、骑行、不限（智能出行）三种模式
5. **策略选择**：支持最短距离、最短时间、智能出行策略
6. **路径规划**：调用后端API进行路径规划
7. **路线显示**：在地图上显示规划的路线，支持颜色编码和方向箭头
8. **路线详情**：显示距离、时间、路径步骤等详细信息

### 界面特点
- **响应式设计**：适配不同尺寸的手机屏幕
- **底部控制面板**：主要操作集中在底部，方便单手操作
- **直观的标记**：起点（绿色"起"）、终点（红色"终"）、途径点（蓝色"途"）
- **智能路线显示**：根据出行策略显示不同颜色的路线段

### 技术实现
- **前端**：微信小程序原生开发，使用TypeScript
- **地图**：微信小程序原生地图组件
- **路径规划**：完全使用后端算法，与网页端逻辑一致
- **API调用**：与后端Flask服务器通信，使用相同的API接口
- **数据格式**：与网页端保持完全一致的数据结构和算法

## 后端算法集成

### 完全一致的实现
- 使用与网页端相同的后端路径规划算法
- 支持所有策略：最短距离、最短时间、智能出行、骑行策略
- 支持途径点路径规划，使用简单顺序算法
- 支持拥挤度计算和颜色编码显示

### 坐标系统
- 使用后端数据库中的x, y坐标系统
- 通过坐标转换公式转换为经纬度：`lng = x / 1000000.0, lat = y / 1000000.0`
- 与网页端使用完全相同的坐标转换逻辑

## 使用说明

### 基本操作
1. **选择起点**：点击"起点"下拉框，选择出发地点
2. **选择终点**：点击"终点"下拉框，选择目的地点
3. **添加途径点**（可选）：点击"添加途径点"按钮，选择中间要经过的地点
4. **选择交通方式**：点击步行/骑行/不限按钮
5. **选择策略**：点击最短距离/最短时间/智能出行按钮
6. **规划路线**：点击"规划路线"按钮开始规划

### 智能出行策略
- 当选择"不限"交通方式时，可以选择"智能出行"策略
- 智能出行会自动结合骑行和步行，找到最优路线
- 路线会用不同颜色显示：
  - 绿色：畅通路段（拥挤度>0.9）
  - 黄色：一般路段（拥挤度0.5-0.8）
  - 红色：拥挤路段（拥挤度<0.5）
  - 蓝色虚线：步行路段

### 骑行模式
- 选择"骑行"交通方式时，支持最短距离和最短时间策略
- 系统会考虑道路的可骑行性和拥挤度
- 骑行速度设定为12km/h

## 文件结构

```
route-plan/
├── route-plan.wxml    # 页面布局
├── route-plan.ts      # 页面逻辑
├── route-plan.wxss    # 页面样式
├── route-plan.json    # 页面配置
└── README.md          # 说明文档
```

## API接口

页面使用以下API接口（与网页端完全一致）：

1. **获取地点列表**：`GET /api/path/vertices`
2. **单目的地路径规划**：`POST /api/path/plan`
3. **多目的地路径规划**：`POST /api/path/plan`

### 策略映射
- 策略0：最短距离
- 策略1：最短时间
- 策略2：可骑行路径
- 策略3：智能出行策略
- 策略4：骑行最短距离
- 策略5：骑行最短时间

## 注意事项

1. **网络权限**：需要在微信小程序后台配置服务器域名
   - 后端API域名：需要配置到request合法域名
2. **地图权限**：已在app.json中配置位置权限
3. **API地址**：当前配置为localhost:5000，实际部署时需要修改为正式服务器地址
4. **兼容性**：支持微信小程序基础库2.0.0以上版本
5. **数据一致性**：确保后端数据库与网页端使用相同的数据

## 配置说明

### 后端API配置
1. 确保后端服务器正常运行
2. 在代码中配置正确的API地址：`utils/util.ts` 第22行
3. 确保后端API接口与网页端一致

### 服务器域名配置
在微信小程序后台的"开发管理" -> "开发设置" -> "服务器域名"中配置：
- request合法域名：
  - `https://your-backend-domain.com` (后端API)

## 开发说明

### 主要组件
- `Component`：使用微信小程序Component构造器
- `map`：微信小程序原生地图组件
- `picker`：下拉选择器组件
- `scroll-view`：可滚动视图组件

### 数据流
1. 页面加载时获取所有地点数据
2. 用户选择起点、终点、途径点
3. 用户选择交通方式和策略
4. 调用后端API进行路径规划
5. 在地图上绘制路线并显示详情

### 扩展功能
- 可以添加实时位置获取功能
- 可以添加语音导航功能
- 可以添加路线收藏功能
- 可以添加路线分享功能
