"""
Article service
Aligned with Java version's ArticleServiceImpl
"""
import json
import heapq
from typing import Dict, List, Any, Tuple
from models.article import Article, ArticleScore, ArticleFavorite
from models.user import User
from models.location import Location
from models.article_like import ArticleLike
from models.article_comment import ArticleComment
from utils.database import db

class HuffmanNode:
    """Node for Huffman tree"""
    def __init__(self, char, freq):
        self.char = char
        self.freq = freq
        self.left = None
        self.right = None

    def __lt__(self, other):
        return self.freq < other.freq

class ArticleService:
    """Article service class"""

    def __init__(self):
        """Initialize the service"""
        pass

    def build_huffman_tree(self, text: str) -> HuffmanNode:
        """
        Build a Huffman tree from text

        Args:
            text: Input text

        Returns:
            Root node of Huffman tree
        """
        # Count frequency of each character
        frequency = {}
        for char in text:
            if char in frequency:
                frequency[char] += 1
            else:
                frequency[char] = 1

        # Create a priority queue
        priority_queue = []
        for char, freq in frequency.items():
            node = HuffmanNode(char, freq)
            heapq.heappush(priority_queue, node)

        # Build Huffman tree
        while len(priority_queue) > 1:
            left = heapq.heappop(priority_queue)
            right = heapq.heappop(priority_queue)

            # Create a new internal node with frequency equal to the sum
            internal = HuffmanNode(None, left.freq + right.freq)
            internal.left = left
            internal.right = right

            heapq.heappush(priority_queue, internal)

        # Return the root of the Huffman tree
        return priority_queue[0] if priority_queue else None

    def generate_huffman_codes(self, root: HuffmanNode) -> Dict[str, str]:
        """
        Generate Huffman codes from Huffman tree

        Args:
            root: Root node of Huffman tree

        Returns:
            Dictionary mapping characters to their Huffman codes
        """
        codes = {}

        def generate_codes_recursive(node, code):
            if node:
                if node.char:
                    codes[node.char] = code
                generate_codes_recursive(node.left, code + '0')
                generate_codes_recursive(node.right, code + '1')

        generate_codes_recursive(root, '')
        return codes

    def compress_text(self, text: str, codes: Dict[str, str]) -> bytes:
        """
        Compress text using Huffman codes

        Args:
            text: Input text
            codes: Huffman codes

        Returns:
            Compressed binary data
        """
        # Convert text to binary string using Huffman codes
        binary_string = ''
        for char in text:
            binary_string += codes[char]

        # Pad binary string to make its length a multiple of 8
        padding = 8 - (len(binary_string) % 8) if len(binary_string) % 8 != 0 else 0
        binary_string += '0' * padding

        # Convert binary string to bytes
        result = bytearray()
        for i in range(0, len(binary_string), 8):
            byte = binary_string[i:i+8]
            result.append(int(byte, 2))

        return bytes(result)

    def decompress_text(self, compressed_data: bytes, codes: Dict[str, str]) -> str:
        """
        Decompress binary data using Huffman codes

        Args:
            compressed_data: Compressed binary data
            codes: Huffman codes

        Returns:
            Decompressed text
        """
        # Reverse the codes dictionary
        reverse_codes = {code: char for char, code in codes.items()}

        # Convert bytes to binary string
        binary_string = ''
        for byte in compressed_data:
            binary_string += format(byte, '08b')

        # Decode binary string using reverse codes
        result = ''
        code = ''
        for bit in binary_string:
            code += bit
            if code in reverse_codes:
                result += reverse_codes[code]
                code = ''

        return result

    def add_article(self, user_id: int, title: str, content: str, location: str, tags: list = None) -> int:
        """
        Add a new article

        Args:
            user_id: User ID
            title: Article title
            content: Article content
            location: Location name

        Returns:
            Article ID if successful, None otherwise
        """
        try:
            # Check if content is empty
            if not content:
                return None

            # Compress content using Huffman coding
            huffman_tree = self.build_huffman_tree(content)
            huffman_codes = self.generate_huffman_codes(huffman_tree)
            compressed_content = self.compress_text(content, huffman_codes)

            # Convert Huffman codes to JSON string
            huffman_codes_json = json.dumps(huffman_codes)

            # Convert tags to JSON string
            tags_json = json.dumps(tags) if tags else json.dumps([])

            # Create new article
            article = Article(
                user_id=user_id,
                title=title,
                content=compressed_content,
                huffman_codes=huffman_codes_json,
                location=location,
                tags=tags_json,
                popularity=0,  # 初始浏览量为0
                evaluation=0
            )

            db.session.add(article)
            db.session.commit()

            return article.article_id
        except Exception as e:
            db.session.rollback()
            print(f"Error adding article: {e}")
            return None

    def get_article(self, article_id: int, increment_popularity: bool = False) -> Dict[str, Any]:
        """
        Get article by ID

        Args:
            article_id: Article ID
            increment_popularity: Whether to increment the popularity counter (default: True)

        Returns:
            Article data dictionary
        """
        try:
            article = Article.query.get(article_id)
            if not article:
                return None

            # Update popularity only if increment_popularity is True
            if increment_popularity:
                article.popularity += 1
                db.session.commit()

            # Get user information
            user = User.query.get(article.user_id)

            # Decompress content
            huffman_codes = json.loads(article.huffman_codes)
            content = self.decompress_text(article.content, huffman_codes)

            # 解析标签
            try:
                tags = json.loads(article.tags) if article.tags else []
            except:
                tags = []

            # 获取点赞、收藏和评论数量
            likes_count = ArticleLike.query.filter_by(article_id=article_id).count()
            favorites_count = ArticleFavorite.query.filter_by(article_id=article_id).count()
            comments_count = ArticleComment.query.filter_by(article_id=article_id).count()

            # 收集所有图片和视频URL
            image_urls = {
                'image_url': article.image_url,
                'image_url_2': article.image_url_2,
                'image_url_3': article.image_url_3,
                'image_url_4': article.image_url_4,
                'image_url_5': article.image_url_5,
                'image_url_6': article.image_url_6
            }

            video_urls = {
                'video_url': article.video_url,
                'video_url_2': article.video_url_2,
                'video_url_3': article.video_url_3
            }

            return {
                'article_id': article.article_id,
                'user_id': article.user_id,
                'username': user.username if user else None,
                'avatar': user.avatar if user else None,  # 添加用户头像
                'title': article.title,
                'content': content,
                'location': article.location,
                'tags': tags,  # 添加标签信息
                'popularity': article.popularity,
                'evaluation': article.evaluation,
                **image_urls,
                **video_urls,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                'likes_count': likes_count,
                'favorites_count': favorites_count,
                'comments_count': comments_count
            }
        except Exception as e:
            print(f"Error getting article: {e}")
            return None
