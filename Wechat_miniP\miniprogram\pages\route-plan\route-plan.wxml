<!--route-plan.wxml-->
<view class="page">
  <!-- 自定义导航栏 -->
  <view class="custom-nav-bar">
    <view class="nav-bar-content">
      <view class="nav-back" bindtap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <view class="nav-title">路线规划</view>
      <view class="nav-placeholder"></view>
    </view>
  </view>

  <!-- 地图容器 - 使用web-view加载高德地图 -->
  <web-view
    wx:if="{{useWebView}}"
    id="map-webview"
    src="{{mapUrl}}"
    class="map-container"
    bindmessage="onMapMessage"
  ></web-view>

  <!-- 备用原生地图 -->
  <map
    wx:else
    id="map"
    class="map-container"
    longitude="{{mapCenter.longitude}}"
    latitude="{{mapCenter.latitude}}"
    scale="{{mapScale}}"
    markers="{{markers}}"
    polyline="{{polylines}}"
    bindtap="onMapTap"
    bindmarkertap="onMarkerTap"
    bindinitialized="onMapReady"
    show-location="{{false}}"
    enable-3D="{{false}}"
    show-compass="{{false}}"
    enable-overlooking="{{false}}"
    enable-zoom="{{true}}"
    enable-scroll="{{true}}"
    enable-rotate="{{false}}"
    enable-satellite="{{false}}"
    enable-traffic="{{false}}"
  ></map>

  <!-- 控制面板 -->
  <view class="control-panel">
    <!-- 地点选择区域 -->
    <view class="location-section">
      <view class="section-title">地点选择</view>

      <!-- 起点选择 -->
      <view class="location-item">
        <view class="location-label">起点</view>
        <view class="location-input">
          <view class="autocomplete-container">
            <input
              type="text"
              value="{{startSearchText}}"
              bindinput="onStartSearchInput"
              bindfocus="onStartSearchFocus"
              bindblur="onStartSearchBlur"
              placeholder="请输入起点"
              class="location-input-field"
              disabled="{{isLoading}}"
            />
            <view wx:if="{{showStartSuggestions && filteredStartLocations.length > 0}}" class="suggestions-container">
              <view
                wx:for="{{filteredStartLocations}}"
                wx:key="vertex_id"
                class="suggestion-item"
                bindtap="selectStartLocation"
                data-location="{{item}}"
              >
                {{item.label}}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 终点选择 -->
      <view class="location-item">
        <view class="location-label">终点</view>
        <view class="location-input">
          <view class="autocomplete-container">
            <input
              type="text"
              value="{{endSearchText}}"
              bindinput="onEndSearchInput"
              bindfocus="onEndSearchFocus"
              bindblur="onEndSearchBlur"
              placeholder="请输入终点"
              class="location-input-field"
              disabled="{{isLoading}}"
            />
            <view wx:if="{{showEndSuggestions && filteredEndLocations.length > 0}}" class="suggestions-container">
              <view
                wx:for="{{filteredEndLocations}}"
                wx:key="vertex_id"
                class="suggestion-item"
                bindtap="selectEndLocation"
                data-location="{{item}}"
              >
                {{item.label}}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 途径点 -->
      <view class="waypoints-section" wx:if="{{waypoints.length > 0}}">
        <view class="waypoints-title">途径点</view>
        <view class="waypoint-item" wx:for="{{waypoints}}" wx:key="index">
          <view class="waypoint-name">{{item.name}}</view>
          <view class="waypoint-remove" bindtap="removeWaypoint" data-index="{{index}}">删除</view>
        </view>
      </view>

      <!-- 添加途径点按钮 -->
      <view class="add-waypoint-btn" bindtap="showWaypointPicker">
        <text class="add-icon">+</text>
        <text>添加途径点</text>
      </view>
    </view>

    <!-- 策略选择区域 -->
    <view class="strategy-section">
      <view class="section-title">出行策略</view>

      <!-- 交通方式选择 -->
      <view class="transport-modes">
        <view
          class="mode-btn {{selectedTransportMode === 'walking' ? 'active' : ''}}"
          bindtap="selectTransportMode"
          data-mode="walking"
        >
          步行
        </view>
        <view
          class="mode-btn {{selectedTransportMode === 'riding' ? 'active' : ''}}"
          bindtap="selectTransportMode"
          data-mode="riding"
        >
          骑行
        </view>
        <view
          class="mode-btn {{selectedTransportMode === 'driving' ? 'active' : ''}}"
          bindtap="selectTransportMode"
          data-mode="driving"
        >
          不限
        </view>
      </view>

      <!-- 策略选择 -->
      <view class="strategy-options">
        <view
          class="strategy-btn {{selectedStrategy === 0 ? 'active' : ''}}"
          bindtap="selectStrategy"
          data-strategy="0"
        >
          最短距离
        </view>
        <view
          class="strategy-btn {{selectedStrategy === 1 ? 'active' : ''}}"
          bindtap="selectStrategy"
          data-strategy="1"
        >
          最短时间
        </view>
        <view
          class="strategy-btn {{selectedStrategy === 3 ? 'active' : ''}}"
          bindtap="selectStrategy"
          data-strategy="3"
          wx:if="{{selectedTransportMode === 'driving'}}"
        >
          智能出行
        </view>
      </view>
    </view>

    <!-- 规划按钮 -->
    <view class="plan-section">
      <button
        class="plan-btn {{canPlan ? 'enabled' : 'disabled'}}"
        bindtap="planRoute"
        disabled="{{!canPlan || isLoading}}"
        loading="{{isLoading}}"
      >
        {{isLoading ? '规划中...' : '规划路线'}}
      </button>
    </view>

    <!-- 路线信息 -->
    <view class="route-info" wx:if="{{routeResult}}">
      <view class="section-title">路线信息</view>

      <!-- 距离和时间 -->
      <view class="route-summary">
        <view class="summary-item">
          <view class="summary-label">总距离</view>
          <view class="summary-value">{{routeResult.totalDistance}}</view>
        </view>
        <view class="summary-item" wx:if="{{routeResult.cyclingDistance}}">
          <view class="summary-label">骑行距离</view>
          <view class="summary-value">{{routeResult.cyclingDistance}}</view>
        </view>
        <view class="summary-item" wx:if="{{routeResult.walkingDistance}}">
          <view class="summary-label">步行距离</view>
          <view class="summary-value">{{routeResult.walkingDistance}}</view>
        </view>
        <view class="summary-item">
          <view class="summary-label">预计时间</view>
          <view class="summary-value">{{routeResult.totalTime}}</view>
        </view>
      </view>

      <!-- 路径详情 -->
      <view class="route-details" wx:if="{{routeResult.pathDetails && routeResult.pathDetails.length > 0}}">
        <view class="details-title">路径详情</view>
        <view class="detail-item" wx:for="{{routeResult.pathDetails}}" wx:key="index">
          <view class="detail-from">{{item.from_name}}</view>
          <view class="detail-arrow">→</view>
          <view class="detail-to">{{item.to_name}}</view>
          <view class="detail-info">
            <text class="detail-distance">{{item.distance}}</text>
            <text class="detail-time">{{item.time}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 途径点选择弹窗 -->
  <view class="waypoint-modal {{showWaypointModal ? 'show' : ''}}" wx:if="{{showWaypointModal}}">
    <view class="modal-mask" bindtap="hideWaypointPicker"></view>
    <view class="modal-content">
      <view class="modal-title">添加途径点</view>

      <!-- 搜索输入框 -->
      <view class="waypoint-search-container">
        <input
          type="text"
          value="{{waypointSearchText}}"
          bindinput="onWaypointSearchInput"
          bindfocus="onWaypointSearchFocus"
          placeholder="请输入途径点名称"
          class="waypoint-search-input"
        />
      </view>

      <!-- 搜索结果列表 -->
      <scroll-view class="location-list" scroll-y="true">
        <view
          class="location-option"
          wx:for="{{filteredWaypointLocations}}"
          wx:key="vertex_id"
          bindtap="selectWaypoint"
          data-location="{{item}}"
        >
          {{item.label}}
        </view>
        <view wx:if="{{filteredWaypointLocations.length === 0 && waypointSearchText}}" class="no-results">
          未找到相关地点
        </view>
      </scroll-view>

      <view class="modal-actions">
        <button class="cancel-btn" bindtap="hideWaypointPicker">取消</button>
      </view>
    </view>
  </view>
</view>
