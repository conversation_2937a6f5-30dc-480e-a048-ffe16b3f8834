#!/usr/bin/env python3
"""
测试数据库路径脚本
"""
import os

def test_paths():
    print("=== 路径测试 ===")
    
    # 当前脚本路径
    current_file = os.path.abspath(__file__)
    print(f"当前脚本路径: {current_file}")
    
    # 当前目录
    current_dir = os.path.dirname(current_file)
    print(f"当前目录: {current_dir}")
    
    # backend目录
    backend_dir = current_dir
    print(f"backend目录: {backend_dir}")
    
    # SQL文件路径
    sql_file = os.path.join(backend_dir, 'database', 'study_tour_system.sql')
    print(f"SQL文件路径: {sql_file}")
    
    # 检查文件是否存在
    if os.path.exists(sql_file):
        print("✅ SQL文件存在")
        print(f"文件大小: {os.path.getsize(sql_file)} 字节")
    else:
        print("❌ SQL文件不存在")
    
    # 检查scripts目录中的脚本路径计算
    print("\n=== scripts脚本路径计算 ===")
    scripts_dir = os.path.join(backend_dir, 'scripts')
    recreate_script = os.path.join(scripts_dir, 'recreate_database.py')
    print(f"重建脚本路径: {recreate_script}")
    
    if os.path.exists(recreate_script):
        print("✅ 重建脚本存在")
        
        # 模拟脚本中的路径计算
        script_dir = os.path.dirname(recreate_script)
        backend_dir_from_script = os.path.dirname(script_dir)
        sql_file_from_script = os.path.join(backend_dir_from_script, 'database', 'study_tour_system.sql')
        
        print(f"脚本目录: {script_dir}")
        print(f"从脚本计算的backend目录: {backend_dir_from_script}")
        print(f"从脚本计算的SQL文件路径: {sql_file_from_script}")
        
        if os.path.exists(sql_file_from_script):
            print("✅ 从脚本计算的SQL文件路径正确")
        else:
            print("❌ 从脚本计算的SQL文件路径错误")
    else:
        print("❌ 重建脚本不存在")

if __name__ == "__main__":
    test_paths()
