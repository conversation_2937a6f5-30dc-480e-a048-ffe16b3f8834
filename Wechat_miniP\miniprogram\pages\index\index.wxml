<!--index.wxml-->
<view class="page">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-bar-title">鸿雁智游</view>
  </view>

  <scroll-view class="scrollarea" scroll-y type="list">
    <!-- 轮播图区域 -->
    <swiper class="carousel" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{5000}}" duration="{{500}}">
      <swiper-item wx:for="{{carouselList}}" wx:key="index">
        <view class="carousel-item">
          <view class="carousel-title">{{item.title}}</view>
          <view class="carousel-desc">{{item.description}}</view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 功能卡片区域 - 新设计 -->
    <view class="features-new">
      <view class="section-title">我们的服务</view>
      <view class="feature-grid">
        <view class="feature-item" wx:for="{{features}}" wx:key="id" bindtap="navigateTo" data-url="{{item.route}}">
          <view class="feature-icon">{{item.icon}}</view>
          <view class="feature-text">{{item.title}}</view>
        </view>
      </view>
    </view>

    <!-- 热门景点推荐 -->
    <view class="popular-places">
      <view class="section-title">热门景点</view>
      <view class="places-list">
        <view class="place-item" wx:for="{{popularPlaces}}" wx:key="id" bindtap="goToPlaceDetail" data-id="{{item.id}}">
          <view class="place-image-container">
            <view class="place-name">{{item.name}}</view>
          </view>
          <view class="place-info">
            <view class="place-rating">
              <text class="rating-text">{{item.rating}}分</text>
              <text class="review-count">({{item.reviews}}条点评)</text>
            </view>
            <view class="place-tags">
              <text class="tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">{{tag}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
