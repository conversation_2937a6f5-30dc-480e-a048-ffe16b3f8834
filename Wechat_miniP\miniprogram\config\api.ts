// API配置文件
// 微信小程序API配置

// 开发环境配置
const DEV_CONFIG = {
  // 方案1：直接访问后端（需要关闭域名校验）
  API_BASE_URL: 'http://**************:5000/api',

  // 方案2：使用代理服务器（推荐）
  PROXY_API_URL: 'http://**************:3000/api',

  // 方案3：使用微信开发者工具的本地服务器
  LOCAL_API_URL: 'http://127.0.0.1:5000/api',

  // 备用IP地址
  BACKUP_IPS: [
    'http://**************:5000/api',
    'http://127.0.0.1:5000/api',
    'http://localhost:5000/api'
  ]
}

// 生产环境配置
const PROD_CONFIG = {
  // 生产环境请替换为实际的服务器域名
  API_BASE_URL: 'https://your-domain.com/api'
}

// 根据环境选择配置
const isDev = true // 开发环境设为true，生产环境设为false

// 选择使用的API方案
// 0: 直接访问后端（需要关闭域名校验）
// 1: 使用代理服务器（推荐）
// 2: 使用本地服务器
const API_STRATEGY = 0  // 先尝试直接连接

export const API_CONFIG = isDev ? DEV_CONFIG : PROD_CONFIG

// 获取当前使用的API基础URL
export const getApiBaseUrl = () => {
  if (!isDev) {
    return API_CONFIG.API_BASE_URL
  }

  switch (API_STRATEGY) {
    case 1:
      return DEV_CONFIG.PROXY_API_URL
    case 2:
      return DEV_CONFIG.LOCAL_API_URL
    default:
      return DEV_CONFIG.API_BASE_URL
  }
}

// 测试API连接
export const testApiConnection = async () => {
  return new Promise((resolve) => {
    wx.request({
      url: `${getApiBaseUrl()}/path/vertices`,
      method: 'GET',
      timeout: 5000,
      success: (response) => {
        if (response.statusCode === 200) {
          console.log('API连接测试成功')
          resolve(true)
        } else {
          console.error('API连接测试失败:', response.statusCode)
          resolve(false)
        }
      },
      fail: (error) => {
        console.error('API连接测试异常:', error)
        resolve(false)
      }
    })
  })
}

// 自动检测可用的API地址
export const detectAvailableApi = async () => {
  const urlsToTest = [API_CONFIG.API_BASE_URL]

  if (isDev && DEV_CONFIG.BACKUP_IPS) {
    urlsToTest.push(...DEV_CONFIG.BACKUP_IPS)
  }

  for (const url of urlsToTest) {
    try {
      console.log(`测试API地址: ${url}`)
      const isAvailable = await new Promise((resolve) => {
        wx.request({
          url: `${url}/path/vertices`,
          method: 'GET',
          timeout: 3000,
          success: (response) => {
            resolve(response.statusCode === 200)
          },
          fail: () => {
            resolve(false)
          }
        })
      })

      if (isAvailable) {
        console.log(`API地址可用: ${url}`)
        return url
      }
    } catch (error) {
      console.log(`API地址不可用: ${url}`, error)
    }
  }

  console.error('所有API地址都不可用')
  return null
}
