#!/usr/bin/env python3
"""
测试AIGC动画生成API
"""
import requests
import json

def test_aigc_animation():
    """测试AIGC动画生成"""
    
    # API端点
    url = "http://localhost:5000/api/ai/generate_travel_animation"
    
    # 测试数据
    test_data = {
        "article_id": 1,  # 假设存在ID为1的文章
        "animation_style": "温馨",
        "duration": "短片",
        "focus_elements": "风景,美食"
    }
    
    print("🚀 开始测试AIGC动画生成API...")
    print(f"📡 请求URL: {url}")
    print(f"📋 请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=test_data, timeout=60)
        
        print(f"\n📡 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功!")
            print(f"📋 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 检查是否包含AIGC内容
            if result.get('code') == 0 and result.get('data'):
                data = result['data']
                
                # 检查动画配置
                if 'animation' in data:
                    print(f"🎬 动画配置: {data['animation'].get('id', 'N/A')}")
                    if data['animation'].get('player_url'):
                        print(f"🎥 播放器URL: {data['animation']['player_url']}")
                
                # 检查生成的媒体
                if 'generated_media' in data:
                    media = data['generated_media']
                    image_count = len(media.get('images', []))
                    video_count = len(media.get('videos', []))
                    print(f"🎨 生成图片数量: {image_count}")
                    print(f"🎬 生成视频数量: {video_count}")
                    
                    # 显示前几个图片URL
                    for i, img in enumerate(media.get('images', [])[:3]):
                        print(f"🖼️  图片{i+1}: {img.get('url', 'N/A')}")
                    
                    # 显示前几个视频URL
                    for i, vid in enumerate(media.get('videos', [])[:3]):
                        print(f"🎥 视频{i+1}: {vid.get('url', 'N/A')}")
                
                print("✅ AIGC功能测试成功！")
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📋 错误响应: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时，AIGC生成可能需要更长时间")
    except requests.exceptions.ConnectionError:
        print("🔌 连接失败，请确保后端服务正在运行")
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

def test_article_exists():
    """测试文章是否存在"""
    url = "http://localhost:5000/api/articles"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            articles = response.json().get('data', [])
            print(f"📚 找到 {len(articles)} 篇文章")
            if articles:
                first_article = articles[0]
                print(f"📖 第一篇文章: ID={first_article.get('article_id')}, 标题={first_article.get('title')}")
                return first_article.get('article_id')
        else:
            print(f"❌ 获取文章列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取文章列表异常: {str(e)}")
    
    return None

if __name__ == "__main__":
    print("🧪 AIGC API 测试工具")
    print("=" * 50)
    
    # 首先检查是否有可用的文章
    article_id = test_article_exists()
    
    if article_id:
        print(f"\n🎯 使用文章ID: {article_id}")
        test_aigc_animation()
    else:
        print("\n⚠️  没有找到可用的文章，请先创建一些文章数据")
