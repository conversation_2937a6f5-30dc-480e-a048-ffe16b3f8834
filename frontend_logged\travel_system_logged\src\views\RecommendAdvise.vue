<template>
  <div class="recommend-container" v-loading="isLoading">
    <div class="controls-container">
      <!-- 推荐方式选择器 (标签形式) -->
      <div class="recommendation-tabs">
        <el-tabs v-model="recommendationType" @tab-change="handleRecommendationTypeChange" type="card">
          <el-tab-pane label="为您推荐" name="smart"></el-tab-pane>
          <el-tab-pane label="全部景点" name="all"></el-tab-pane>
          <el-tab-pane label="热门景点" name="popularity"></el-tab-pane>
          <el-tab-pane label="高评分景点" name="rating"></el-tab-pane>
          <el-tab-pane label="协同过滤推荐" name="collaborative" :disabled="!isUserLoggedIn"></el-tab-pane>
          <!-- <el-tab-pane label="混合推荐" name="hybrid" :disabled="!isUserLoggedIn"></el-tab-pane>
          <el-tab-pane label="高级混合推荐" name="advanced-hybrid" :disabled="!isUserLoggedIn"></el-tab-pane> -->
        </el-tabs>
      </div>

      <!-- 搜索按钮 -->
      <div class="search-button-container">
        <el-button
          type="primary"
          @click="searchPanelVisible = !searchPanelVisible"
          icon="Search"
          class="search-button"
          size="large"
          :round="true"
        >
          搜索
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选面板 (页面内) -->
    <div v-if="searchPanelVisible" class="search-filter-panel">
      <div class="search-box">
        <h4>景点搜索</h4>
        <el-autocomplete
          v-model="searchKeyword"
          :fetch-suggestions="fetchSearchSuggestions"
          placeholder="输入景点名称或关键词搜索"
          prefix-icon="Search"
          clearable
          @keyup.enter="handleSearch"
          @select="handleSuggestionSelect"
          :trigger-on-focus="false"
          :debounce="300"
        >
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
          <template #default="{ item }">
            <div class="suggestion-item">
              <span class="suggestion-name">{{ item.name }}</span>
              <span class="suggestion-type">{{ item.type }}</span>
            </div>
          </template>
        </el-autocomplete>
      </div>

      <div class="filter-section">
        <h4>景点类型</h4>
        <div class="keyword-tags-container">
          <el-check-tag
            v-for="tag in availableKeywords.type"
            :key="tag"
            :checked="filterKeywords.type.includes(tag)"
            @change="toggleKeyword('type', tag)"
            class="keyword-tag"
          >
            {{ tag }}
          </el-check-tag>
        </div>
      </div>

      <!-- 混合推荐权重设置 (仅在混合推荐模式下显示) -->
      <div v-if="recommendationType === 'hybrid' || recommendationType === 'advanced-hybrid'" class="filter-section">
        <h4>推荐权重设置</h4>
        <div class="weight-slider">
          <span>内容推荐权重:</span>
          <el-slider v-model="weights.content" :min="0" :max="2" :step="0.1" :format-tooltip="formatWeight"></el-slider>
        </div>
        <div class="weight-slider">
          <span>协同过滤权重:</span>
          <el-slider v-model="weights.collaborative" :min="0" :max="2" :step="0.1" :format-tooltip="formatWeight"></el-slider>
        </div>
        <div class="weight-slider">
          <span>热度权重:</span>
          <el-slider v-model="weights.popularity" :min="0" :max="2" :step="0.1" :format-tooltip="formatWeight"></el-slider>
        </div>
      </div>

      <div class="filter-actions">
        <el-button type="primary" @click="handleConfirmFilter">应用筛选</el-button>
        <el-button @click="resetFilters">重置</el-button>
        <el-button @click="searchPanelVisible = false">关闭</el-button>
      </div>
    </div>

    <div class="results-section">
      <!-- 推荐方式标题 -->
      <div class="recommendation-title">
        <h2>{{ getRecommendationTypeTitle() }}</h2>
      </div>

      <!-- 景点推荐 -->
      <div class="category-section">
        <div v-if="!isLoading && filteredAttractions.length > 0" class="place-grid">
          <div
            v-for="item in filteredAttractions"
            :key="'attr-' + item.location_id"
            class="place-card"
            @click="viewDetail(item.location_id)"
          >
            <img :src="item.Img" alt="场所图片" class="place-image" />
            <div class="place-details">
              <h3 class="place-name">{{ item.name }}</h3>
              <p class="place-address">{{ item.address }}</p>
              <div class="rating-section">
                <span class="rating-value">⭐ {{ item.rating ?? item.evaluation }}</span>
                <span class="rating-count">({{ item.reviews ?? item.popularity }})</span>
              </div>

              <!-- 推荐原因 (仅在个性化推荐时显示) -->
              <div v-if="showRecommendationReason(item)" class="recommendation-reason">
                <span class="reason-icon" :class="getRecommendationReasonIcon(item)"></span>
                <span class="reason-text">{{ getRecommendationReason(item) }}</span>
              </div>

              <div class="tags">
                <span
                  v-for="(tag, index) in item.tags || (item.keyword ? item.keyword.split(',') : [])"
                  :key="index"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-if="!isLoading && filteredAttractions.length === 0 && allAttractions.length > 0" description="当前筛选条件下没有找到合适的地点"></el-empty>
        <el-empty v-if="!isLoading && allAttractions.length === 0" description="暂无地点推荐"></el-empty>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import {
  ElButton, ElCheckTag, ElEmpty, ElMessage, vLoading,
  ElSlider, ElTabs, ElTabPane, ElAutocomplete
} from 'element-plus';
import {
  getPopularRecommendations, getTopRatedRecommendations,
  getContentRecommendations, getCollaborativeRecommendations,
  getHybridRecommendations, getAdvancedHybridRecommendations,
  getForYouRecommendations, getAllLocationsByPopularity,
  refreshRecommendationCache
} from '@/api/recommend';
import { updateLocationBrowseCount } from '@/api/location';
import axios from 'axios';
import { useStore } from 'vuex';

import forbiddenCityImg from '@/assets/forbidden_city.jpg';


// 初始化数据
const allAttractions = ref([]);
const filteredAttractions = ref([]);
const router = useRouter();
const store = useStore();
const isLoading = ref(false);
const searchPanelVisible = ref(false);
const searchKeyword = ref('');

// 推荐相关变量
const recommendationType = ref('smart'); // 默认为智能推荐
const sortOrder = ref(0); // 默认按人气排序
// 检查用户是否已登录
const isUserLoggedIn = computed(() => {
  try {
    // 首先尝试从Vuex store获取登录状态
    if (store?.getters?.isAuthenticated) {
      return true;
    }

    // 如果Vuex中没有，尝试从localStorage获取
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
      // 找到了用户信息，同步到Vuex
      try {
        const userData = JSON.parse(currentUser);
        const token = localStorage.getItem('token');
        if (token && userData) {
          // 同步到Vuex
          store.dispatch('login', { user: userData, token });
          return true;
        }
      } catch (e) {
        console.error('解析用户信息出错:', e);
      }
    }

    // 最后尝试从App.vue中可能存储的位置获取
    if (localStorage.getItem('currentUser')) {
      return true;
    }

    return false;
  } catch (error) {
    console.warn('无法获取用户登录状态:', error);
    return false;
  }
});

// 混合推荐权重
const weights = reactive({
  content: 1.0,
  collaborative: 1.0,
  popularity: 0.5
});

// 格式化权重显示
const formatWeight = (val) => {
  return val.toFixed(1);
};

// 获取推荐类型标题
const getRecommendationTypeTitle = () => {
  const titles = {
    'smart': isUserLoggedIn.value ? '为您智能推荐的地点' : '热门地点推荐',
    'all': '全部地点',
    'popularity': '热门地点推荐',
    'rating': '高评分地点推荐',
    'content': '基于您浏览历史的内容推荐',
    'collaborative': '与您相似用户喜欢的地点',
    'hybrid': '个性化混合推荐',
    'advanced-hybrid': '高级个性化推荐'
  };
  return titles[recommendationType.value] || '地点推荐';
};

// 处理推荐类型变更
const handleRecommendationTypeChange = () => {
  fetchRecommendations();
};

// 获取推荐
const fetchRecommendations = async () => {
  isLoading.value = true;
  try {
    let response;
    let userId = null;

    try {
      // 首先尝试从Vuex store获取用户ID
      if (store?.getters?.userId) {
        userId = store.getters.userId;
      } else if (store?.state?.user?.userId) {
        userId = store.state.user.userId;
      } else {
        // 如果Vuex中没有，尝试从localStorage获取
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          const userData = JSON.parse(currentUser);
          userId = userData.user_id || userData.userId;

          // 同步到Vuex
          const token = localStorage.getItem('token');
          if (token && userData) {
            store.dispatch('login', { user: userData, token });
          }
        }
      }

      console.log('获取到用户ID:', userId);
    } catch (error) {
      console.warn('无法获取用户ID:', error);
    }

    switch (recommendationType.value) {
      case 'smart':
        // 智能推荐：根据用户登录状态选择不同的推荐方式
        if (userId) {
          // 用户已登录，使用"为您推荐"API
          response = await getForYouRecommendations({
            is_guest: false,
            user_id: userId
          });
        } else {
          // 用户未登录，使用游客模式的"为您推荐"API
          response = await getForYouRecommendations({
            is_guest: true
          });
        }
        break;

      case 'all':
        // 获取所有地点，按照热度排序
        response = await getAllLocationsByPopularity({
          limit: 300 // 增加limit参数，获取更多地点
          // 移除type参数，获取所有类型的地点
        });
        break;

      case 'popularity':
        response = await getPopularRecommendations({
          limit: 50
          // 移除type参数，获取所有类型的地点
        });
        break;

      case 'rating':
        response = await getTopRatedRecommendations({
          limit: 50
          // 移除type参数，获取所有类型的地点
        });
        break;

      case 'content':
        if (!userId) {
          ElMessage.warning('请先登录以获取个性化推荐');
          recommendationType.value = 'popularity';
          return fetchRecommendations();
        }
        response = await getContentRecommendations({
          user_id: userId,
          limit: 50
        });
        break;

      case 'collaborative':
        if (!userId) {
          ElMessage.warning('请先登录以获取个性化推荐');
          recommendationType.value = 'popularity';
          return fetchRecommendations();
        }
        response = await getCollaborativeRecommendations({
          user_id: userId,
          limit: 50
        });
        break;

      case 'hybrid':
        if (!userId) {
          ElMessage.warning('请先登录以获取个性化推荐');
          recommendationType.value = 'popularity';
          return fetchRecommendations();
        }
        response = await getHybridRecommendations({
          user_id: userId,
          limit: 50,
          weights: weights
        });
        break;

      case 'advanced-hybrid':
        if (!userId) {
          ElMessage.warning('请先登录以获取个性化推荐');
          recommendationType.value = 'popularity';
          return fetchRecommendations();
        }
        response = await getAdvancedHybridRecommendations({
          user_id: userId,
          limit: 50,
          weights: weights
        });
        break;

      default:
        // 默认使用热门推荐
        response = await getPopularRecommendations({
          limit: 50
          // 移除type参数，获取所有类型的地点
        });
    }

    if (response.code === 0 && response.data?.recommendations) {
      // 处理返回的景点数据
      allAttractions.value = response.data.recommendations.map(item => {
        // 构建完整的图片URL - 严格按照后端返回的image_url字段
        let imageUrl;

        // 调试输出
        console.log(`处理景点 ${item.name} (ID: ${item.location_id}) 的图片URL:`, item.image_url);

        if (item.image_url) {
          // 后端已经处理过image_url，直接构建完整URL
          if (item.image_url.startsWith('http')) {
            // 如果已经是完整URL，直接使用
            imageUrl = item.image_url;
            console.log('使用完整URL:', imageUrl);
          } else {
            // 如果是相对路径（后端已经处理为/uploads/locations/xxx.jpg格式），添加服务器地址
            imageUrl = `http://localhost:5000${item.image_url}`;
            console.log('使用后端处理的相对路径:', imageUrl);
          }
        } else {
          // 没有图片URL时使用默认图片
          imageUrl = `http://localhost:5000/uploads/locations/default_location.jpg`;
          console.log('使用默认图片');
        }

        return {
          ...item,
          rating: item.evaluation, // 确保评分字段一致
          Img: imageUrl
        };
      });

      // 应用筛选
      filteredAttractions.value = [...allAttractions.value];

      if (allAttractions.value.length === 0) {
        ElMessage.info('暂无地点推荐');
      }
    } else {
      ElMessage.warning(response.message || '获取地点推荐失败');
      allAttractions.value = [];
      filteredAttractions.value = [];
    }
  } catch (error) {
    console.error('获取地点推荐出错:', error);
    ElMessage.error('获取地点推荐时发生错误');
    allAttractions.value = [];
    filteredAttractions.value = [];
  } finally {
    isLoading.value = false;
  }
};
// 可用的筛选关键词 (根据数据库中实际存在的关键词)
const availableKeywords = ref({
  type: [
    // 景点关键词
    '历史', '公园', '博物馆', '登山', '海滨', '湖泊', '古迹', '自然风光', '主题公园',
    // 学校关键词
    '综合', '理工', '师范', '农业', '医药', '财经', '政法', '艺术', '语言', '体育'
  ]
});

// 用户选择的筛选关键词 (使用 reactive)
const filterKeywords = reactive({
  type: []
});

// const userInfo = ref(null); // 如果不需要混合推荐，可以注释掉

// // 获取当前用户信息 (如果不需要混合推荐，可以注释掉)
// const fetchUserInfo = async () => { ... };

// 获取初始推荐 (分别获取景点和美食)
// const fetchInitialRecommendations = async () => {
//   isLoading.value = true;
//   try {
//     // 并行获取景点和美食
//     const [attrRes, foodRes] = await Promise.all([
//       getPopularRecommendations({ type: 'attraction', limit: 50 }),
//       getPopularRecommendations({ type: 'food', limit: 50 })
//     ]);

//     // 处理景点数据
//     if (attrRes.code === 0 && attrRes.data?.recommendations) {
//       allAttractions.value = attrRes.data.recommendations;
//       filteredAttractions.value = applyFrontendFilter(allAttractions.value, 'attraction');
//     } else {
//       allAttractions.value = [];
//       filteredAttractions.value = [];
//       console.warn('获取景点推荐列表失败:', attrRes.message || '未知错误');
//     }

//     // 处理美食数据
//     if (foodRes.code === 0 && foodRes.data?.recommendations) {
//       allFood.value = foodRes.data.recommendations;
//       filteredFood.value = applyFrontendFilter(allFood.value, 'food');
//     } else {
//       allFood.value = [];
//       filteredFood.value = [];
//        console.warn('获取美食推荐列表失败:', foodRes.message || '未知错误');
//     }

//      if (filteredAttractions.value.length === 0 && filteredFood.value.length === 0 && allAttractions.value.length === 0 && allFood.value.length === 0) {
//          ElMessage.warning('未能获取到任何推荐信息');
//      }

//   } catch (error) {
//     console.error("获取推荐失败:", error);
//     ElMessage.error('加载推荐数据时出错');
//     allAttractions.value = [];
//     filteredAttractions.value = [];
//     allFood.value = [];
//     filteredFood.value = [];
//   } finally {
//     isLoading.value = false;
//   }
// };

// 前端筛选逻辑
const applyFrontendFilter = (dataToFilter) => {
  const selectedTypes = filterKeywords.type;
  const keyword = searchKeyword.value.trim().toLowerCase();

  return dataToFilter.filter(item => {
    // 关键词搜索筛选
    if (keyword && !(
      (item.name && item.name.toLowerCase().includes(keyword)) ||
      (item.address && item.address.toLowerCase().includes(keyword)) ||
      (item.keyword && item.keyword.toLowerCase().includes(keyword))
    )) {
      return false;
    }

    // 类型筛选
    if (selectedTypes.length > 0) {
      const itemKeywords = item.keyword ? item.keyword.split(',').map(k => k.trim()) : [];
      return selectedTypes.some(selectedType => itemKeywords.includes(selectedType));
    }

    return true;
  });
};

// 获取搜索建议
const fetchSearchSuggestions = async (queryString, callback) => {
  if (!queryString || queryString.trim().length < 1) {
    callback([]);
    return;
  }

  try {
    const response = await axios.get('http://localhost:5000/api/locations/fuzzy_search', {
      params: {
        query: queryString.trim(),
        limit: 10
      }
    });

    if (response.data && response.data.code === 0 && response.data.data) {
      const suggestions = response.data.data.map(location => ({
        value: location.name,
        name: location.name,
        type: location.type === 0 ? '学校' : '景点',
        location_id: location.location_id,
        address: location.address,
        keyword: location.keyword
      }));
      callback(suggestions);
    } else {
      callback([]);
    }
  } catch (error) {
    console.error('获取搜索建议失败:', error);
    callback([]);
  }
};

// 处理建议选择
const handleSuggestionSelect = (item) => {
  searchKeyword.value = item.value;
  handleSearch();
};

// 处理搜索
const handleSearch = () => {
  // 应用前端筛选
  filteredAttractions.value = applyFrontendFilter(allAttractions.value);

  // 如果搜索结果为空，显示提示
  if (filteredAttractions.value.length === 0 && allAttractions.value.length > 0) {
    ElMessage.info('没有找到符合搜索条件的景点');
  }
};

// 切换关键词选中状态
const toggleKeyword = (category, keyword) => {
  const selectedList = filterKeywords[category];
  const index = selectedList.indexOf(keyword);

  if (index === -1) {
    selectedList.push(keyword);
  } else {
    selectedList.splice(index, 1);
  }
};

// 处理筛选确认
const handleConfirmFilter = async () => {
  isLoading.value = true;

  try {
    // 构建查询参数
    const queryParams = {};

    // 添加关键词筛选
    if (filterKeywords.type.length > 0) {
      queryParams.keyword = filterKeywords.type.join(',');
    }

    // 添加搜索关键词
    if (searchKeyword.value.trim()) {
      queryParams.search = searchKeyword.value.trim();
    }

    // 添加排序方式
    queryParams.sortOrder = sortOrder.value;

    // 不设置类型，获取所有类型的地点
    // queryParams.type = 1;

    // 根据当前推荐类型选择API
    let response;
    let userId = null;

    try {
      // 首先尝试从Vuex store获取用户ID
      if (store?.getters?.userId) {
        userId = store.getters.userId;
      } else if (store?.state?.user?.userId) {
        userId = store.state.user.userId;
      } else {
        // 如果Vuex中没有，尝试从localStorage获取
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          const userData = JSON.parse(currentUser);
          userId = userData.user_id || userData.userId;

          // 同步到Vuex
          const token = localStorage.getItem('token');
          if (token && userData) {
            store.dispatch('login', { user: userData, token });
          }
        }
      }

      console.log('筛选时获取到用户ID:', userId);
    } catch (error) {
      console.warn('无法获取用户ID:', error);
    }

    if (recommendationType.value === 'smart') {
      // 智能推荐：根据用户登录状态选择不同的推荐方式
      if (userId) {
        // 用户已登录，使用"为您推荐"API
        response = await getForYouRecommendations({
          is_guest: false,
          user_id: userId,
          keyword: queryParams.keyword
        });
      } else {
        // 用户未登录，使用游客模式的"为您推荐"API
        response = await getForYouRecommendations({
          is_guest: true,
          keyword: queryParams.keyword
        });
      }
    } else if (recommendationType.value === 'all') {
      // 获取所有地点
      response = await getAllLocationsByPopularity({
        ...queryParams,
        limit: 300 // 增加limit参数，获取更多地点
      });
    } else if (recommendationType.value === 'popularity') {
      response = await getPopularRecommendations(queryParams);
    } else if (recommendationType.value === 'rating') {
      response = await getTopRatedRecommendations(queryParams);
    } else if (recommendationType.value === 'content' && userId) {
      response = await getContentRecommendations({
        user_id: userId,
        limit: 50,
        keyword: queryParams.keyword
      });
    } else if (recommendationType.value === 'collaborative' && userId) {
      response = await getCollaborativeRecommendations({
        user_id: userId,
        limit: 50,
        keyword: queryParams.keyword
      });
    } else if (recommendationType.value === 'hybrid' && userId) {
      response = await getHybridRecommendations({
        user_id: userId,
        limit: 50,
        weights: weights,
        keyword: queryParams.keyword
      });
    } else if (recommendationType.value === 'advanced-hybrid' && userId) {
      response = await getAdvancedHybridRecommendations({
        user_id: userId,
        limit: 50,
        weights: weights,
        keyword: queryParams.keyword
      });
    } else {
      // 默认使用热门推荐
      response = await getPopularRecommendations(queryParams);
    }

    if (response.code === 0 && response.data?.recommendations) {
      // 处理返回的景点数据
      allAttractions.value = response.data.recommendations.map(item => {
        // 构建完整的图片URL
        let imageUrl;

        // 调试输出
        console.log(`筛选后处理景点 ${item.name} 的图片URL:`, item.image_url);

        if (item.image_url && item.image_url.startsWith('/uploads')) {
          // 如果是以/uploads开头的相对路径
          imageUrl = `http://localhost:5000${item.image_url}`;
          console.log('使用相对路径:', imageUrl);
        } else if (item.image_url && item.image_url.startsWith('http')) {
          // 如果已经是完整URL
          imageUrl = item.image_url;
          console.log('使用完整URL:', imageUrl);
        } else if (item.location_id) {
          // 尝试使用基于ID的图片路径
          imageUrl = `http://localhost:5000/uploads/locations/${item.location_id}.jpg`;
          console.log('使用ID路径:', imageUrl);
        } else {
          // 使用默认图片
          imageUrl = forbiddenCityImg;
          console.log('使用默认图片');
        }

        return {
          ...item,
          rating: item.evaluation, // 确保评分字段一致
          Img: imageUrl
        };
      });

      // 应用前端筛选
      filteredAttractions.value = applyFrontendFilter(allAttractions.value);
    } else {
      ElMessage.warning(response.message || '获取筛选结果失败');
      filteredAttractions.value = [];
    }
  } catch (error) {
    console.error('筛选出错:', error);
    ElMessage.error('筛选时发生错误');
    filteredAttractions.value = [];
  } finally {
    isLoading.value = false;

    // 更新提示信息逻辑
    const hasActiveFilters = filterKeywords.type.length > 0;

    if (filteredAttractions.value.length === 0 && allAttractions.value.length > 0 && hasActiveFilters) {
      ElMessage.info('当前筛选条件下没有找到合适的地点');
    } else if (filteredAttractions.value.length === 0 && allAttractions.value.length === 0) {
      ElMessage.info('暂无地点推荐');
    }
  }

  // 不关闭侧边栏
};

// 重置筛选条件
const resetFilters = () => {
    // 重置搜索关键词
    searchKeyword.value = '';

    // 重置筛选类型
    filterKeywords.type = [];

    // 重置排序方式
    sortOrder.value = 0;

    // 如果是混合推荐，重置权重
    if (recommendationType.value === 'hybrid' || recommendationType.value === 'advanced-hybrid') {
      weights.content = 1.0;
      weights.collaborative = 1.0;
      weights.popularity = 0.5;
    }

    // 重置后立即应用筛选
    handleConfirmFilter();
};

// 查看详情
const viewDetail = async (locationId) => {
  try {
    // 获取当前用户ID
    let userId = null;

    try {
      // 首先尝试从Vuex store获取用户ID
      if (store?.getters?.userId) {
        userId = store.getters.userId;
      } else if (store?.state?.user?.userId) {
        userId = store.state.user.userId;
      } else {
        // 如果Vuex中没有，尝试从localStorage获取
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          try {
            const userData = JSON.parse(currentUser);
            userId = userData.user_id || userData.userId;

            // 同步到Vuex
            const token = localStorage.getItem('token');
            if (token && userData) {
              store.dispatch('login', { user: userData, token });
            }
          } catch (e) {
            console.warn('解析用户信息失败:', e);
          }
        }
      }

      console.log('查看详情时获取到用户ID:', userId);
    } catch (error) {
      console.warn('无法获取用户ID:', error);
    }

    // 先跳转到详情页，不等待浏览计数更新
    console.log(`跳转到地点详情页: ${locationId}`);
    router.push({
      name: 'LocationDetail',
      params: { id: locationId }
    });

    // 如果用户已登录，异步更新浏览计数（不阻塞页面跳转）
    if (userId) {
      setTimeout(async () => {
        try {
          const response = await updateLocationBrowseCount(locationId, { user_id: userId });
          console.log(`已更新地点 ${locationId} 的浏览计数:`, response);
        } catch (error) {
          console.error('更新浏览计数失败:', error);
          // 不影响用户体验，静默失败
        }
      }, 100);
    }
  } catch (error) {
    console.error('跳转到详情页失败:', error);
    ElMessage.error('无法查看详情，请稍后再试');
  }
};

// 判断是否显示推荐原因
const showRecommendationReason = (item) => {
  // 只在个性化推荐模式下显示推荐原因
  const personalizedModes = ['content', 'collaborative', 'hybrid', 'advanced-hybrid'];
  if (!personalizedModes.includes(recommendationType.value)) {
    return false;
  }

  // 检查是否有推荐分数
  return item.content_score !== undefined ||
         item.collaborative_score !== undefined ||
         item.hybrid_score !== undefined;
};

// 获取推荐原因图标
const getRecommendationReasonIcon = (item) => {
  if (recommendationType.value === 'content' || item.content_score > 0.5) {
    return 'el-icon-star-on';
  } else if (recommendationType.value === 'collaborative' || item.collaborative_score > 0.5) {
    return 'el-icon-user';
  } else {
    return 'el-icon-data-analysis';
  }
};

// 获取推荐原因文本
const getRecommendationReason = (item) => {
  if (recommendationType.value === 'content' || (item.content_score && item.content_score > 0.5)) {
    return '根据您的浏览历史推荐';
  } else if (recommendationType.value === 'collaborative' || (item.collaborative_score && item.collaborative_score > 0.5)) {
    return '与您相似的用户也喜欢此景点';
  } else if (recommendationType.value === 'hybrid' || recommendationType.value === 'advanced-hybrid') {
    return '基于多种因素为您推荐';
  } else {
    return '推荐理由';
  }
};

// 刷新推荐系统缓存
const refreshCache = async (force = false) => {
  try {
    isLoading.value = true;
    const response = await refreshRecommendationCache({ force });
    if (response.code === 0) {
      ElMessage.success('推荐系统缓存已刷新');
    } else {
      ElMessage.warning(response.message || '刷新缓存失败');
    }
  } catch (error) {
    console.error('刷新缓存出错:', error);
    ElMessage.error('刷新缓存时发生错误');
  } finally {
    isLoading.value = false;
  }
};

// 组件挂载时执行
onMounted(async () => {
  // 检查登录状态
  console.log('当前登录状态:', isUserLoggedIn.value);

  // 如果用户已登录，在控制台输出用户信息
  if (isUserLoggedIn.value) {
    try {
      const userId = store?.state?.user?.userId || JSON.parse(localStorage.getItem('currentUser'))?.user_id;
      console.log('当前用户ID:', userId);
    } catch (error) {
      console.warn('无法获取用户ID:', error);
    }
  }

  // 刷新缓存（不强制）
  await refreshCache(false);

  // 获取初始数据
  fetchRecommendations();
});

// 当页面被激活时重新获取数据（从详情页返回时）
onActivated(() => {
  console.log('RecommendAdvise 页面被激活，重新获取数据');
  fetchRecommendations();
});

</script>

<style scoped>
.recommend-container {
  padding: 20px;
  max-width: 1200px;
  margin: 20px auto;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
}

.controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.recommendation-tabs {
  flex-grow: 1;
  margin-right: 20px;
}

.search-button-container {
  margin-left: auto;
}

.search-button {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 搜索和筛选面板样式 */
.search-filter-panel {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.search-box {
  margin-bottom: 20px;
}

.search-box h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.keyword-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px dashed #e0e0e0;
}

.filter-actions .el-button {
  margin-left: 10px;
}

.recommendation-title {
  margin-bottom: 25px;
  text-align: center;
}

.recommendation-title h2 {
  font-size: 24px;
  color: #303133;
  font-weight: 600;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.recommendation-title h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: #409EFF;
  border-radius: 3px;
}

.filter-section {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eaeaea;
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.keyword-tag {
  margin-right: 5px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.keyword-tag:hover {
  transform: translateY(-2px);
}

.sort-radio-group {
  display: flex;
  flex-direction: column;
}

.sort-radio-group .el-radio {
  margin-bottom: 10px;
  margin-left: 0;
}

.weight-slider {
  margin-bottom: 15px;
}

.weight-slider span {
  display: block;
  margin-bottom: 8px;
  color: #606266;
}

.results-section {
  margin-top: 20px;
}

.category-section {
  margin-bottom: 30px;
}

.place-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
}

.place-card {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.place-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.place-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.place-details {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.place-name {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.place-address {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.rating-value {
  font-size: 16px;
  font-weight: 600;
  color: #f7ba2a;
  margin-right: 8px;
}

.rating-count {
  color: #909399;
  font-size: 14px;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: auto;
}

.tag {
  background-color: #f5f7fa;
  color: #606266;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  transition: all 0.2s;
}

.tag:hover {
  background-color: #ecf5ff;
  color: #409EFF;
}

/* 推荐原因样式 */
.recommendation-reason {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 6px 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  font-size: 13px;
}

.reason-icon {
  margin-right: 6px;
  font-size: 14px;
}

.reason-text {
  line-height: 1.4;
}

/* 标签样式美化 */
:deep(.el-tabs__item) {
  font-size: 15px;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  transition: all 0.3s;
}

:deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #f0f0f0;
}

:deep(.el-tabs__active-bar) {
  height: 3px;
  border-radius: 3px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  border-bottom-color: #fff;
  background-color: #fff;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border-radius: 4px 4px 0 0;
  margin-right: 4px;
}

/* 搜索建议样式 */
.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.suggestion-name {
  font-weight: 500;
  color: #303133;
}

.suggestion-type {
  font-size: 12px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .controls-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .recommendation-tabs,
  .filter-button-container {
    width: 100%;
    margin-bottom: 15px;
    margin-right: 0;
  }

  .place-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 15px;
  }
}

</style>