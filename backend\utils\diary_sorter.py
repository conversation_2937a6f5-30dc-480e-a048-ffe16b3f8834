"""
游记排序工具类

该模块实现了各种游记排序算法，包括：
1. 快速排序
2. 堆排序
3. Top-K排序
"""

import heapq
from typing import List, Callable, Any
import random
from models.article import Article

class DiarySorter:
    """游记排序工具类"""
    
    @staticmethod
    def quick_sort(diaries: List[Article], key_func: Callable[[Article], Any], reverse: bool = False) -> List[Article]:
        """
        使用快速排序算法对游记列表进行排序
        
        Args:
            diaries: 游记列表
            key_func: 排序关键字函数，例如 lambda x: x.evaluation
            reverse: 是否降序排序，默认为False（升序）
            
        Returns:
            排序后的游记列表
        """
        if not diaries:
            return []
            
        # 创建副本，避免修改原始列表
        diaries_copy = diaries.copy()
        
        def _quick_sort(arr, low, high):
            if low < high:
                # 分区操作，返回分区点
                pivot_index = _partition(arr, low, high)
                # 对分区点左侧子数组进行递归排序
                _quick_sort(arr, low, pivot_index - 1)
                # 对分区点右侧子数组进行递归排序
                _quick_sort(arr, pivot_index + 1, high)
                
        def _partition(arr, low, high):
            # 选择随机元素作为pivot，减少最坏情况的发生
            pivot_index = random.randint(low, high)
            arr[pivot_index], arr[high] = arr[high], arr[pivot_index]
            
            pivot = key_func(arr[high])
            i = low - 1
            
            for j in range(low, high):
                # 根据reverse参数决定比较方式
                if (not reverse and key_func(arr[j]) <= pivot) or (reverse and key_func(arr[j]) >= pivot):
                    i += 1
                    arr[i], arr[j] = arr[j], arr[i]
                    
            arr[i + 1], arr[high] = arr[high], arr[i + 1]
            return i + 1
            
        _quick_sort(diaries_copy, 0, len(diaries_copy) - 1)
        return diaries_copy
    
    @staticmethod
    def top_k_sort(diaries: List[Article], k: int, key_func: Callable[[Article], Any], reverse: bool = True) -> List[Article]:
        """
        只排序前K个元素的高效算法
        
        Args:
            diaries: 游记列表
            k: 需要排序的前K个元素
            key_func: 排序关键字函数，例如 lambda x: x.evaluation
            reverse: 是否降序排序，默认为True（降序）
            
        Returns:
            排序后的前K个游记
        """
        if not diaries:
            return []
            
        if k >= len(diaries):
            # 如果k大于等于列表长度，直接使用完全排序
            return DiarySorter.quick_sort(diaries, key_func, reverse)
            
        # 使用堆排序找出前K个元素
        if reverse:
            # 降序排序，使用最小堆找出最大的K个元素
            heap = [(key_func(diary), i, diary) for i, diary in enumerate(diaries[:k])]
            heapq.heapify(heap)
            
            # 遍历剩余元素
            for i, diary in enumerate(diaries[k:], k):
                key = key_func(diary)
                if key > heap[0][0]:
                    # 如果当前元素比堆顶元素大，替换堆顶元素
                    heapq.heapreplace(heap, (key, i, diary))
                    
            # 从堆中取出元素，并按降序排列
            result = [item[2] for item in sorted(heap, reverse=True)]
        else:
            # 升序排序，使用最大堆找出最小的K个元素
            heap = [(- key_func(diary), i, diary) for i, diary in enumerate(diaries[:k])]
            heapq.heapify(heap)
            
            # 遍历剩余元素
            for i, diary in enumerate(diaries[k:], k):
                key = - key_func(diary)
                if key > heap[0][0]:
                    # 如果当前元素比堆顶元素大，替换堆顶元素
                    heapq.heapreplace(heap, (key, i, diary))
                    
            # 从堆中取出元素，并按升序排列
            result = [item[2] for item in sorted(heap, key=lambda x: -x[0])]
            
        return result
