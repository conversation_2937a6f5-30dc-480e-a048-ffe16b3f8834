"""
增强版旅游动画生成服务
集成真正的AIGC文生图和文生视频功能
"""

import os
import json
import uuid
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from models.article import Article
from services.ai_service import AIService
from services.real_aigc_service import RealAIGCService

class EnhancedTravelAnimationService:
    """增强版旅游动画生成服务"""

    def __init__(self):
        """初始化服务"""
        self.ai_service = AIService()
        self.aigc_service = RealAIGCService()
        self.output_dir = os.path.join('static', 'travel_animations')

        # 确保目录存在
        os.makedirs(self.output_dir, exist_ok=True)

    def generate_travel_animation(self, article: Article, animation_style: str, duration: str, focus_elements: str) -> Dict[str, Any]:
        """
        生成完整的旅游动画

        Args:
            article: 文章对象
            animation_style: 动画风格（温馨、活泼、文艺、大气）
            duration: 动画时长（短片、中等、长片）
            focus_elements: 重点元素

        Returns:
            完整的动画生成结果
        """
        try:
            print(f"开始生成旅游动画: {article.title}")

            # 1. 提取文章内容
            content_data = self._extract_article_content(article)
            if not content_data:
                return self._generate_error_response("无法提取文章内容")

            # 2. 生成动画脚本
            animation_script = self._generate_enhanced_script(content_data, animation_style, duration, focus_elements)

            # 3. 生成配套图片
            generated_images = self._generate_scene_images(animation_script, content_data)

            # 4. 生成视频片段（可选）
            generated_videos = self._generate_scene_videos(animation_script, content_data, duration)

            # 5. 组装最终结果
            final_result = {
                'animation_script': {
                    'total_duration': self._get_duration_text(duration),
                    'style': animation_style,
                    'scenes': animation_script.get('scenes', [])
                },
                'generated_media': {
                    'images': generated_images,
                    'videos': generated_videos
                },
                'expanded_content': self._generate_expanded_content(content_data, animation_style, focus_elements),
                'video_caption': self._generate_video_caption(content_data, animation_style),
                'media_usage': self._generate_media_usage_guide(),
                'creation_info': {
                    'created_at': datetime.now().isoformat(),
                    'article_id': article.article_id,
                    'generation_id': str(uuid.uuid4())
                }
            }

            print(f"旅游动画生成完成: {len(generated_images)}张图片, {len(generated_videos)}个视频")
            return final_result

        except Exception as e:
            print(f"生成旅游动画失败: {e}")
            import traceback
            traceback.print_exc()
            return self._generate_error_response(f"生成失败: {str(e)}")

    def _extract_article_content(self, article: Article) -> Optional[Dict]:
        """提取文章内容"""
        try:
            # 解压文章内容
            content = article.content
            if article.huffman_codes:
                from services.article_service import ArticleService
                service = ArticleService()
                huffman_codes = json.loads(article.huffman_codes)
                content = service.decompress_text(article.content, huffman_codes)

            if isinstance(content, bytes):
                content = content.decode('utf-8')

            # 收集媒体资源
            images = []
            for i in range(1, 7):
                url = article.image_url if i == 1 else getattr(article, f'image_url_{i}', None)
                if url:
                    images.append(url)

            videos = []
            for i in range(1, 4):
                url = article.video_url if i == 1 else getattr(article, f'video_url_{i}', None)
                if url:
                    videos.append(url)

            return {
                'article_id': article.article_id,
                'title': article.title,
                'content': content,
                'location': article.location,
                'images': images,
                'videos': videos,
                'created_at': article.created_at,
                'user_id': article.user_id
            }

        except Exception as e:
            print(f"提取文章内容失败: {e}")
            return None

    def _generate_enhanced_script(self, content_data: Dict, style: str, duration: str, focus_elements: str) -> Dict:
        """生成增强版动画脚本"""
        try:
            # 构建AI提示词
            prompt = f"""
            请为以下旅游日记生成一个详细的{style}风格动画脚本：

            标题: {content_data['title']}
            地点: {content_data['location']}
            重点元素: {focus_elements}
            动画时长: {duration}
            内容摘要: {content_data['content'][:300]}...

            请生成包含以下场景的动画脚本：
            1. 开场标题场景
            2. 地点介绍场景
            3. 3-7个主要内容场景（根据时长调整）
            4. 结尾场景

            每个场景需要包含：
            - scene_number: 场景编号
            - duration: 持续时间（秒）
            - description: 场景描述
            - camera_angle: 镜头角度
            - music_suggestion: 配乐建议
            - image_prompt: 用于AI生成图片的提示词
            - transition: 转场效果

            请以JSON格式返回，只返回JSON，不要其他文字。
            """

            try:
                ai_response = self.ai_service.generate_text(prompt, max_tokens=2000)
                # 尝试解析JSON
                if ai_response.startswith('{'):
                    script = json.loads(ai_response)
                else:
                    # 如果不是JSON格式，提取JSON部分
                    start = ai_response.find('{')
                    end = ai_response.rfind('}') + 1
                    if start != -1 and end != -1:
                        script = json.loads(ai_response[start:end])
                    else:
                        raise ValueError("无法解析AI响应")

                return script

            except (json.JSONDecodeError, ValueError) as e:
                print(f"AI脚本解析失败: {e}")
                return self._generate_default_enhanced_script(content_data, style, duration, focus_elements)

        except Exception as e:
            print(f"生成动画脚本失败: {e}")
            return self._generate_default_enhanced_script(content_data, style, duration, focus_elements)

    def _generate_default_enhanced_script(self, content_data: Dict, style: str, duration: str, focus_elements: str) -> Dict:
        """生成默认增强版脚本"""
        scene_count = 3 if duration == '短片' else (5 if duration == '中等' else 7)
        scene_duration = 15 if duration == '短片' else (30 if duration == '中等' else 45)

        scenes = []

        # 开场场景
        scenes.append({
            'scene_number': 1,
            'duration': f'{scene_duration//3}-{scene_duration//2}秒',
            'description': f'开场标题：{content_data["title"]}，展现{style}风格的标题动画',
            'camera_angle': '正面特写',
            'music_suggestion': self._get_music_by_style(style),
            'image_prompt': f'{style}风格的旅游标题背景，{content_data["location"]}风景',
            'transition': '淡入'
        })

        # 主要内容场景
        for i in range(2, scene_count):
            scenes.append({
                'scene_number': i,
                'duration': f'{scene_duration-10}-{scene_duration}秒',
                'description': f'场景{i}：展现{content_data["location"]}的{focus_elements}，营造{style}的氛围',
                'camera_angle': '广角镜头' if i % 2 == 0 else '特写镜头',
                'music_suggestion': self._get_music_by_style(style),
                'image_prompt': f'{content_data["location"]}的{focus_elements}，{style}风格，高质量摄影',
                'transition': '交叉淡化' if i > 2 else '滑动'
            })

        # 结尾场景
        scenes.append({
            'scene_number': scene_count,
            'duration': f'{scene_duration//2}-{scene_duration//1.5}秒',
            'description': f'结尾：回顾{content_data["title"]}的美好时光',
            'camera_angle': '远景',
            'music_suggestion': self._get_music_by_style(style),
            'image_prompt': f'{content_data["location"]}全景，{style}风格，温馨结尾',
            'transition': '淡出'
        })

        return {
            'scenes': scenes,
            'style': style,
            'total_scenes': len(scenes)
        }

    def _generate_scene_images(self, script: Dict, content_data: Dict) -> List[Dict]:
        """为每个场景生成AI图片"""
        generated_images = []

        try:
            scenes = script.get('scenes', [])
            print(f"开始为{len(scenes)}个场景生成图片...")

            for scene in scenes[:4]:  # 限制生成数量，避免API调用过多
                image_prompt = scene.get('image_prompt', f"{content_data['location']}风景")

                print(f"生成场景{scene['scene_number']}的图片: {image_prompt}")

                # 调用火山引擎文生图API
                result = self.aigc_service.generate_image(
                    prompt=image_prompt,
                    style="realistic",
                    size="1024x1024",
                    num_images=1
                )

                if result['success']:
                    for img_data in result['images']:
                        generated_images.append({
                            'scene_number': scene['scene_number'],
                            'url': img_data.get('url'),
                            'prompt': image_prompt,
                            'description': scene['description'],
                            'type': 'ai_generated'
                        })
                else:
                    # 使用降级图片
                    for img_data in result.get('fallback_images', []):
                        generated_images.append({
                            'scene_number': scene['scene_number'],
                            'url': img_data['url'],
                            'prompt': image_prompt,
                            'description': img_data['description'],
                            'type': 'fallback'
                        })

                # 避免API调用过于频繁
                import time
                time.sleep(1)

            print(f"图片生成完成，共生成{len(generated_images)}张图片")

        except Exception as e:
            print(f"生成场景图片失败: {e}")

        return generated_images

    def _generate_scene_videos(self, script: Dict, content_data: Dict, duration: str) -> List[Dict]:
        """为关键场景生成AI视频"""
        generated_videos = []

        try:
            scenes = script.get('scenes', [])
            # 只为1-2个关键场景生成视频（视频生成成本较高）
            key_scenes = scenes[1:3] if len(scenes) > 2 else scenes[:1]

            print(f"开始为{len(key_scenes)}个关键场景生成视频...")

            for scene in key_scenes:
                video_prompt = f"{content_data['location']}的{scene['description']}，电影级质量"
                video_duration = 5 if duration == '短片' else 8

                print(f"生成场景{scene['scene_number']}的视频: {video_prompt}")

                # 调用火山引擎文生视频API
                result = self.aigc_service.generate_video(
                    prompt=video_prompt,
                    duration=video_duration,
                    style="realistic"
                )

                if result['success']:
                    generated_videos.append({
                        'scene_number': scene['scene_number'],
                        'task_id': result['task_id'],
                        'status': result['status'],
                        'prompt': video_prompt,
                        'description': scene['description'],
                        'estimated_time': result.get('estimated_time', 60),
                        'type': 'ai_generated'
                    })
                else:
                    # 使用降级视频
                    fallback_video = result.get('fallback_video', {})
                    generated_videos.append({
                        'scene_number': scene['scene_number'],
                        'url': fallback_video.get('url'),
                        'prompt': video_prompt,
                        'description': fallback_video.get('description'),
                        'type': 'fallback'
                    })

            print(f"视频生成任务提交完成，共{len(generated_videos)}个视频")

        except Exception as e:
            print(f"生成场景视频失败: {e}")

        return generated_videos

    def _get_music_by_style(self, style: str) -> str:
        """根据风格获取配乐建议"""
        music_map = {
            '温馨': '轻柔的钢琴曲或吉他弹唱',
            '活泼': '欢快的民谣或流行音乐',
            '文艺': '古典音乐或独立音乐',
            '大气': '大气的交响乐或电影配乐'
        }
        return music_map.get(style, '轻松的背景音乐')

    def _get_duration_text(self, duration: str) -> str:
        """获取时长文本"""
        duration_map = {
            '短片': '60秒',
            '中等': '3分钟',
            '长片': '5分钟'
        }
        return duration_map.get(duration, '3分钟')

    def _generate_expanded_content(self, content_data: Dict, style: str, focus_elements: str) -> str:
        """生成扩展内容描述"""
        return f"""这是一个关于{content_data['title']}的{style}风格旅游动画。

动画以{content_data['location']}为背景，通过精心设计的场景展现了这次旅行的独特魅力。
重点突出了{focus_elements}等元素，为观众带来身临其境的旅游体验。

整个动画采用{style}的视觉风格，配合恰当的音乐和转场效果，
将旅行中的美好时光以动态的方式呈现，让观众感受到旅行的乐趣和意义。

通过AI技术生成的图片和视频片段，增强了动画的视觉效果，
使得每一个场景都充满了生动的细节和情感色彩。"""

    def _generate_video_caption(self, content_data: Dict, style: str) -> str:
        """生成视频文案"""
        return f"""🎬 {content_data['title']} | {style}风格旅游动画

📍 {content_data['location']}
✨ 一段精彩的视觉盛宴，带你领略旅途中的美好时光

🎨 采用AI技术生成的精美图片和视频
🎵 配合{self._get_music_by_style(style)}
🎭 {style}风格的视觉呈现

#旅游 #AI动画 #{content_data['location']} #{style}风格"""

    def _generate_media_usage_guide(self) -> List[Dict]:
        """生成媒体使用指南"""
        return [
            {
                'media_type': 'AI生成图片',
                'usage_scene': '场景背景和转场',
                'effect': '提供高质量的视觉背景，增强场景氛围'
            },
            {
                'media_type': 'AI生成视频',
                'usage_scene': '关键场景展示',
                'effect': '动态展现重要时刻，增强观看体验'
            },
            {
                'media_type': '原始照片',
                'usage_scene': '真实记录展示',
                'effect': '保持旅行的真实性和纪念价值'
            },
            {
                'media_type': '背景音乐',
                'usage_scene': '全程配乐',
                'effect': '营造情感氛围，增强沉浸感'
            }
        ]

    def _generate_error_response(self, error_message: str) -> Dict[str, Any]:
        """生成错误响应"""
        return {
            'success': False,
            'error': error_message,
            'animation_script': {
                'total_duration': '3分钟',
                'style': '温馨',
                'scenes': []
            },
            'generated_media': {
                'images': [],
                'videos': []
            },
            'expanded_content': '动画生成失败，请稍后重试。',
            'video_caption': '🎬 旅游动画生成失败',
            'media_usage': []
        }

    def query_video_generation_status(self, task_ids: List[str]) -> Dict[str, Any]:
        """查询视频生成状态"""
        try:
            results = {}
            for task_id in task_ids:
                result = self.aigc_service.query_video_status(task_id)
                results[task_id] = result

            return {
                'success': True,
                'results': results
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'查询视频状态失败: {str(e)}'
            }
