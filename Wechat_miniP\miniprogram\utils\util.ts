export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

// 导入API配置
import { getApiBaseUrl } from '../config/api'

// API请求工具函数
export const request = (options: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: any
}) => {
  return new Promise((resolve, reject) => {
    const fullUrl = `${getApiBaseUrl()}${options.url}`
    console.log('发起请求:', {
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data
    })

    wx.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        console.log('请求成功:', {
          statusCode: res.statusCode,
          data: res.data
        })
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          console.error('请求失败:', res.statusCode, res.data)
          reject(new Error(`请求失败: ${res.statusCode} - ${JSON.stringify(res.data)}`))
        }
      },
      fail: (err) => {
        console.error('网络请求失败:', err)
        reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`))
      }
    })
  })
}

// 获取所有地点（与网页端API一致）
export const getVertices = () => {
  return request({
    url: '/path/vertices',
    method: 'GET'
  })
}

// 路径规划API（与网页端API一致）
export const planRoute = (data: {
  start_id: number
  end_id?: number
  destinations?: number[]
  strategy: number
  algorithm?: string
}) => {
  return request({
    url: '/path/plan',
    method: 'POST',
    data
  })
}

// 格式化距离
export const formatDistance = (distance: number) => {
  if (distance < 1000) {
    return `${Math.round(distance)} 米`
  } else {
    return `${(distance / 1000).toFixed(2)} 公里`
  }
}

// 格式化时间
export const formatDuration = (minutes: number) => {
  if (minutes < 60) {
    return `${Math.round(minutes)} 分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return `${hours} 小时 ${mins} 分钟`
  }
}
