"""
附近景点查询服务
使用最短路径算法而不是欧几里得距离来查找附近景点
"""

from typing import List, Dict, Any, Optional
from models.path_planning import Vertex, Edge
from services.path_planning_service import PathPlanningService
import heapq


class NearbySpotService:
    """
    附近景点查询服务类
    """
    
    def __init__(self):
        self.path_service = PathPlanningService()
    
    def find_nearby_spots_by_name(self, location_name: str, max_distance: int = 500, 
                                 spot_type: Optional[int] = None, limit: int = 15, 
                                 strategy: int = 0) -> Dict[str, Any]:
        """
        通过地点名称查找附近景点
        
        Args:
            location_name: 起始地点名称
            max_distance: 最大距离限制
            spot_type: 景点类型过滤
            limit: 返回结果数量限制
            strategy: 路径规划策略
            
        Returns:
            包含附近景点信息的字典
        """
        # 查找起始顶点
        start_vertex = Vertex.query.filter(Vertex.label.ilike(f'%{location_name}%')).first()
        if not start_vertex:
            return {'error': f'Location "{location_name}" not found'}
        
        return self.find_nearby_spots_by_vertex_id(
            start_vertex.vertex_id, max_distance, spot_type, limit, strategy
        )
    
    def find_nearby_spots_by_vertex_id(self, start_vertex_id: int, max_distance: int = 500,
                                      spot_type: Optional[int] = None, limit: int = 15,
                                      strategy: int = 0) -> Dict[str, Any]:
        """
        通过顶点ID查找附近景点
        
        Args:
            start_vertex_id: 起始顶点ID
            max_distance: 最大距离限制
            spot_type: 景点类型过滤
            limit: 返回结果数量限制
            strategy: 路径规划策略
            
        Returns:
            包含附近景点信息的字典
        """
        # 获取起始顶点
        start_vertex = Vertex.query.get(start_vertex_id)
        if not start_vertex:
            return {'error': f'Vertex with ID {start_vertex_id} not found'}
        
        # 查询候选景点
        candidate_spots = self._get_candidate_spots(start_vertex_id, spot_type)
        
        if not candidate_spots:
            return {
                'start_location': self._vertex_to_dict(start_vertex),
                'nearby_spots': [],
                'total_found': 0,
                'parameters': {
                    'max_distance': max_distance,
                    'strategy': strategy,
                    'type_filter': spot_type,
                    'limit': limit
                }
            }
        
        # 使用优化的批量最短路径计算
        nearby_spots = self._calculate_distances_batch(
            start_vertex_id, candidate_spots, max_distance, strategy, limit
        )
        
        return {
            'start_location': self._vertex_to_dict(start_vertex),
            'nearby_spots': nearby_spots,
            'total_found': len(nearby_spots),
            'parameters': {
                'max_distance': max_distance,
                'strategy': strategy,
                'type_filter': spot_type,
                'limit': limit
            }
        }
    
    def _get_candidate_spots(self, start_vertex_id: int, spot_type: Optional[int] = None) -> List[Vertex]:
        """
        获取候选景点列表
        """
        query = Vertex.query
        
        # 景点类型过滤
        if spot_type is not None:
            query = query.filter(Vertex.type == spot_type)
        else:
            # 默认查询景点类型（排除学校、门、路口等基础设施）
            query = query.filter(Vertex.type.in_([2, 3, 4, 5, 6, 7, 8, 9, 10, 11]))
        
        # 排除起始顶点本身
        query = query.filter(Vertex.vertex_id != start_vertex_id)
        
        return query.all()
    
    def _calculate_distances_batch(self, start_vertex_id: int, candidate_spots: List[Vertex],
                                  max_distance: int, strategy: int, limit: int) -> List[Dict[str, Any]]:
        """
        批量计算到候选景点的距离
        """
        nearby_spots = []
        
        for spot in candidate_spots:
            try:
                # 计算最短路径
                result = self.path_service.get_shortest_path(start_vertex_id, spot.vertex_id, strategy)
                
                if 'error' not in result and 'total_distance' in result:
                    path_distance = result['total_distance']
                    
                    # 检查是否在距离限制内
                    if path_distance <= max_distance:
                        nearby_spots.append({
                            'vertex_id': spot.vertex_id,
                            'name': spot.label,
                            'type': spot.type,
                            'coordinates': {'x': spot.x, 'y': spot.y},
                            'path_distance': path_distance,
                            'path_length': len(result.get('path', [])),
                            'strategy_used': strategy
                        })
                
            except Exception as e:
                print(f"计算到景点 {spot.label} 的路径时出错: {str(e)}")
                continue
        
        # 按距离排序并限制结果数量
        nearby_spots.sort(key=lambda x: x['path_distance'])
        return nearby_spots[:limit]
    
    def _vertex_to_dict(self, vertex: Vertex) -> Dict[str, Any]:
        """
        将顶点对象转换为字典
        """
        return {
            'vertex_id': vertex.vertex_id,
            'name': vertex.label,
            'coordinates': {'x': vertex.x, 'y': vertex.y},
            'type': vertex.type
        }
    
    def get_location_suggestions(self, query_text: str, limit: int = 10, 
                               type_filter: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取地点名称建议（用于自动补全）
        
        Args:
            query_text: 查询文本
            limit: 返回结果数量限制
            type_filter: 类型过滤
            
        Returns:
            地点建议列表
        """
        if not query_text.strip():
            return []
        
        # 构建查询
        query = Vertex.query
        
        # 类型过滤
        if type_filter is not None:
            query = query.filter(Vertex.type == type_filter)
        
        # 名称模糊匹配
        query = query.filter(Vertex.label.ilike(f'%{query_text}%'))
        
        # 按名称排序并限制结果数量
        vertices = query.order_by(Vertex.label).limit(limit).all()
        
        # 构建建议列表
        suggestions = []
        for vertex in vertices:
            suggestions.append({
                'vertex_id': vertex.vertex_id,
                'label': vertex.label,
                'type': vertex.type,
                'coordinates': {'x': vertex.x, 'y': vertex.y}
            })
        
        return suggestions
    
    def find_spots_by_multiple_criteria(self, start_vertex_id: int, criteria: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据多个条件查找景点
        
        Args:
            start_vertex_id: 起始顶点ID
            criteria: 查询条件字典
                - max_distance: 最大距离
                - spot_types: 景点类型列表
                - name_keywords: 名称关键词列表
                - limit: 结果数量限制
                - strategy: 路径策略
                
        Returns:
            查询结果
        """
        max_distance = criteria.get('max_distance', 500)
        spot_types = criteria.get('spot_types', [])
        name_keywords = criteria.get('name_keywords', [])
        limit = criteria.get('limit', 15)
        strategy = criteria.get('strategy', 0)
        
        # 获取起始顶点
        start_vertex = Vertex.query.get(start_vertex_id)
        if not start_vertex:
            return {'error': f'Vertex with ID {start_vertex_id} not found'}
        
        # 构建查询
        query = Vertex.query.filter(Vertex.vertex_id != start_vertex_id)
        
        # 类型过滤
        if spot_types:
            query = query.filter(Vertex.type.in_(spot_types))
        else:
            # 默认景点类型
            query = query.filter(Vertex.type.in_([2, 3, 4, 5, 6, 7, 8, 9, 10, 11]))
        
        # 名称关键词过滤
        if name_keywords:
            from sqlalchemy import or_
            conditions = [Vertex.label.ilike(f'%{keyword}%') for keyword in name_keywords]
            query = query.filter(or_(*conditions))
        
        candidate_spots = query.all()
        
        # 计算距离
        nearby_spots = self._calculate_distances_batch(
            start_vertex_id, candidate_spots, max_distance, strategy, limit
        )
        
        return {
            'start_location': self._vertex_to_dict(start_vertex),
            'nearby_spots': nearby_spots,
            'total_found': len(nearby_spots),
            'criteria': criteria
        }
