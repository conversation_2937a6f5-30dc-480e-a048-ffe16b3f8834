from utils.database import db
import datetime
from utils.time_helper import get_china_now, format_time_for_api, ensure_timezone

def get_utc_now():
    """Get current UTC time in a timezone-aware format"""
    return datetime.datetime.now(datetime.timezone.utc)

class Article(db.Model):
    """
    Article model - represents a travel article
    Aligned with Java version's Article model
    """
    __tablename__ = 'articles'
    __table_args__ = {'extend_existing': True}

    article_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.LargeBinary, nullable=False)  # Compressed content
    huffman_codes = db.Column(db.Text, nullable=True)  # Huffman codes for decompression
    location = db.Column(db.String(255), nullable=True)
    tags = db.Column(db.Text, nullable=True)  # 存储JSON格式的标签数组
    popularity = db.Column(db.Integer, default=0, nullable=True)
    evaluation = db.Column(db.Float, default=0, nullable=True)

    # 图片和视频字段
    image_url = db.Column(db.String(255), nullable=True)  # 图片URL 1
    video_url = db.Column(db.String(255), nullable=True)  # 视频URL 1
    # 新增多图片支持
    image_url_2 = db.Column(db.String(255), nullable=True)  # 图片URL 2
    image_url_3 = db.Column(db.String(255), nullable=True)  # 图片URL 3
    image_url_4 = db.Column(db.String(255), nullable=True)  # 图片URL 4
    image_url_5 = db.Column(db.String(255), nullable=True)  # 图片URL 5
    image_url_6 = db.Column(db.String(255), nullable=True)  # 图片URL 6
    # 新增多视频支持
    video_url_2 = db.Column(db.String(255), nullable=True)  # 视频URL 2
    video_url_3 = db.Column(db.String(255), nullable=True)  # 视频URL 3
    created_at = db.Column(db.DateTime, default=get_utc_now)
    updated_at = db.Column(db.DateTime, default=get_utc_now, onupdate=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='articles')

    def to_dict(self, include_content=False):
        """Convert article to dictionary"""
        result = {
            'article_id': self.article_id,
            'user_id': self.user_id,
            'title': self.title,
            'location': self.location,
            'popularity': self.popularity,
            'evaluation': self.evaluation,
            'image_url': self.image_url,
            'video_url': self.video_url,
            # 新增多图片支持
            'image_url_2': self.image_url_2,
            'image_url_3': self.image_url_3,
            'image_url_4': self.image_url_4,
            'image_url_5': self.image_url_5,
            'image_url_6': self.image_url_6,
            # 新增多视频支持
            'video_url_2': self.video_url_2,
            'video_url_3': self.video_url_3,
            'created_at': format_time_for_api(ensure_timezone(self.created_at)),
            'updated_at': format_time_for_api(ensure_timezone(self.updated_at))
        }

        if include_content:
            result['content'] = self.content
            result['huffman_codes'] = self.huffman_codes

        return result

class ArticleScore(db.Model):
    """
    Article score model - represents a user's rating of an article
    """
    __tablename__ = 'article_scores'
    __table_args__ = {'extend_existing': True}

    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('articles.article_id'), primary_key=True)
    score = db.Column(db.Integer, nullable=False, default=0)  # Java版本使用Integer而不是Float

    # 这些字段在数据库中可能不存在，所以我们不自动创建它们
    # 但在代码中仍然可以引用它们
    # created_at = db.Column(db.DateTime, default=get_utc_now)
    # updated_at = db.Column(db.DateTime, default=get_utc_now, onupdate=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='article_scores')
    article = db.relationship('Article', backref='scores')

    def to_dict(self):
        """Convert article score to dictionary"""
        result = {
            'user_id': self.user_id,
            'article_id': self.article_id,
            'score': self.score
        }

        # 如果存在created_at字段，添加到结果中
        if hasattr(self, 'created_at') and self.created_at:
            result['created_at'] = self.created_at.isoformat()
        else:
            result['created_at'] = None

        # 如果存在updated_at字段，添加到结果中
        if hasattr(self, 'updated_at') and self.updated_at:
            result['updated_at'] = self.updated_at.isoformat()
        else:
            result['updated_at'] = None

        return result


class ArticleFavorite(db.Model):
    """
    Article favorite model - represents a user's favorite article
    """
    __tablename__ = 'article_favorites'
    __table_args__ = {'extend_existing': True}

    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('articles.article_id'), primary_key=True)
    created_at = db.Column(db.DateTime, default=get_utc_now)

    # Define relationships
    user = db.relationship('User', backref='article_favorites')
    article = db.relationship('Article', backref='favorites')

    def to_dict(self):
        """Convert article favorite to dictionary"""
        return {
            'user_id': self.user_id,
            'article_id': self.article_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

