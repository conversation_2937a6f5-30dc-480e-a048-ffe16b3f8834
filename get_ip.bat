@echo off
echo ========================================
echo 获取本机IP地址
echo ========================================
echo.

echo 正在获取IP地址...
echo.

for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    set ip=%%a
    set ip=!ip: =!
    if not "!ip!"=="" (
        echo 找到IP地址: !ip!
        echo.
        echo 请将以下地址复制到小程序配置文件中：
        echo http://!ip!:5000/api
        echo.
    )
)

echo ========================================
echo 配置步骤：
echo 1. 复制上面的地址
echo 2. 打开文件：Wechat_miniP/miniprogram/config/api.ts
echo 3. 将 API_BASE_URL 替换为上面的地址
echo ========================================
echo.

pause
