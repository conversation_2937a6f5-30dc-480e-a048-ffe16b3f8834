<template>
  <el-dialog
    v-model="dialogVisible"
    :title="activeTab === 'login' ? '登录' : '注册'"
    width="400px"
    :close-on-click-modal="false"
  >
    <el-tabs v-model="activeTab" class="demo-tabs">
      <el-tab-pane label="登录" name="login">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          label-width="80px"
          class="login-form"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleLogin"
              style="width: 100%"
              :disabled="!isLoginFormValid"
            >登录</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="注册" name="register">
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          label-width="80px"
          class="register-form"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="registerForm.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleRegister"
              style="width: 100%"
              :disabled="!isRegisterFormValid"
            >注册</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
import { ref, computed, defineEmits, defineExpose } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/services/api'

const router = useRouter()
const dialogVisible = ref(false)
const activeTab = ref('login')

// 定义事件
const emit = defineEmits(['login-success'])

// 表单引用
const loginFormRef = ref(null)
const registerFormRef = ref(null)

// 登录表单数据
const loginForm = ref({
  username: '',
  password: ''
})

// 注册表单数据
const registerForm = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 登录表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度不能小于3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度不能小于3个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性：登录表单是否有效
const isLoginFormValid = computed(() => {
  return loginForm.value.username && loginForm.value.username.length >= 3 &&
         loginForm.value.password && loginForm.value.password.length >= 6
})

// 计算属性：注册表单是否有效
const isRegisterFormValid = computed(() => {
  return (
    registerForm.value.username &&
    registerForm.value.username.length >= 3 &&
    registerForm.value.email &&
    registerForm.value.password &&
    registerForm.value.password.length >= 6 &&
    registerForm.value.confirmPassword === registerForm.value.password
  )
})

// 处理登录
const handleLogin = async () => {
  if (!isLoginFormValid.value) return

  try {
    console.log('开始登录请求');
    const response = await authAPI.login({
      username: loginForm.value.username,
      password: loginForm.value.password
    });

    console.log('登录响应:', response);
    const { token, user } = response.data;

    // 保存token、用户ID和用户类型
    localStorage.setItem('token', token);
    localStorage.setItem('authToken', token); // 同时保存为authToken，确保兼容性
    localStorage.setItem('userType', 'user'); // 设置用户类型为注册用户

    // 保存用户ID
    if (user && user.id) {
      localStorage.setItem('userId', user.id);
      console.log('已保存用户ID到localStorage:', user.id);
    } else if (user && user.user_id) {
      localStorage.setItem('userId', user.user_id);
      console.log('已保存用户ID(user_id)到localStorage:', user.user_id);
    }

    // 保存完整的用户信息
    localStorage.setItem('currentUser', JSON.stringify(user));

    ElMessage.success('登录成功');
    dialogVisible.value = false;

    // 发送登录成功事件
    emit('login-success', user);

    // 打印调试信息
    console.log('登录成功，用户信息和状态:', {
      user,
      token,
      userType: localStorage.getItem('userType'),
      authToken: localStorage.getItem('authToken')
    });

    // 跳转到首页
    router.push('/home');
  } catch (error) {
    console.error('登录错误详情:', error);
    ElMessage.error(error.response?.data?.error || '登录失败，请检查用户名和密码');
  }
}

// 处理注册
const handleRegister = async () => {
  if (!isRegisterFormValid.value) return

  try {
    await authAPI.register({
      username: registerForm.value.username,
      email: registerForm.value.email,
      password: registerForm.value.password,
      confirmPassword: registerForm.value.confirmPassword
    });

    ElMessage.success('注册成功，请登录');
    // 切换到登录标签
    activeTab.value = 'login';
    // 预填充登录表单
    loginForm.value.username = registerForm.value.username;
    // 清空注册表单
    registerForm.value = {
      username: '',
      email: '',
      password: '',
      confirmPassword: ''
    };
  } catch (error) {
    ElMessage.error(error.response?.data?.error || '注册失败');
    console.error('注册错误:', error);
  }
}

// 对外暴露的方法：显示对话框
const show = () => {
  dialogVisible.value = true
}

// 对外暴露的方法：隐藏对话框
const hide = () => {
  dialogVisible.value = false
}

// 导出方法供父组件调用
defineExpose({
  show,
  hide,
  activeTab
})
</script>

<style scoped>
.login-form,
.register-form {
  margin-top: 20px;
}

:deep(.el-tabs__nav) {
  width: 100%;
  display: flex;
}

:deep(.el-tabs__item) {
  flex: 1;
  text-align: center;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
