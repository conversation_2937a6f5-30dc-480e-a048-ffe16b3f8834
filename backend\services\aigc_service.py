"""
AIGC服务

该模块实现了基于DeepSeek的旅游动画生成功能
"""

import os
import requests
import json
import base64
from typing import Optional
import uuid
from datetime import datetime

class AIGCService:
    """AIGC服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.api_key = os.environ.get('DEEPSEEK_API_KEY', '')
        self.api_url = "https://api.deepseek.com/v1/images/generations"
        
    def generate_travel_animation(self, image_url: str, location_name: str = None) -> Optional[str]:
        """
        根据景点照片生成旅游动画
        
        Args:
            image_url: 景点照片URL
            location_name: 位置名称（可选）
            
        Returns:
            生成的动画URL，失败则返回None
        """
        try:
            # 下载图片并转换为base64
            response = requests.get(image_url)
            if response.status_code != 200:
                print(f"Failed to download image: {response.status_code}")
                return None
                
            image_data = response.content
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            # 构建提示词
            if location_name:
                prompt = f"Create a beautiful travel animation based on this image of {location_name}. Make it cinematic, vibrant, and appealing for travel enthusiasts."
            else:
                prompt = "Create a beautiful travel animation based on this image. Make it cinematic, vibrant, and appealing for travel enthusiasts."
            
            # 调用DeepSeek API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            payload = {
                "prompt": prompt,
                "image": base64_image,
                "n": 1,
                "size": "1024x1024",
                "style": "vivid"
            }
            
            response = requests.post(self.api_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code != 200:
                print(f"API request failed: {response.status_code}, {response.text}")
                return None
                
            result = response.json()
            
            # 保存生成的图片
            if 'data' in result and len(result['data']) > 0 and 'url' in result['data'][0]:
                animation_url = result['data'][0]['url']
                
                # 下载生成的动画
                animation_response = requests.get(animation_url)
                if animation_response.status_code != 200:
                    print(f"Failed to download animation: {animation_response.status_code}")
                    return animation_url
                
                # 保存到本地
                filename = f"travel_animation_{uuid.uuid4()}.mp4"
                save_path = os.path.join('static', 'animations', filename)
                
                # 确保目录存在
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                
                with open(save_path, 'wb') as f:
                    f.write(animation_response.content)
                
                # 返回本地URL
                return f"/static/animations/{filename}"
            
            return None
            
        except Exception as e:
            print(f"Error generating travel animation: {e}")
            return None
    
    def generate_travel_story(self, image_url: str, location_name: str = None) -> Optional[str]:
        """
        根据景点照片生成旅游故事
        
        Args:
            image_url: 景点照片URL
            location_name: 位置名称（可选）
            
        Returns:
            生成的故事文本，失败则返回None
        """
        try:
            # 下载图片并转换为base64
            response = requests.get(image_url)
            if response.status_code != 200:
                print(f"Failed to download image: {response.status_code}")
                return None
                
            image_data = response.content
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            # 构建提示词
            if location_name:
                prompt = f"Write a short, engaging travel story about visiting {location_name} based on this image. Include sensory details and personal experiences."
            else:
                prompt = "Write a short, engaging travel story about visiting the place shown in this image. Include sensory details and personal experiences."
            
            # 调用DeepSeek API (文本生成)
            text_api_url = "https://api.deepseek.com/v1/chat/completions"
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a creative travel writer who creates engaging, vivid stories about travel destinations."
                    },
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ],
                "max_tokens": 500
            }
            
            response = requests.post(text_api_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code != 200:
                print(f"API request failed: {response.status_code}, {response.text}")
                return None
                
            result = response.json()
            
            # 提取生成的文本
            if 'choices' in result and len(result['choices']) > 0 and 'message' in result['choices'][0]:
                story = result['choices'][0]['message'].get('content', '')
                return story
            
            return None
            
        except Exception as e:
            print(f"Error generating travel story: {e}")
            return None
