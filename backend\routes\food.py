"""
美食推荐路由模块

该模块提供美食推荐的API接口，包括：
1. 获取推荐餐馆列表
2. 获取热门餐馆
3. 获取高评分餐馆
4. 获取餐馆详情
5. 搜索餐馆
"""


from flask import Blueprint, request, current_app
from models.restaurant import Restaurant
from models.restaurant_favorite import RestaurantFavorite
from utils.database import db
from utils.response import success, error
from utils.restaurant_sorter import RestaurantSorter
from utils.restaurant_finder import RestaurantFinder
from utils.restaurant_recommender import RestaurantRecommender
from sqlalchemy import not_

food_bp = Blueprint('food', __name__)

@food_bp.route('/recommendations', methods=['GET'])
def get_food_recommendations():
    """
    获取推荐餐馆列表
    支持参数：
    - limit: 返回结果数量限制，默认10
    - cuisine_type: 菜系类型
    - sort_by: 排序方式，支持 'popularity'(默认), 'rating', 'distance', 'price'
    - order: 排序顺序，支持 'desc'(默认), 'asc'
    - location_x: 位置x坐标，用于按距离排序
    - location_y: 位置y坐标，用于按距离排序
    - user_id: 用户ID，用于个性化推荐
    """
    try:
        # 获取请求参数
        limit = request.args.get('limit', 10, type=int)
        cuisine_type = request.args.get('cuisine_type')
        sort_by = request.args.get('sort_by', 'popularity')
        order = request.args.get('order', 'desc')
        location_x = request.args.get('location_x', type=float)
        location_y = request.args.get('location_y', type=float)

        # 获取所有餐馆
        restaurants = Restaurant.query.all()

        # 应用菜系过滤
        if cuisine_type:
            restaurants = RestaurantFinder.filter_by_cuisine_type(restaurants, [cuisine_type])

        # 确定排序方向
        reverse = (order.lower() == 'desc')

        # 应用排序
        if sort_by == 'rating':
            # 使用高效排序算法，只排序前limit个元素
            key_func = lambda x: x.evaluation
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        elif sort_by == 'distance' and location_x is not None and location_y is not None:
            # 按距离排序
            restaurants = RestaurantSorter.top_k_by_distance(restaurants, limit, location_x, location_y)
            # 如果是降序排序，需要反转结果
            if reverse:
                restaurants.reverse()
        elif sort_by == 'price':
            # 按价格排序
            key_func = lambda x: x.average_price_perperson if x.average_price_perperson is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        else:  # 默认按人气排序
            key_func = lambda x: x.popularity if x.popularity is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)

        # 限制结果数量
        restaurants = restaurants[:limit]

        # 转换为字典列表
        result = [restaurant.to_dict() for restaurant in restaurants]

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取推荐餐馆出错: {str(e)}")
        return error(str(e))

@food_bp.route('/popular', methods=['GET'])
def get_popular_food():
    """
    获取热门餐馆
    支持参数：
    - limit: 返回结果数量限制，默认10
    - cuisine_type: 菜系类型
    - order: 排序顺序，支持 'desc'(默认), 'asc'
    - sort_by: 排序方式，支持 'popularity'(默认), 'rating', 'price'
    """
    try:
        # 获取请求参数
        limit = request.args.get('limit', 10, type=int)
        cuisine_type = request.args.get('cuisine_type')
        order = request.args.get('order', 'desc')
        sort_by = request.args.get('sort_by', 'popularity')

        # 获取所有餐馆
        restaurants = Restaurant.query.all()

        # 应用菜系过滤
        if cuisine_type:
            restaurants = RestaurantFinder.filter_by_cuisine_type(restaurants, [cuisine_type])

        # 确定排序方向
        reverse = (order.lower() == 'desc')

        # 应用排序
        if sort_by == 'rating':
            # 按评分排序
            key_func = lambda x: x.evaluation if x.evaluation is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        elif sort_by == 'price':
            # 按价格排序
            key_func = lambda x: x.average_price_perperson if x.average_price_perperson is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        else:  # 默认按人气排序
            key_func = lambda x: x.popularity if x.popularity is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)

        # 限制结果数量
        restaurants = restaurants[:limit]

        # 转换为字典列表
        result = [restaurant.to_dict() for restaurant in restaurants]

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取热门餐馆出错: {str(e)}")
        return error(str(e))

@food_bp.route('/special', methods=['GET'])
def get_special_food():
    """
    获取特色餐馆
    支持参数：
    - limit: 返回结果数量限制，默认10
    - cuisine_type: 菜系类型
    - min_rating: 最低评分，默认4.0
    - order: 排序顺序，支持 'desc'(默认), 'asc'
    - sort_by: 排序方式，支持 'rating'(默认), 'popularity', 'price'
    """
    try:
        # 获取请求参数
        limit = request.args.get('limit', 10, type=int)
        cuisine_type = request.args.get('cuisine_type')
        min_rating = request.args.get('min_rating', 4.0, type=float)
        order = request.args.get('order', 'desc')
        sort_by = request.args.get('sort_by', 'rating')

        # 获取所有餐馆
        restaurants = Restaurant.query.all()

        # 应用菜系过滤
        if cuisine_type:
            restaurants = RestaurantFinder.filter_by_cuisine_type(restaurants, [cuisine_type])

        # 过滤评分低于min_rating的餐馆
        restaurants = [r for r in restaurants if r.evaluation is not None and r.evaluation >= min_rating]

        # 确定排序方向
        reverse = (order.lower() == 'desc')

        # 应用排序
        if sort_by == 'popularity':
            # 按人气排序
            key_func = lambda x: x.popularity if x.popularity is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        elif sort_by == 'price':
            # 按价格排序
            key_func = lambda x: x.average_price_perperson if x.average_price_perperson is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        else:  # 默认按评分排序
            key_func = lambda x: x.evaluation if x.evaluation is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)

        # 限制结果数量
        restaurants = restaurants[:limit]

        # 转换为字典列表
        result = [restaurant.to_dict() for restaurant in restaurants]

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取特色餐馆出错: {str(e)}")
        return error(str(e))

@food_bp.route('/<int:restaurant_id>', methods=['GET'])
def get_food_detail(restaurant_id):
    """
    获取餐馆详情
    """
    try:
        # 查询餐馆
        restaurant = Restaurant.query.get(restaurant_id)

        if not restaurant:
            return error("餐馆不存在")

        # 增加浏览次数
        restaurant.number_of_view = (restaurant.number_of_view or 0) + 1
        db.session.commit()

        # 转换为字典
        result = restaurant.to_dict()

        # 确保菜品信息存在
        if 'dishes' not in result or not result['dishes']:
            result['dishes'] = []

        # 为每个菜品添加描述
        for i, dish in enumerate(result['dishes']):
            if dish:
                if i == 0:
                    dish['description'] = f"精选食材制作的{dish['name']}，口感鲜美，色香味俱全。"
                elif i == 1:
                    dish['description'] = f"本店招牌{dish['name']}，采用传统工艺精心烹制，深受顾客喜爱。"
                elif i == 2:
                    dish['description'] = f"特色{dish['name']}，选用上等食材，制作精良，味道独特。"

        # 打印调试信息
        print(f"餐馆ID: {restaurant.id}")
        print(f"餐馆名称: {restaurant.name}")
        print(f"菜品数据: {result['dishes']}")

        # 添加餐馆详情信息
        if restaurant.cuisine_type == "川菜":
            result['description'] = f"{restaurant.name}是一家正宗的{restaurant.cuisine_type}餐厅。餐厅以麻辣鲜香的川菜为特色，提供多种经典川菜，如麻婆豆腐、水煮鱼、宫保鸡丁等。食材新鲜，调料正宗，口味地道，深受顾客喜爱。"
            result['features'] = f"特色菜品包括{result['dishes'][0]['name'] if result.get('dishes') and result['dishes'][0] else '麻婆豆腐'}、{result['dishes'][1]['name'] if result.get('dishes') and len(result['dishes']) > 1 and result['dishes'][1] else '水煮鱼'}等，麻辣鲜香，回味无穷。"
            result['cooking_method'] = "采用传统川菜烹饪技法，注重麻、辣、鲜、香、酥、嫩、脆、糯等多种口感，选用上等花椒、辣椒等调料，确保每道菜品都能带来正宗川菜体验。"
        elif restaurant.cuisine_type == "粤菜":
            result['description'] = f"{restaurant.name}是一家地道的{restaurant.cuisine_type}餐厅。餐厅以清淡鲜美的粤菜为特色，提供多种经典粤菜，如白切鸡、烧鹅、虾饺等。食材新鲜，做工精细，口味鲜美，深受顾客喜爱。"
            result['features'] = f"特色菜品包括{result['dishes'][0]['name'] if result.get('dishes') and result['dishes'][0] else '白切鸡'}、{result['dishes'][1]['name'] if result.get('dishes') and len(result['dishes']) > 1 and result['dishes'][1] else '烧鹅'}等，鲜香可口，回味无穷。"
            result['cooking_method'] = "采用传统粤菜烹饪技法，注重食材本身的鲜美，烹饪方式多样，包括蒸、煎、炒、炖等，保持食材的原汁原味，确保每道菜品都能带来正宗粤菜体验。"
        elif restaurant.cuisine_type == "湘菜":
            result['description'] = f"{restaurant.name}是一家正宗的{restaurant.cuisine_type}餐厅。餐厅以香辣可口的湘菜为特色，提供多种经典湘菜，如剁椒鱼头、口味虾、湘西腊肉等。食材新鲜，调料正宗，口味地道，深受顾客喜爱。"
            result['features'] = f"特色菜品包括{result['dishes'][0]['name'] if result.get('dishes') and result['dishes'][0] else '剁椒鱼头'}、{result['dishes'][1]['name'] if result.get('dishes') and len(result['dishes']) > 1 and result['dishes'][1] else '口味虾'}等，香辣可口，回味无穷。"
            result['cooking_method'] = "采用传统湘菜烹饪技法，注重香、辣、鲜、酸等多种口感，选用上等辣椒、豆豉等调料，确保每道菜品都能带来正宗湘菜体验。"
        else:
            result['description'] = f"{restaurant.name}是一家提供{restaurant.cuisine_type}的特色餐厅。餐厅环境优雅，服务周到，提供多种美味菜品，深受顾客喜爱。"
            result['features'] = f"特色菜品包括{result['dishes'][0]['name'] if result.get('dishes') and result['dishes'][0] else '招牌菜'}等，口味正宗，食材新鲜，做工精细。"
            result['cooking_method'] = "采用传统烹饪方法，注重食材新鲜度和口感，确保每道菜品都能带来美味体验。"

        # 添加餐馆评价信息
        result['rating'] = restaurant.evaluation
        result['rating_count'] = restaurant.number_of_view // 10 if restaurant.number_of_view else 0

        # 添加餐馆地址和联系方式
        result['address'] = "北京市"
        result['phone'] = "010-12345678"

        # 添加餐馆营业时间
        result['business_hours'] = "周一至周日 10:00-22:00"

        # 添加餐馆菜品图片 - 使用实际菜品图片
        result['dish_images'] = []

        # 从菜品中提取图片URL
        for dish in result['dishes']:
            if dish and dish.get('image_url'):
                result['dish_images'].append(dish['image_url'])

        # 如果没有找到菜品图片，使用默认图片
        if not result['dish_images']:
            result['dish_images'] = [
                f'/uploads/restaurants/dishes/{restaurant_id}_dish1.jpg',
                f'/uploads/restaurants/dishes/{restaurant_id}_dish2.jpg',
                f'/uploads/restaurants/dishes/{restaurant_id}_dish3.jpg'
            ]

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取餐馆详情出错: {str(e)}")
        return error(str(e))

@food_bp.route('/search', methods=['GET'])
def search_food():
    """
    搜索餐馆
    支持参数：
    - keyword: 搜索关键词，匹配餐馆名称、菜系或菜品
    - cuisine_type: 菜系类型
    - sort_by: 排序方式，支持 'relevance'(默认), 'popularity', 'rating', 'distance', 'price'
    - order: 排序顺序，支持 'desc'(默认), 'asc'
    - location_x: 位置x坐标，用于按距离排序
    - location_y: 位置y坐标，用于按距离排序
    - limit: 返回结果数量限制，默认10
    """
    try:
        # 获取请求参数
        keyword = request.args.get('keyword', '')
        cuisine_type = request.args.get('cuisine_type')
        sort_by = request.args.get('sort_by', 'relevance')
        order = request.args.get('order', 'desc')
        location_x = request.args.get('location_x', type=float)
        location_y = request.args.get('location_y', type=float)
        limit = request.args.get('limit', 10, type=int)

        # 获取所有餐馆
        restaurants = Restaurant.query.all()

        # 应用菜系过滤
        if cuisine_type:
            restaurants = RestaurantFinder.filter_by_cuisine_type(restaurants, [cuisine_type])

        # 应用关键词搜索
        if keyword:
            # 使用基于内容的搜索算法
            search_results = RestaurantFinder.content_based_search(restaurants, keyword)

            # 如果排序方式是相关度，直接使用搜索结果的相关度排序
            if sort_by == 'relevance':
                # 提取餐馆列表，已经按相关度排序
                restaurants = [result[0] for result in search_results]

                # 限制结果数量
                restaurants = restaurants[:limit]

                # 转换为字典列表
                result = [restaurant.to_dict() for restaurant in restaurants]

                # 添加相关度得分
                for i, (_, relevance) in enumerate(search_results[:limit]):
                    result[i]['relevance'] = relevance

                return success(result)
        else:
            # 如果没有关键词，使用所有餐馆
            search_results = [(restaurant, 1.0) for restaurant in restaurants]

        # 提取餐馆列表
        restaurants = [result[0] for result in search_results]

        # 确定排序方向
        reverse = (order.lower() == 'desc')

        # 应用排序
        if sort_by == 'rating':
            # 使用高效排序算法，只排序前limit个元素
            key_func = lambda x: x.evaluation
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        elif sort_by == 'distance' and location_x is not None and location_y is not None:
            # 按距离排序
            restaurants = RestaurantSorter.top_k_by_distance(restaurants, limit, location_x, location_y)
            # 如果是降序排序，需要反转结果
            if reverse:
                restaurants.reverse()
        elif sort_by == 'price':
            # 按价格排序
            key_func = lambda x: x.average_price_perperson if x.average_price_perperson is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)
        elif sort_by == 'popularity':
            # 按人气排序（使用浏览量）
            key_func = lambda x: x.number_of_view if x.number_of_view is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, reverse)

        # 限制结果数量
        restaurants = restaurants[:limit]

        # 转换为字典列表
        result = [restaurant.to_dict() for restaurant in restaurants]

        # 添加相关度得分（如果有关键词搜索）
        if keyword:
            # 创建餐馆ID到相关度的映射
            relevance_map = {restaurant.id: relevance for restaurant, relevance in search_results}

            # 添加相关度得分
            for i, restaurant_dict in enumerate(result):
                restaurant_id = restaurant_dict['id']
                result[i]['relevance'] = relevance_map.get(restaurant_id, 0.0)

        return success(result)
    except Exception as e:
        current_app.logger.error(f"搜索餐馆出错: {str(e)}")
        return error(str(e))

@food_bp.route('/nearby', methods=['GET'])
def get_nearby_food():
    """
    获取附近餐馆
    支持参数：
    - location_x: 位置x坐标（必需）
    - location_y: 位置y坐标（必需）
    - limit: 返回结果数量限制，默认10
    - max_distance: 最大距离，默认无限制
    - cuisine_type: 菜系类型
    """
    try:
        # 获取请求参数
        location_x = request.args.get('location_x', type=float)
        location_y = request.args.get('location_y', type=float)
        limit = request.args.get('limit', 10, type=int)
        max_distance = request.args.get('max_distance', type=float)
        cuisine_type = request.args.get('cuisine_type')

        # 检查必需参数
        if location_x is None or location_y is None:
            return error("缺少位置参数 location_x 或 location_y")

        # 获取所有餐馆
        restaurants = Restaurant.query.all()

        # 应用菜系过滤
        if cuisine_type:
            restaurants = RestaurantFinder.filter_by_cuisine_type(restaurants, [cuisine_type])

        # 计算每个餐馆的距离
        restaurants_with_distance = []
        for restaurant in restaurants:
            if restaurant.x is None or restaurant.y is None:
                continue

            # 计算欧几里得距离
            dx = restaurant.x - location_x
            dy = restaurant.y - location_y
            distance = (dx * dx + dy * dy) ** 0.5

            # 应用最大距离过滤
            if max_distance is not None and distance > max_distance:
                continue

            restaurants_with_distance.append((restaurant, distance))

        # 按距离排序
        restaurants_with_distance.sort(key=lambda x: x[1])

        # 限制结果数量
        restaurants_with_distance = restaurants_with_distance[:limit]

        # 转换为字典列表
        result = []
        for restaurant, distance in restaurants_with_distance:
            restaurant_dict = restaurant.to_dict()
            restaurant_dict['distance'] = distance
            result.append(restaurant_dict)

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取附近餐馆出错: {str(e)}")
        return error(str(e))

@food_bp.route('/cuisine-types', methods=['GET'])
def get_cuisine_types():
    """
    获取所有菜系类型
    """
    try:
        # 查询所有不同的菜系类型
        cuisine_types = db.session.query(Restaurant.cuisine_type).distinct().all()

        # 提取菜系类型列表
        result = [cuisine[0] for cuisine in cuisine_types if cuisine[0]]

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取菜系类型出错: {str(e)}")
        return error(str(e))

@food_bp.route('/suggestions', methods=['GET'])
def get_restaurant_suggestions():
    """
    获取餐馆名称建议
    支持参数：
    - keyword: 搜索关键词
    - limit: 返回结果数量限制，默认10
    """
    try:
        # 获取请求参数
        keyword = request.args.get('keyword', '')
        limit = request.args.get('limit', 10, type=int)

        if not keyword:
            return success([])

        # 获取所有餐馆
        restaurants = Restaurant.query.all()

        # 使用模糊查询找到匹配的餐馆
        matched_restaurants = RestaurantFinder.fuzzy_search(restaurants, keyword)

        # 提取餐馆名称和ID
        suggestions = []
        for restaurant in matched_restaurants[:limit]:
            suggestions.append({
                'id': restaurant.id,
                'name': restaurant.name,
                'cuisine_type': restaurant.cuisine_type,
                'image_url': restaurant.image_url
            })

        return success(suggestions)
    except Exception as e:
        current_app.logger.error(f"获取餐馆名称建议出错: {str(e)}")
        return error(str(e))

@food_bp.route('/collaborative-recommendations', methods=['GET'])
def get_collaborative_recommendations():
    """
    获取基于协同过滤的餐馆推荐
    支持参数：
    - user_id: 用户ID（必需）
    - limit: 返回结果数量限制，默认10
    """
    try:
        # 获取请求参数
        user_id = request.args.get('user_id', type=int)
        limit = request.args.get('limit', 10, type=int)

        # 检查必需参数
        if user_id is None:
            return error("缺少必要参数 user_id")

        # 使用协同过滤算法获取推荐
        recommended_restaurants = RestaurantRecommender.collaborative_filtering_recommend(user_id, limit)

        # 打印推荐结果，便于调试
        current_app.logger.info(f"用户 {user_id} 的协同过滤推荐结果: {recommended_restaurants}")

        # 如果没有推荐结果，尝试基于用户收藏的菜系进行推荐
        if not recommended_restaurants:
            # 获取用户收藏的餐馆
            favorite_restaurants = RestaurantFavorite.query.filter_by(user_id=user_id).all()
            favorite_restaurant_ids = [fav.restaurant_id for fav in favorite_restaurants]

            # 获取用户收藏餐馆的菜系
            cuisine_types = []
            if favorite_restaurant_ids:
                favorite_restaurant_objects = Restaurant.query.filter(Restaurant.id.in_(favorite_restaurant_ids)).all()
                cuisine_types = [r.cuisine_type for r in favorite_restaurant_objects if r.cuisine_type]

            # 如果用户有收藏的菜系，推荐相同菜系的餐馆
            if cuisine_types:
                current_app.logger.info(f"用户 {user_id} 收藏的菜系: {cuisine_types}")

                # 获取相同菜系的餐馆（排除已收藏的）
                similar_cuisine_restaurants = Restaurant.query.filter(
                    Restaurant.cuisine_type.in_(cuisine_types),
                    not_(Restaurant.id.in_(favorite_restaurant_ids))
                ).all()

                # 按评分排序
                key_func = lambda x: x.evaluation if x.evaluation is not None else 0
                similar_cuisine_restaurants = RestaurantSorter.top_k_sort(similar_cuisine_restaurants, limit, key_func, True)

                # 转换为字典列表
                result = [restaurant.to_dict() for restaurant in similar_cuisine_restaurants]

                # 添加推荐类型标记
                for item in result:
                    item['recommendation_type'] = 'cuisine_based'

                if result:
                    return success(result)

            # 如果没有基于菜系的推荐结果，返回热门餐馆
            restaurants = Restaurant.query.all()

            # 按人气排序
            key_func = lambda x: x.popularity if x.popularity is not None else 0
            restaurants = RestaurantSorter.top_k_sort(restaurants, limit, key_func, True)

            # 转换为字典列表
            result = [restaurant.to_dict() for restaurant in restaurants]

            # 添加推荐类型标记
            for item in result:
                item['recommendation_type'] = 'popularity'

            return success(result)

        # 获取推荐的餐馆详情
        restaurant_ids = [restaurant_id for restaurant_id, _ in recommended_restaurants]
        restaurants = Restaurant.query.filter(Restaurant.id.in_(restaurant_ids)).all()

        # 创建ID到得分的映射
        score_map = {restaurant_id: score for restaurant_id, score in recommended_restaurants}

        # 转换为字典列表，并保持原始排序
        result = []
        for restaurant_id, _ in recommended_restaurants:
            restaurant = next((r for r in restaurants if r.id == restaurant_id), None)
            if restaurant:
                restaurant_dict = restaurant.to_dict()
                restaurant_dict['collaborative_score'] = score_map[restaurant_id]
                restaurant_dict['recommendation_type'] = 'collaborative'
                result.append(restaurant_dict)

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取协同过滤推荐出错: {str(e)}")
        return error(str(e))

@food_bp.route('/fuzzy_search_cuisine', methods=['GET'])
def fuzzy_search_cuisine():
    """
    菜系模糊查询 - 用于AI生成模块的自动完成
    """
    try:
        query = request.args.get('query', '').strip()
        limit = int(request.args.get('limit', 10))

        if not query:
            # 如果没有查询词，返回所有菜系
            cuisine_types = db.session.query(Restaurant.cuisine_type).distinct().all()
            result = [{'cuisine_type': cuisine[0]} for cuisine in cuisine_types if cuisine[0]][:limit]
        else:
            # 模糊搜索菜系类型
            cuisine_types = db.session.query(Restaurant.cuisine_type).filter(
                Restaurant.cuisine_type.like(f'%{query}%')
            ).distinct().all()
            result = [{'cuisine_type': cuisine[0]} for cuisine in cuisine_types if cuisine[0]][:limit]

        return success(result, 'Fuzzy search completed successfully')

    except Exception as e:
        return error(f'Error in fuzzy search: {str(e)}')

@food_bp.route('/fuzzy_search_restaurants', methods=['GET'])
def fuzzy_search_restaurants():
    """
    餐厅模糊查询 - 用于AI生成模块的自动完成
    """
    try:
        query = request.args.get('query', '').strip()
        cuisine_type = request.args.get('cuisine_type', '').strip()
        limit = int(request.args.get('limit', 10))

        # 构建查询条件
        query_conditions = []

        if query:
            query_conditions.append(
                db.or_(
                    Restaurant.name.like(f'%{query}%'),
                    Restaurant.cuisine_type.like(f'%{query}%')
                )
            )

        if cuisine_type:
            query_conditions.append(Restaurant.cuisine_type == cuisine_type)

        # 执行查询
        if query_conditions:
            restaurants = Restaurant.query.filter(db.and_(*query_conditions)).limit(limit).all()
        else:
            # 如果没有查询条件，返回热门餐厅
            restaurants = Restaurant.query.order_by(Restaurant.popularity.desc()).limit(limit).all()

        result = []
        for restaurant in restaurants:
            result.append({
                'restaurant_id': restaurant.id,
                'name': restaurant.name,
                'cuisine_type': restaurant.cuisine_type,
                'evaluation': restaurant.evaluation,
                'popularity': restaurant.popularity,
                'image_url': restaurant.image_url
            })

        return success(result, 'Fuzzy search completed successfully')

    except Exception as e:
        return error(f'Error in fuzzy search: {str(e)}')
