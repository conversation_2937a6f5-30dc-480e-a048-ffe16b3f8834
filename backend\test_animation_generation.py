#!/usr/bin/env python3
"""
测试动画生成功能的完整流程
"""

import requests
import json
import time
import webbrowser
import os

# 配置
BASE_URL = "http://localhost:5000"
ARTICLE_ID = 1  # 请根据实际情况修改

def test_animation_generation_flow():
    """测试完整的动画生成流程"""
    print("=== 日记动画生成测试流程 ===")
    
    # 步骤1: 检查文章动画信息
    print("\n1. 检查文章动画信息...")
    info_url = f"{BASE_URL}/api/ai/diary_animation_info?article_id={ARTICLE_ID}"
    
    try:
        response = requests.get(info_url)
        if response.status_code == 200:
            info_data = response.json()
            print("✅ 文章信息获取成功")
            print(f"   标题: {info_data['data']['article_info']['title']}")
            print(f"   图片数量: {info_data['data']['media_analysis']['images_count']}")
            print(f"   视频数量: {info_data['data']['media_analysis']['videos_count']}")
            print(f"   推荐风格: {info_data['data']['animation_potential']['recommended_style']}")
            print(f"   预估时长: {info_data['data']['animation_potential']['estimated_duration']}秒")
            
            if not info_data['data']['animation_potential']['can_generate']:
                print("❌ 该文章不适合生成动画（缺少图片或视频）")
                return False
        else:
            print(f"❌ 获取文章信息失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    # 步骤2: 生成动画配置
    print("\n2. 生成动画配置...")
    generate_url = f"{BASE_URL}/api/ai/generate_simple_diary_animation"
    
    for style in ['cinematic', 'slideshow', 'storytelling']:
        print(f"\n   测试 {style} 风格...")
        
        try:
            response = requests.post(generate_url, json={
                'article_id': ARTICLE_ID,
                'style': style
            })
            
            if response.status_code == 200:
                config_data = response.json()
                print(f"   ✅ {style} 风格配置生成成功")
                
                animation_config = config_data['data']['animation_config']
                print(f"      动画ID: {animation_config['id']}")
                print(f"      场景数量: {len(animation_config['scenes'])}")
                print(f"      总时长: {animation_config['duration']}秒")
                
                # 获取预览URL
                preview_url = animation_config.get('preview_url')
                if preview_url:
                    full_preview_url = f"{BASE_URL}{preview_url}"
                    print(f"      预览URL: {full_preview_url}")
                    
                    # 测试预览页面
                    test_preview_page(full_preview_url, style)
                
            else:
                print(f"   ❌ {style} 风格配置生成失败: {response.status_code}")
                print(f"      错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ {style} 风格请求失败: {e}")
    
    # 步骤3: 查看生成的文件
    print("\n3. 查看生成的文件...")
    check_generated_files()
    
    return True

def test_preview_page(preview_url, style):
    """测试预览页面"""
    try:
        response = requests.get(preview_url)
        if response.status_code == 200:
            print(f"      ✅ {style} 预览页面可访问")
            
            # 询问是否在浏览器中打开
            user_input = input(f"      是否在浏览器中打开 {style} 预览？(y/n): ").lower()
            if user_input == 'y':
                webbrowser.open(preview_url)
                print(f"      🌐 已在浏览器中打开 {style} 预览")
        else:
            print(f"      ❌ {style} 预览页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"      ❌ {style} 预览页面测试失败: {e}")

def check_generated_files():
    """检查生成的文件"""
    animations_dir = os.path.join('static', 'diary_animations')
    
    if os.path.exists(animations_dir):
        files = os.listdir(animations_dir)
        if files:
            print(f"   📁 生成的文件 ({len(files)} 个):")
            for file in files:
                file_path = os.path.join(animations_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"      - {file} ({file_size} 字节)")
                
                # 如果是HTML文件，提供访问URL
                if file.endswith('.html'):
                    file_url = f"{BASE_URL}/static/diary_animations/{file}"
                    print(f"        访问URL: {file_url}")
        else:
            print("   📁 动画目录为空")
    else:
        print("   📁 动画目录不存在")

def test_storyboard_generation():
    """测试故事板生成"""
    print("\n=== 测试故事板生成 ===")
    
    storyboard_url = f"{BASE_URL}/api/ai/generate_diary_storyboard"
    
    try:
        response = requests.post(storyboard_url, json={
            'article_id': ARTICLE_ID,
            'style': 'cinematic'
        })
        
        if response.status_code == 200:
            storyboard_data = response.json()
            print("✅ 故事板生成成功")
            
            storyboard = storyboard_data['data']['storyboard']
            print(f"   总场景数: {storyboard['total_scenes']}")
            print(f"   动画风格: {storyboard['style']}")
            
            print("\n   场景列表:")
            for i, scene in enumerate(storyboard['scenes'][:5], 1):  # 只显示前5个场景
                print(f"      {i}. {scene['type']}: {scene['content'][:50]}...")
                print(f"         时长: {scene['duration']}秒, 动画: {scene['animation']}")
            
            if len(storyboard['scenes']) > 5:
                print(f"      ... 还有 {len(storyboard['scenes']) - 5} 个场景")
                
        else:
            print(f"❌ 故事板生成失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 故事板生成请求失败: {e}")

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试不存在的文章
    print("\n1. 测试不存在的文章...")
    try:
        response = requests.get(f"{BASE_URL}/api/ai/diary_animation_info?article_id=99999")
        if response.status_code == 404:
            print("   ✅ 正确返回404错误")
        else:
            print(f"   ❌ 期望404，实际返回: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试无效的风格
    print("\n2. 测试无效的动画风格...")
    try:
        response = requests.post(f"{BASE_URL}/api/ai/generate_simple_diary_animation", json={
            'article_id': ARTICLE_ID,
            'style': 'invalid_style'
        })
        if response.status_code == 400:
            print("   ✅ 正确返回400错误")
        else:
            print(f"   ❌ 期望400，实际返回: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def main():
    """主测试函数"""
    print("开始测试日记动画生成功能...")
    print(f"后端服务地址: {BASE_URL}")
    print(f"测试文章ID: {ARTICLE_ID}")
    
    # 检查后端服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/api/articles")
        if response.status_code != 200:
            print("❌ 后端服务可能未运行或有问题")
            return
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
        print("请确保后端服务正在运行在 http://localhost:5000")
        return
    
    print("✅ 后端服务连接正常")
    
    # 运行测试
    if test_animation_generation_flow():
        test_storyboard_generation()
        test_error_handling()
        
        print("\n=== 测试完成 ===")
        print("📝 查看动画的方式:")
        print("1. 使用上面显示的预览URL在浏览器中查看")
        print("2. 直接访问生成的HTML文件")
        print("3. 使用API返回的配置在前端实现自定义播放器")
        
        # 提供快速访问链接
        print(f"\n🔗 快速访问链接:")
        print(f"   电影风格: {BASE_URL}/api/ai/preview_diary_animation?article_id={ARTICLE_ID}&style=cinematic")
        print(f"   幻灯片风格: {BASE_URL}/api/ai/preview_diary_animation?article_id={ARTICLE_ID}&style=slideshow")
        print(f"   故事风格: {BASE_URL}/api/ai/preview_diary_animation?article_id={ARTICLE_ID}&style=storytelling")
    else:
        print("\n❌ 测试失败，请检查文章ID和数据")

if __name__ == "__main__":
    main()
