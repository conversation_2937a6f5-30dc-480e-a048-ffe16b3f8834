# AI生成接口
import time
import json
from flask import Blueprint, request, jsonify, current_app
import os
from utils.response import success, error
from utils.database import db
from sqlalchemy import or_
from models.location import Location
from models.article import Article
from services.ai_service import AIService
from models.restaurant import Restaurant
# from services.diary_animation_service import DiaryAnimationService  # 需要安装cv2等库
from services.simple_diary_animation_service import SimpleDiaryAnimationService

# 导入AI配置和服务
try:
    from config.ai_config import AIConfig
    from services.doubao_service import create_doubao_service
    AI_SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"AI服务导入失败: {e}")
    AI_SERVICES_AVAILABLE = False
    AIConfig = None
    create_doubao_service = None

ai_bp = Blueprint('ai', __name__)

@ai_bp.route('/generate', methods=['POST'])
def generate_animation():
    try:
        # 文件验证
        if 'images' not in request.files:
            return jsonify({"error": "No images uploaded"}), 400

        # 创建保存目录
        os.makedirs('generated', exist_ok=True)

        # 获取提示词
        prompt = request.form.get('prompt', 'A scenic travel animation')

        # 生成唯一文件名
        timestamp = int(time.time())
        filename = f"animation_{timestamp}.txt"
        output_path = os.path.join('generated', filename)

        try:
            # 调用AI服务生成内容描述
            from services.ai_service import AIService
            ai_service = AIService()

            description_prompt = f"""
            请为以下旅游场景生成一段详细的描述，可以用于生成图像或动画：

            场景: {prompt}

            请提供详细的视觉描述，包括场景、颜色、光线、氛围等元素。
            """

            # 调用AI生成
            ai_response = ai_service.generate_text(description_prompt, max_tokens=500)

            # 检查是否包含错误信息
            try:
                error_json = json.loads(ai_response)
                if "error" in error_json:
                    # 如果是API错误，使用本地生成方案
                    print(f"AI服务返回错误: {error_json['error']}")
                    raise Exception("API error")
            except json.JSONDecodeError:
                # 不是JSON，说明是正常的文本响应
                pass

            # 创建一个包含AI生成内容的文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"AI Generated Content\n")
                f.write(f"===================\n\n")
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Prompt: {prompt}\n\n")
                f.write(f"AI Generated Description:\n")
                f.write(f"{ai_response}\n\n")
                f.write(f"Note: This description would be used to generate an actual image or animation.")

            return jsonify({
                "status": "generated",
                "description": ai_response,
                "url": f"/generated/{filename}"
            }), 200

        except Exception as e:
            # 如果AI服务调用失败，回退到本地生成方案
            print(f"AI服务调用失败，使用本地生成方案: {str(e)}")

            # 使用本地生成方案生成场景描述
            scene_description = generate_scene_description(prompt)

            # 创建一个包含生成内容的文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"Generated Content (Fallback)\n")
                f.write(f"===================\n\n")
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Prompt: {prompt}\n\n")
                f.write(f"Generated Description:\n")
                f.write(f"{scene_description}\n\n")
                f.write(f"Note: This description would be used to generate an actual image or animation.")

            return jsonify({
                "status": "generated_fallback",
                "description": scene_description,
                "url": f"/generated/{filename}",
                "note": "Generated using fallback method due to AI service error"
            }), 200

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@ai_bp.route('/generate_plan', methods=['POST'])
def generate_travel_plan():
    """
    生成旅行计划
    根据提供的地点和天数生成旅行计划
    """
    try:
        print("=== AI生成旅游计划接口被调用 ===")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        if not data:
            print("错误: 没有提供数据")
            return error('No data provided')

        locations = data.get('locations', [])
        days = data.get('days', 1)
        preferences = data.get('preferences', '')

        print(f"解析后的参数: locations={locations}, days={days}, preferences={preferences}")

        if not locations:
            print("错误: 没有提供地点")
            return error('No locations provided')

        if not isinstance(locations, list):
            print("错误: 地点必须是列表")
            return error('Locations must be a list')

        if not isinstance(days, int) or days < 1:
            print("错误: 天数必须是正整数")
            return error('Days must be a positive integer')

        # 获取地点信息
        print(f"开始查询地点信息...")
        location_objects = []
        for location_id in locations:
            print(f"查询地点ID: {location_id}")
            location = Location.query.get(location_id)
            if location:
                print(f"找到地点: {location.name}")
                location_objects.append(location)
            else:
                print(f"未找到地点ID: {location_id}")

        print(f"有效地点数量: {len(location_objects)}")
        if not location_objects:
            print("错误: 没有找到有效地点")
            return error('No valid locations found')

        # 调用AI服务生成旅行计划
        from services.ai_service import AIService

        # 准备地点信息
        location_info = []
        for loc in location_objects:
            loc_type = "未知"
            if loc.type == 0:
                loc_type = "教育"
            elif loc.type == 1:
                loc_type = "文化"
            elif loc.type == 2:
                loc_type = "自然"
            elif loc.type == 3:
                loc_type = "娱乐"

            location_info.append({
                "id": loc.location_id,
                "name": loc.name,
                "type": loc_type,
                "keywords": loc.keyword if loc.keyword else ""
            })

        # 构建提示词
        prompt = f"""
        请为一个{days}天的旅行生成详细的旅行计划。旅行将包括以下地点：

        {json.dumps(location_info, ensure_ascii=False, indent=2)}

        用户偏好: {preferences}

        请根据地点类型和特点，合理安排每天的行程，包括：
        1. 每天游览的地点（平均分配所有地点，最后一天可以安排剩余地点）
        2. 每个地点的游览时间（上午、下午或晚上）
        3. 每个地点的详细游览建议和活动推荐
        4. 考虑地点之间的合理顺序

        请以JSON格式返回，格式如下：
        {{
            "title": "旅行计划标题",
            "days": [
                {{
                    "day": 1,
                    "locations": [
                        {{
                            "location_id": 地点ID,
                            "name": "地点名称",
                            "description": "详细的游览建议",
                            "time": "游览时间段"
                        }}
                    ]
                }}
            ]
        }}

        只返回JSON格式，不要有其他说明文字。
        """

        # 根据详细程度调整提示词
        plan_detail_level = current_app.config.get('PLAN_DETAIL_LEVEL', 'medium')
        if plan_detail_level == 'detailed':
            prompt += "\n请提供非常详细的游览建议，包括景点特色、历史背景、推荐活动等。"
        elif plan_detail_level == 'simple':
            prompt += "\n请提供简洁的游览建议，重点突出必看景点和活动。"

        try:
            # 调用AI生成
            print("开始调用AI服务...")
            ai_service = AIService()
            print("AI服务实例创建成功")

            ai_response = ai_service.generate_text(prompt, max_tokens=2000)
            print(f"AI服务响应: {ai_response[:200]}...")  # 只显示前200个字符

            try:
                # 尝试解析JSON响应
                plan = json.loads(ai_response)
                print("AI响应JSON解析成功")

                # 检查是否包含错误信息
                if "error" in plan:
                    # 如果是API错误，使用本地生成方案
                    print(f"AI服务返回错误: {plan['error']}")
                    print("使用本地生成方案...")
                    return generate_fallback_plan(location_objects, days, preferences)

                print("AI生成成功，返回结果")
                return success({'plan': plan}, 'Travel plan generated successfully')
            except json.JSONDecodeError:
                # 如果AI返回的不是有效JSON，尝试提取JSON部分
                import re
                json_match = re.search(r'({[\s\S]*})', ai_response)
                if json_match:
                    try:
                        json_str = json_match.group(1)
                        plan = json.loads(json_str)
                        return success({'plan': plan}, 'Travel plan generated successfully')
                    except:
                        pass

                # 如果仍然无法解析，回退到传统方法
                return generate_fallback_plan(location_objects, days, preferences)
        except Exception as e:
            # 如果AI服务调用失败，回退到传统方法
            print(f"AI服务调用失败: {str(e)}")
            import traceback
            traceback.print_exc()
            print("使用本地生成方案...")
            return generate_fallback_plan(location_objects, days, preferences)

    except Exception as e:
        print(f"生成旅游计划时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating travel plan: {str(e)}')

def generate_fallback_plan(location_objects, days, preferences=None):
    """
    使用传统方法生成旅行计划（作为AI服务失败时的回退方案）

    Args:
        location_objects: 地点对象列表
        days: 旅行天数
        preferences: 用户偏好，如"文化"、"自然"等
    """
    # 生成旅行计划标题
    title = f'旅行计划 ({days}天)'
    if preferences:
        title = f'{preferences}主题{title}'

    # 生成旅行计划
    plan = {
        'title': title,
        'days': []
    }

    # 每天安排地点
    locations_per_day = max(1, len(location_objects) // days)
    for day in range(1, days + 1):
        day_plan = {
            'day': day,
            'locations': []
        }

        # 获取当天的地点
        start_idx = (day - 1) * locations_per_day
        end_idx = min(start_idx + locations_per_day, len(location_objects))

        # 如果是最后一天，把剩余的地点都安排上
        if day == days:
            end_idx = len(location_objects)

        for i in range(start_idx, end_idx):
            location = location_objects[i]

            # 根据地点类型和用户偏好生成不同的描述
            description = '参观这个地点，了解当地文化。'

            # 根据地点类型生成基础描述
            if location.type == 0:  # 教育类
                base_desc = f'参观{location.name}，感受学术氛围'
                if preferences == '文化':
                    description = f'{base_desc}，了解其深厚的文化底蕴和历史意义。建议参观主要展馆和历史建筑，聆听讲解员的详细介绍。'
                else:
                    description = f'{base_desc}。这里有丰富的教育资源和学术氛围，值得细细品味。'

            elif location.type == 1:  # 文化类
                base_desc = f'参观{location.name}，了解历史文化'
                if preferences == '文化':
                    description = f'{base_desc}。这里是文化瑰宝，建议您深入了解其历史背景、建筑特色和文化意义，可以请专业导游进行讲解。'
                else:
                    description = f'{base_desc}。这里有丰富的文化遗产和历史故事，值得一游。'

            elif location.type == 2:  # 自然类
                base_desc = f'游览{location.name}，欣赏自然风光'
                if preferences == '文化':
                    description = f'{base_desc}，同时了解这里的人文历史和文化背景。自然景观中往往蕴含着深厚的文化内涵。'
                else:
                    description = f'{base_desc}。这里有壮丽的自然景观，是放松身心的好去处。'

            elif location.type == 3:  # 娱乐类
                base_desc = f'前往{location.name}，享受休闲时光'
                if preferences == '文化':
                    description = f'{base_desc}，体验当地特色文化活动和娱乐方式。了解现代文化与传统文化的融合。'
                else:
                    description = f'{base_desc}。这里有丰富的娱乐设施和活动，适合放松娱乐。'

            # 生成时间安排
            if i == start_idx:
                time_slot = '上午9:00-12:00'
            elif i == end_idx - 1:
                time_slot = '下午16:00-18:00'
            else:
                time_slot = '下午13:00-16:00'

            day_plan['locations'].append({
                'location_id': location.location_id,
                'name': location.name,
                'description': description,
                'time': time_slot
            })

        plan['days'].append(day_plan)

    return success({'plan': plan}, 'Travel plan generated successfully (fallback method)')

@ai_bp.route('/generate_summary', methods=['POST'])
def generate_article_summary_old():
    """
    生成文章摘要
    根据文章ID生成摘要
    """
    try:
        data = request.get_json()

        if not data:
            return error('No data provided')

        article_id = data.get('article_id')
        max_length = data.get('max_length', current_app.config.get('SUMMARY_MAX_LENGTH', 200))

        if not article_id:
            return error('No article_id provided')

        # 获取文章
        article = Article.query.get(article_id)

        if not article:
            return error('Article not found')

        # 获取文章内容
        content = article.content

        # 如果文章内容是压缩的，需要解压
        if article.huffman_codes:
            from services.article_service import ArticleService
            import json
            service = ArticleService()
            # 将JSON字符串解析为字典
            huffman_codes = json.loads(article.huffman_codes)
            content = service.decompress_text(article.content, huffman_codes)

        # 确保content是字符串
        if isinstance(content, bytes):
            content = content.decode('utf-8')

        try:
            # 调用AI服务生成摘要
            from services.ai_service import AIService
            ai_service = AIService()

            # 构建提示词
            prompt = f"""
            请为以下文章生成一个简洁、准确的摘要，长度不超过{max_length}个字符：

            标题: {article.title}

            内容:
            {content[:3000]}  # 限制输入长度，避免超出AI模型的上下文限制

            请确保摘要包含文章的主要观点和关键信息。
            """

            # 调用AI生成
            ai_response = ai_service.generate_text(prompt, max_tokens=max_length // 2)  # 估算token数约为字符数的一半

            # 检查是否包含错误信息
            try:
                error_json = json.loads(ai_response)
                if "error" in error_json:
                    # 如果是API错误，使用本地生成方案
                    print(f"AI服务返回错误: {error_json['error']}")
                    raise Exception("API error")
            except json.JSONDecodeError:
                # 不是JSON，说明是正常的文本响应
                pass

            # 确保摘要不超过最大长度
            if len(ai_response) > max_length:
                ai_response = ai_response[:max_length-3] + '...'

            return success({'summary': ai_response}, 'Article summary generated successfully')

        except Exception as e:
            # 如果AI服务调用失败，回退到简单摘要生成
            print(f"AI服务调用失败，使用本地生成方案: {str(e)}")

            # 提取文章的前几句话作为摘要
            sentences = content.split('。')[:3]
            simple_summary = '。'.join(sentences)

            # 确保摘要不超过最大长度
            if len(simple_summary) > max_length:
                simple_summary = simple_summary[:max_length-3] + '...'

            return success({
                'summary': simple_summary,
                'note': 'Generated using fallback method due to AI service error'
            }, 'Article summary generated successfully (fallback method)')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error generating article summary: {str(e)}')

@ai_bp.route('/generate_description', methods=['POST'])
def generate_location_description():
    """
    生成地点描述
    根据地点ID生成详细的地点描述
    """
    try:
        data = request.get_json()

        if not data:
            return error('No data provided')

        location_id = data.get('location_id')
        style = data.get('style', '详细')  # 详细、简洁、文学
        focus = data.get('focus', '综合')  # 历史、文化、建筑、自然等

        if not location_id:
            return error('No location_id provided')

        # 获取地点
        location = Location.query.get(location_id)

        if not location:
            return error('Location not found')

        # 获取地点类型
        location_type = "未知"
        if location.type == 0:
            location_type = "教育"
        elif location.type == 1:
            location_type = "文化"
        elif location.type == 2:
            location_type = "自然"
        elif location.type == 3:
            location_type = "娱乐"

        try:
            # 调用AI服务生成地点描述
            from services.ai_service import AIService
            ai_service = AIService()

            # 构建提示词
            prompt = f"""
            请为以下旅游地点生成一段{style}的描述，重点关注{focus}方面：

            地点名称: {location.name}
            地点类型: {location_type}
            关键词: {location.keyword if location.keyword else "无"}

            描述应该包括地点的特色、历史背景、文化意义、建筑特点、自然风光等方面，让游客对这个地点有全面的了解。
            """

            # 根据风格调整提示词
            if style == '详细':
                prompt += "\n请提供全面、详尽的描述，包括各个方面的信息。"
            elif style == '简洁':
                prompt += "\n请提供简洁明了的描述，突出最重要的特点。"
            elif style == '文学':
                prompt += "\n请使用优美的文学语言，生动形象地描述这个地点，营造吸引人的氛围。"

            # 根据重点调整提示词
            if focus == '历史':
                prompt += "\n请重点描述这个地点的历史背景、历史事件和历史意义。"
            elif focus == '文化':
                prompt += "\n请重点描述这个地点的文化特色、文化活动和文化意义。"
            elif focus == '建筑':
                prompt += "\n请重点描述这个地点的建筑风格、建筑特点和建筑历史。"
            elif focus == '自然':
                prompt += "\n请重点描述这个地点的自然风光、自然景观和生态环境。"

            # 调用AI生成
            ai_response = ai_service.generate_text(prompt, max_tokens=1000)

            # 检查是否包含错误信息
            try:
                error_json = json.loads(ai_response)
                if "error" in error_json:
                    # 如果是API错误，使用本地生成方案
                    print(f"AI服务返回错误: {error_json['error']}")
                    raise Exception("API error")
            except json.JSONDecodeError:
                # 不是JSON，说明是正常的文本响应
                pass

            return success({
                'description': ai_response,
                'style': style,
                'focus': focus
            }, 'Location description generated successfully')

        except Exception as e:
            # 如果AI服务调用失败，回退到本地生成方案
            print(f"AI服务调用失败，使用本地生成方案: {str(e)}")

            # 生成本地描述
            description = generate_local_description(location, location_type, style, focus)

            return success({
                'description': description,
                'style': style,
                'focus': focus,
                'note': 'Generated using fallback method due to AI service error'
            }, 'Location description generated successfully (fallback method)')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error generating location description: {str(e)}')





@ai_bp.route('/generate_travel_animation', methods=['POST'])
def generate_travel_animation_aigc():
    """
    生成AIGC旅游动画 - 支持已有文章和上传新内容，调用豆包API
    """
    try:
        print("收到请求: POST /api/ai/generate_travel_animation")
        print(f"查询参数: {dict(request.args)}")

        data = request.get_json()
        print(f"JSON数据: {data}")

        if not data:
            return error('No data provided')

        # 检查是否使用上传的新内容
        use_upload = 'title' in data and 'content' in data
        use_doubao = data.get('use_doubao', False)

        animation_style = data.get('animation_style', '温馨')
        duration = data.get('duration', '短片')
        focus_elements = data.get('focus_elements', '')

        if use_upload:
            # 使用上传的新内容
            title = data.get('title')
            content = data.get('content')
            location = data.get('location')
            images = data.get('images', [])
            videos = data.get('videos', [])

            print(f"使用上传内容生成动画: title={title}, location={location}")

            # 准备文章数据
            article_data = {
                'id': f"upload_{int(time.time())}",
                'title': title,
                'content': content,
                'location': location,
                'images': [{'url': url, 'type': 'uploaded'} for url in images],
                'videos': [{'url': url, 'type': 'uploaded'} for url in videos]
            }
        else:
            # 使用已有文章
            article_id = data.get('article_id')
            if not article_id:
                return error('No article_id provided')

            # 验证文章是否存在
            article = Article.query.get(article_id)
            if not article:
                return error('Article not found')

            # 准备文章数据
            article_data = {
                'id': article.article_id,
                'title': article.title,
                'content': article.content,
                'location': article.location,
                'images': [],
                'videos': []
            }

            # 收集已有文章的图片和视频
            for i in range(1, 7):  # 最多6张图片
                if i == 1:
                    url = article.image_url
                else:
                    url = getattr(article, f'image_url_{i}', None)
                if url and url.strip():
                    # 如果是相对路径，转换为完整URL
                    if url.startswith('/uploads/'):
                        url = f"http://localhost:5000{url}"

                    article_data['images'].append({
                        'url': url,
                        'index': i,
                        'type': 'original'
                    })
                    print(f"📸 发现图片 {i}: {url}")

            for i in range(1, 4):  # 最多3个视频
                if i == 1:
                    url = article.video_url
                else:
                    url = getattr(article, f'video_url_{i}', None)
                if url and url.strip():
                    article_data['videos'].append({
                        'url': url,
                        'index': i,
                        'type': 'original'
                    })
                    print(f"🎥 发现视频 {i}: {url}")

        # 调用AIGC服务，根据是否使用豆包选择不同的服务
        if use_doubao:
            # 使用豆包大模型服务
            from services.doubao_service import DoubaoService
            doubao_service = DoubaoService()
            animation_result = doubao_service.generate_travel_animation(
                article_data=article_data,
                style=animation_style,
                duration=duration,
                focus_elements=focus_elements
            )
        else:
            # 使用默认的AIGC服务
            from services.aigc_animation_service import AIGCAnimationService
            aigc_service = AIGCAnimationService()
            animation_result = aigc_service.generate_travel_animation(
                article_data=article_data,
                style=animation_style,
                duration=duration,
                focus_elements=focus_elements
            )

        if animation_result.get('success'):
            return success(animation_result, 'AIGC travel animation generated successfully')
        else:
            return error(animation_result.get('error', 'Failed to generate AIGC animation'))

    except Exception as e:
        print(f"生成AIGC旅游动画时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating AIGC travel animation: {str(e)}')


@ai_bp.route('/generate_diary_animation', methods=['POST'])
def generate_diary_animation():
    """
    基于日记内容生成旅游动画
    利用用户的旅游日记中的图片、视频、文本内容生成个性化动画
    """
    try:
        data = request.get_json()

        if not data:
            return error('No data provided')

        article_id = data.get('article_id')
        animation_style = data.get('animation_style', 'cinematic')  # 前端发送的是animation_style
        duration = data.get('duration', '中等')  # 前端发送的duration
        focus_elements = data.get('focus_elements', '')  # 前端发送的focus_elements

        if not article_id:
            return error('No article_id provided')

        # 验证文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # 映射前端的动画风格到后端的style
        style_mapping = {
            '温馨': 'cinematic',
            '活泼': 'slideshow',
            '文艺': 'storytelling',
            '大气': 'cinematic'
        }
        style = style_mapping.get(animation_style, 'cinematic')

        try:
            # 使用真正的AIGC动画生成服务
            from services.aigc_animation_service import AIGCAnimationService

            # 准备文章数据
            article_data = {
                'title': article.title,
                'content': article.content,
                'location': article.location,
                'created_at': article.created_at,
                'images': [],
                'videos': []
            }

            # 解压文章内容（如果需要）
            if article.huffman_codes:
                from services.article_service import ArticleService
                service = ArticleService()
                huffman_codes = json.loads(article.huffman_codes)
                article_data['content'] = service.decompress_text(article.content, huffman_codes)

            # 确保content是字符串
            if isinstance(article_data['content'], bytes):
                article_data['content'] = article_data['content'].decode('utf-8')

            # 收集文章的图片和视频 - 修复数据格式
            for i in range(1, 7):  # 最多6张图片
                if i == 1:
                    url = article.image_url
                else:
                    url = getattr(article, f'image_url_{i}', None)
                if url and url.strip():
                    # 如果是相对路径，转换为完整URL
                    if url.startswith('/uploads/'):
                        url = f"http://localhost:5000{url}"

                    article_data['images'].append({
                        'url': url,
                        'index': i,
                        'type': 'original'
                    })
                    print(f"📸 发现图片 {i}: {url}")

            for i in range(1, 4):  # 最多3个视频
                if i == 1:
                    url = article.video_url
                else:
                    url = getattr(article, f'video_url_{i}', None)
                if url and url.strip():
                    article_data['videos'].append({
                        'url': url,
                        'index': i,
                        'type': 'original'
                    })
                    print(f"🎥 发现视频 {i}: {url}")

            aigc_service = AIGCAnimationService()
            animation_result = aigc_service.generate_travel_animation(
                article_data=article_data,
                style=animation_style,
                duration=duration,
                focus_elements=focus_elements
            )

            if animation_result.get('success', True):  # 如果没有success字段，默认为成功
                return success({
                    'animation': animation_result,
                    'article_info': {
                        'id': article.article_id,
                        'title': article.title,
                        'location': article.location,
                        'created_at': article.created_at.isoformat() if article.created_at else None
                    },
                    'generation_type': 'enhanced_aigc',
                    'features': [
                        'AI生成图片',
                        'AI生成视频',
                        '智能动画脚本',
                        '多媒体整合'
                    ]
                }, 'Enhanced AIGC animation generated successfully')
            else:
                error_msg = animation_result.get('error', 'Unknown error')
                return error(f'Enhanced animation generation failed: {error_msg}')

        except Exception as e:
            print(f"Diary animation generation failed: {str(e)}")
            # 回退到简单的动画描述生成
            return generate_fallback_diary_animation(article, style)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error generating diary animation: {str(e)}')

@ai_bp.route('/query_aigc_video_status/<task_id>', methods=['GET'])
def query_aigc_video_status(task_id):
    """查询AIGC视频生成状态"""
    try:
        print(f"查询AIGC视频状态: {task_id}")

        # 使用真正的AIGC服务查询状态
        from services.real_aigc_service import RealAIGCService
        aigc_service = RealAIGCService()

        result = aigc_service.query_video_status(task_id)

        if result.get('success', True):
            return success({
                'task_id': task_id,
                'status': result.get('status', 'unknown'),
                'video_url': result.get('video_url'),
                'progress': result.get('progress', 0),
                'platform': result.get('platform', 'unknown'),
                'error': result.get('error')
            }, 'AIGC video status retrieved successfully')
        else:
            return error(result.get('error', '查询失败'), 500)

    except Exception as e:
        print(f"AIGC video status query failed: {e}")
        return error(f'查询AIGC视频状态失败: {str(e)}', 500)

@ai_bp.route('/query_video_status', methods=['POST'])
def query_video_generation_status():
    """
    查询AI视频生成状态
    """
    try:
        data = request.get_json()

        if not data:
            return error('No data provided')

        task_ids = data.get('task_ids', [])

        if not task_ids:
            return error('No task_ids provided')

        # 使用增强版服务查询状态
        from services.enhanced_travel_animation_service import EnhancedTravelAnimationService

        enhanced_service = EnhancedTravelAnimationService()
        result = enhanced_service.query_video_generation_status(task_ids)

        if result['success']:
            return success(result['results'], 'Video status queried successfully')
        else:
            return error(result['error'])

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error querying video status: {str(e)}')

@ai_bp.route('/generate_diary_storyboard', methods=['POST'])
def generate_diary_storyboard():
    """
    基于日记内容生成动画故事板
    为用户提供动画预览和编辑建议
    """
    try:
        data = request.get_json()

        if not data:
            return error('No data provided')

        article_id = data.get('article_id')
        style = data.get('style', 'cinematic')

        if not article_id:
            return error('No article_id provided')

        # 验证文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        try:
            # 使用简化版动画生成服务生成故事板
            simple_service = SimpleDiaryAnimationService()

            # 提取文章内容
            content_data = simple_service._extract_article_content(article)
            if not content_data:
                return error('Failed to extract article content')

            # 生成动画脚本
            animation_script = simple_service._generate_animation_script(content_data, style)

            return success({
                'storyboard': animation_script,
                'content_summary': {
                    'title': content_data['title'],
                    'location': content_data['location'],
                    'images_count': len(content_data['images']),
                    'videos_count': len(content_data['videos']),
                    'content_length': len(content_data['content'])
                },
                'article_id': article_id,
                'style': style
            }, 'Diary storyboard generated successfully')

        except Exception as e:
            print(f"Storyboard generation failed: {str(e)}")
            return error(f'Failed to generate storyboard: {str(e)}')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error generating diary storyboard: {str(e)}')

def generate_fallback_diary_animation(article: Article, style: str):
    """
    生成回退的日记动画描述（当真正的动画生成失败时）
    """
    try:
        # 解压文章内容
        content = article.content
        if article.huffman_codes:
            from services.article_service import ArticleService
            service = ArticleService()
            huffman_codes = json.loads(article.huffman_codes)
            content = service.decompress_text(article.content, huffman_codes)

        if isinstance(content, bytes):
            content = content.decode('utf-8')

        # 收集媒体信息
        images = []
        for i in range(1, 7):
            if i == 1:
                url = article.image_url
            else:
                url = getattr(article, f'image_url_{i}', None)
            if url:
                images.append(url)

        videos = []
        for i in range(1, 4):
            if i == 1:
                url = article.video_url
            else:
                url = getattr(article, f'video_url_{i}', None)
            if url:
                videos.append(url)

        # 生成动画描述
        animation_description = f"""
        基于您的旅游日记《{article.title}》生成的{style}风格动画描述：

        📍 地点：{article.location or '未指定'}
        🖼️ 图片数量：{len(images)}个
        🎥 视频数量：{len(videos)}个
        📝 内容长度：{len(content)}字符

        动画构成：
        1. 开场：标题"{article.title}"淡入效果
        2. 地点介绍：显示地点信息
        3. 图片展示：{len(images)}张图片依次展示，每张4秒
        4. 视频片段：{len(videos)}个视频片段穿插其中
        5. 文字叙述：基于日记内容的关键句子
        6. 结尾：感谢观看的文字

        建议配乐：根据{style}风格选择合适的背景音乐
        总时长：约{(len(images) * 4 + len(videos) * 5 + 10)}秒

        注意：这是动画描述，实际动画生成需要视频处理服务支持。
        """

        return success({
            'animation_description': animation_description,
            'article_id': article.article_id,
            'style': style,
            'media_summary': {
                'images': images,
                'videos': videos,
                'images_count': len(images),
                'videos_count': len(videos)
            },
            'note': 'This is a fallback description. Actual video generation requires additional video processing services.'
        }, 'Diary animation description generated successfully (fallback method)')

    except Exception as e:
        return error(f'Error generating fallback diary animation: {str(e)}')

@ai_bp.route('/generate_simple_diary_animation', methods=['POST'])
def generate_simple_diary_animation():
    """
    生成简化版日记动画配置
    不依赖复杂的视频处理库，生成可在前端播放的动画配置
    """
    try:
        data = request.get_json()

        if not data:
            return error('No data provided')

        article_id = data.get('article_id')
        style = data.get('style', 'cinematic')

        if not article_id:
            return error('No article_id provided')

        # 验证文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # 验证动画风格
        valid_styles = ['cinematic', 'slideshow', 'storytelling']
        if style not in valid_styles:
            return error(f'Invalid style. Valid styles: {", ".join(valid_styles)}')

        try:
            # 使用简化版动画生成服务
            simple_service = SimpleDiaryAnimationService()
            animation_config = simple_service.generate_diary_animation_config(article_id, style)

            if animation_config:
                return success({
                    'animation_config': animation_config,
                    'preview_available': True,
                    'message': 'Simple diary animation config generated successfully'
                }, 'Simple diary animation config generated successfully')
            else:
                return error('Failed to generate simple diary animation config')

        except Exception as e:
            print(f"Simple diary animation generation failed: {str(e)}")
            return error(f'Failed to generate simple diary animation: {str(e)}')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error generating simple diary animation: {str(e)}')

@ai_bp.route('/preview_diary_animation', methods=['GET'])
def preview_diary_animation():
    """
    生成日记动画的HTML预览页面
    """
    try:
        article_id = request.args.get('article_id')
        style = request.args.get('style', 'cinematic')

        if not article_id:
            return error('No article_id provided')

        try:
            article_id = int(article_id)
        except ValueError:
            return error('Invalid article_id format')

        # 验证文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        try:
            # 生成HTML预览
            simple_service = SimpleDiaryAnimationService()
            preview_url = simple_service.generate_html_preview(article_id, style)

            if preview_url:
                # 重定向到预览页面
                from flask import redirect
                return redirect(preview_url)
            else:
                return error('Failed to generate preview')

        except Exception as e:
            print(f"Preview generation failed: {str(e)}")
            return error(f'Failed to generate preview: {str(e)}')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error generating preview: {str(e)}')

@ai_bp.route('/diary_animation_info', methods=['GET'])
def get_diary_animation_info():
    """
    获取日记动画生成的相关信息
    """
    try:
        article_id = request.args.get('article_id')

        if not article_id:
            return error('No article_id provided')

        try:
            article_id = int(article_id)
        except ValueError:
            return error('Invalid article_id format')

        # 验证文章是否存在
        article = Article.query.get(article_id)
        if not article:
            return error('Article not found')

        # 分析文章内容
        simple_service = SimpleDiaryAnimationService()
        content_data = simple_service._extract_article_content(article)

        if not content_data:
            return error('Failed to extract article content')

        # 返回分析结果
        return success({
            'article_info': {
                'article_id': article_id,
                'title': content_data['title'],
                'location': content_data['location'],
                'content_length': len(content_data['content']),
                'created_at': content_data['created_at'].isoformat() if content_data['created_at'] else None
            },
            'media_analysis': {
                'images_count': len(content_data['images']),
                'videos_count': len(content_data['videos']),
                'images': content_data['images'],
                'videos': content_data['videos']
            },
            'animation_potential': {
                'can_generate': len(content_data['images']) > 0 or len(content_data['videos']) > 0,
                'estimated_duration': len(content_data['images']) * 4 + len(content_data['videos']) * 6 + 10,
                'recommended_style': 'cinematic' if len(content_data['images']) >= 3 else 'slideshow'
            },
            'available_styles': ['cinematic', 'slideshow', 'storytelling']
        }, 'Diary animation info retrieved successfully')

    except Exception as e:
        import traceback
        traceback.print_exc()
        return error(f'Error getting diary animation info: {str(e)}')

def generate_local_description(location, location_type, style, focus):
    """
    使用本地方法生成地点描述

    Args:
        location: 地点对象
        location_type: 地点类型（教育、文化、自然、娱乐）
        style: 描述风格（详细、简洁、文学）
        focus: 描述重点（历史、文化、建筑、自然）

    Returns:
        生成的描述文本
    """
    # 基础描述
    base_description = f"{location.name}是一个{location_type}类型的地点"
    if location.keyword:
        base_description += f"，以{location.keyword}而闻名"
    base_description += "。"

    # 根据地点类型添加特定描述
    type_description = ""
    if location.type == 0:  # 教育类
        type_description = f"{location.name}拥有浓厚的学术氛围，是学习和研究的理想场所。这里有丰富的教育资源和历史积淀，吸引了众多学者和游客前来参观。"
    elif location.type == 1:  # 文化类
        type_description = f"{location.name}是文化瑰宝，蕴含着丰富的历史和文化内涵。这里保存了许多珍贵的文化遗产，展示了当地独特的文化特色。"
    elif location.type == 2:  # 自然类
        type_description = f"{location.name}拥有壮丽的自然风光，四季景色各异。这里的自然环境保存完好，是亲近自然、放松身心的绝佳去处。"
    elif location.type == 3:  # 娱乐类
        type_description = f"{location.name}提供多样化的娱乐活动，适合各个年龄段的游客。这里设施完善，服务周到，能够带给游客愉快的体验。"

    # 根据重点添加特定描述
    focus_description = ""
    if focus == '历史':
        focus_description = f"{location.name}有着悠久的历史，见证了时代的变迁。历史上，这里曾发生过许多重要事件，留下了丰富的历史遗迹和故事。"
    elif focus == '文化':
        focus_description = f"{location.name}是文化交流的重要场所，汇聚了多元的文化元素。这里经常举办各类文化活动，展示当地独特的文化魅力。"
    elif focus == '建筑':
        focus_description = f"{location.name}的建筑风格独特，融合了传统与现代元素。建筑设计精巧，结构合理，是建筑艺术的典范。"
    elif focus == '自然':
        focus_description = f"{location.name}自然环境优美，生态系统丰富多样。这里的自然景观四季变化，每个季节都有不同的美丽风景。"
    else:  # 综合
        focus_description = f"{location.name}是一个综合性的旅游目的地，兼具历史、文化、建筑和自然等多方面的特色。无论您对哪方面感兴趣，都能在这里找到满足。"

    # 根据风格组合最终描述
    if style == '简洁':
        # 简洁风格：基础描述 + 简短的类型描述
        final_description = f"{base_description} {type_description.split('。')[0]}。"
    elif style == '文学':
        # 文学风格：更加优美的语言
        literary_intro = f"漫步于{location.name}，仿佛穿越时空的长廊。"
        literary_body = ""
        if location.type == 0:  # 教育类
            literary_body = f"这里书香氤氲，学术氛围浓厚，是知识的殿堂，智慧的源泉。每一砖每一瓦都诉说着求知的故事，每一草每一木都传递着学术的芬芳。"
        elif location.type == 1:  # 文化类
            literary_body = f"这里文化底蕴深厚，历史长河中沉淀的珍珠在此闪耀。古老的传说与现代的活力在此交融，编织成一幅绚丽的文化画卷。"
        elif location.type == 2:  # 自然类
            literary_body = f"这里自然风光旖旎，四季更迭间展现不同的美丽。清风拂面，鸟语花香，仿佛置身于大自然的诗篇中，让人流连忘返。"
        elif location.type == 3:  # 娱乐类
            literary_body = f"这里欢声笑语不断，是都市喧嚣中的欢乐岛屿。多彩的活动如繁星点点，点亮每位游客的美好时光，留下难忘的记忆。"

        literary_ending = f"无论何时来到{location.name}，都能感受到它独特的魅力，让人心驰神往，流连忘返。"
        final_description = f"{literary_intro} {literary_body} {literary_ending}"
    else:  # 详细
        # 详细风格：包含所有信息
        visit_tips = f"游览{location.name}时，建议预留足够的时间，细细品味其中的特色。最佳游览季节是春秋两季，天气宜人，景色优美。"
        final_description = f"{base_description} {type_description} {focus_description} {visit_tips}"

    return final_description

def generate_scene_description(prompt):
    """
    使用本地方法生成场景描述

    Args:
        prompt: 用户提供的场景提示词

    Returns:
        生成的场景描述文本
    """
    # 提取关键词
    keywords = prompt.lower().split()

    # 场景类型识别
    scene_type = "旅游"
    if any(word in keywords for word in ["城市", "街道", "建筑", "摩天大楼"]):
        scene_type = "城市"
    elif any(word in keywords for word in ["山", "森林", "湖", "海", "河", "自然"]):
        scene_type = "自然"
    elif any(word in keywords for word in ["历史", "古代", "古迹", "遗址", "博物馆"]):
        scene_type = "历史"
    elif any(word in keywords for word in ["海滩", "度假", "休闲", "放松"]):
        scene_type = "度假"

    # 时间设置
    time_of_day = "白天"
    if any(word in keywords for word in ["夜", "晚上", "黄昏", "日落"]):
        time_of_day = "夜晚"
    elif any(word in keywords for word in ["早晨", "日出", "黎明"]):
        time_of_day = "清晨"
    elif any(word in keywords for word in ["下午", "傍晚", "黄昏"]):
        time_of_day = "傍晚"

    # 天气设置
    weather = "晴朗"
    if any(word in keywords for word in ["雨", "雨天", "下雨"]):
        weather = "雨天"
    elif any(word in keywords for word in ["雪", "雪景", "下雪"]):
        weather = "雪天"
    elif any(word in keywords for word in ["雾", "雾气", "薄雾"]):
        weather = "雾天"
    elif any(word in keywords for word in ["云", "多云"]):
        weather = "多云"

    # 根据场景类型生成描述
    if scene_type == "城市":
        if time_of_day == "白天":
            base_desc = f"这是一个充满活力的城市场景，高耸的摩天大楼在蓝天的映衬下显得格外壮观。街道上人来人往，各种现代建筑展示着城市的繁华与现代感。"
        elif time_of_day == "夜晚":
            base_desc = f"夜幕下的城市灯火辉煌，霓虹灯和街灯将整个城市装点得如梦如幻。摩天大楼的轮廓在夜空中勾勒出城市的天际线，反射着五彩斑斓的光芒。"
        else:
            base_desc = f"{time_of_day}的城市沐浴在柔和的光线中，建筑物的轮廓清晰可见，街道上的人们悠闲地漫步，享受着这美好的时刻。"

    elif scene_type == "自然":
        if weather == "晴朗":
            base_desc = f"这是一幅壮丽的自然风光，蓝天白云下，绿树成荫，山峦起伏。清澈的水面反射着周围的景色，形成了一幅和谐的自然画卷。"
        elif weather == "雨天":
            base_desc = f"雨水轻轻地洒落在大自然中，给一切都蒙上了一层朦胧的面纱。雨滴打在叶子上，发出沙沙的声音，空气中弥漫着泥土的芬芳。"
        else:
            base_desc = f"{weather}的自然景观展现出独特的魅力，光线透过云层，在地面上形成斑驳的光影，增添了几分神秘感。"

    elif scene_type == "历史":
        base_desc = f"这是一个充满历史感的场景，古老的建筑和遗迹诉说着过去的故事。石块和雕刻上的纹理展示了时间的痕迹，让人仿佛穿越到了过去的时光。"

    elif scene_type == "度假":
        base_desc = f"这是一个完美的度假胜地，碧海蓝天，金色的沙滩延伸至远方。椰子树随风摇曳，海浪轻轻拍打着岸边，营造出一种放松和宁静的氛围。"

    else:  # 默认旅游场景
        base_desc = f"这是一个迷人的旅游场景，融合了自然美景和人文风情。远处的风景如画，近处的细节生动有趣，是一个值得探索的地方。"

    # 添加特定的提示词元素
    specific_elements = ""
    for word in keywords:
        if word not in ["城市", "自然", "历史", "度假", "白天", "夜晚", "清晨", "傍晚", "晴朗", "雨天", "雪天", "雾天", "多云"]:
            specific_elements += f"场景中可以看到{word}，"

    if specific_elements:
        specific_elements = "在这个场景中，" + specific_elements[:-1] + "。"

    # 组合最终描述
    colors_and_mood = ""
    if time_of_day == "白天":
        colors_and_mood = "场景色彩明亮，充满活力，给人一种积极向上的感觉。"
    elif time_of_day == "夜晚":
        colors_and_mood = "场景色彩深沉，蓝色和紫色调为主，星空点缀其中，营造出神秘而浪漫的氛围。"
    elif time_of_day == "清晨":
        colors_and_mood = "场景沐浴在金色和橙色的晨光中，充满希望和新生的气息，给人一种宁静而温暖的感觉。"
    elif time_of_day == "傍晚":
        colors_and_mood = "场景笼罩在金色和橙红色的夕阳光芒中，色彩温暖而丰富，营造出一种温馨而略带感伤的氛围。"

    final_description = f"{base_desc} {specific_elements} {colors_and_mood} 整个场景构图平衡，视角恰到好处，细节丰富，光影效果自然，是一幅令人心旷神怡的{scene_type}画面。"

    return final_description



@ai_bp.route('/generate_plan_by_name', methods=['POST'])
def generate_travel_plan_by_name():
    """
    根据地点名称生成旅行计划
    支持手动输入地点名称，提供模糊查询
    """
    try:
        print("=== AI根据地点名称生成旅游计划接口被调用 ===")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        if not data:
            print("错误: 没有提供数据")
            return error('No data provided')

        location_names = data.get('location_names', [])
        days = data.get('days', 1)
        preferences = data.get('preferences', '')

        print(f"解析后的参数: location_names={location_names}, days={days}, preferences={preferences}")

        if not location_names:
            print("错误: 没有提供地点名称")
            return error('No location names provided')

        if not isinstance(location_names, list):
            print("错误: 地点名称必须是列表")
            return error('Location names must be a list')

        if not isinstance(days, int) or days < 1:
            print("错误: 天数必须是正整数")
            return error('Days must be a positive integer')

        # 根据地点名称查找地点
        print(f"开始查询地点信息...")
        location_objects = []
        for location_name in location_names:
            print(f"查询地点名称: {location_name}")
            # 模糊查询地点
            locations = Location.query.filter(
                or_(
                    Location.name.like(f'%{location_name}%'),
                    Location.keyword.like(f'%{location_name}%')
                )
            ).all()

            if locations:
                # 选择最匹配的地点（按人气排序）
                best_match = max(locations, key=lambda x: x.popularity or 0)
                print(f"找到地点: {best_match.name}")
                location_objects.append(best_match)
            else:
                print(f"未找到地点: {location_name}")

        print(f"有效地点数量: {len(location_objects)}")
        if not location_objects:
            print("错误: 没有找到有效地点")
            return error('No valid locations found')

        # 调用现有的生成逻辑
        from services.ai_service import AIService

        # 准备地点信息
        location_info = []
        for loc in location_objects:
            loc_type = "未知"
            if loc.type == 0:
                loc_type = "教育"
            elif loc.type == 1:
                loc_type = "文化"
            elif loc.type == 2:
                loc_type = "自然"
            elif loc.type == 3:
                loc_type = "娱乐"

            location_info.append({
                "id": loc.location_id,
                "name": loc.name,
                "type": loc_type,
                "keywords": loc.keyword if loc.keyword else ""
            })

        # 构建提示词
        prompt = f"""
        请为一个{days}天的旅行生成详细的旅行计划。旅行将包括以下地点：

        {json.dumps(location_info, ensure_ascii=False, indent=2)}

        用户偏好: {preferences}

        请根据地点类型和特点，合理安排每天的行程，包括：
        1. 每天游览的地点（平均分配所有地点，最后一天可以安排剩余地点）
        2. 每个地点的游览时间（上午、下午或晚上）
        3. 每个地点的详细游览建议和活动推荐
        4. 考虑地点之间的合理顺序

        请以JSON格式返回，格式如下：
        {{
            "title": "旅行计划标题",
            "days": [
                {{
                    "day": 1,
                    "locations": [
                        {{
                            "location_id": 地点ID,
                            "name": "地点名称",
                            "description": "详细的游览建议",
                            "time": "游览时间段"
                        }}
                    ]
                }}
            ]
        }}

        只返回JSON格式，不要有其他说明文字。
        """

        try:
            # 调用AI生成
            print("开始调用AI服务...")
            ai_service = AIService()
            print("AI服务实例创建成功")

            ai_response = ai_service.generate_text(prompt, max_tokens=2000)
            print(f"AI服务响应: {ai_response[:200]}...")

            try:
                # 尝试解析JSON响应
                plan = json.loads(ai_response)
                print("AI响应JSON解析成功")

                # 检查是否包含错误信息
                if "error" in plan:
                    print(f"AI服务返回错误: {plan['error']}")
                    print("使用本地生成方案...")
                    return generate_fallback_plan(location_objects, days, preferences)

                print("AI生成成功，返回结果")
                return success({'plan': plan}, 'Travel plan generated successfully')
            except json.JSONDecodeError:
                # 如果AI返回的不是有效JSON，尝试提取JSON部分
                import re
                json_match = re.search(r'({[\s\S]*})', ai_response)
                if json_match:
                    try:
                        json_str = json_match.group(1)
                        plan = json.loads(json_str)
                        return success({'plan': plan}, 'Travel plan generated successfully')
                    except:
                        pass

                # 如果仍然无法解析，回退到传统方法
                return generate_fallback_plan(location_objects, days, preferences)
        except Exception as e:
            # 如果AI服务调用失败，回退到传统方法
            print(f"AI服务调用失败: {str(e)}")
            import traceback
            traceback.print_exc()
            print("使用本地生成方案...")
            return generate_fallback_plan(location_objects, days, preferences)

    except Exception as e:
        print(f"生成旅游计划时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating travel plan: {str(e)}')

@ai_bp.route('/generate_food_recommendation', methods=['POST'])
def generate_food_recommendation():
    """
    智能美食推荐
    根据用户喜欢的菜系推荐餐厅并生成AI描述
    """
    try:
        print("=== AI智能美食推荐接口被调用 ===")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        if not data:
            print("错误: 没有提供数据")
            return error('No data provided')

        cuisine_types = data.get('cuisine_types', [])
        location_preference = data.get('location_preference', '')
        budget_range = data.get('budget_range', '中等')
        limit = data.get('limit', 5)

        print(f"解析后的参数: cuisine_types={cuisine_types}, location_preference={location_preference}, budget_range={budget_range}")

        if not cuisine_types:
            print("错误: 没有提供菜系类型")
            return error('No cuisine types provided')

        if not isinstance(cuisine_types, list):
            print("错误: 菜系类型必须是列表")
            return error('Cuisine types must be a list')

        # 根据菜系类型查找餐厅
        print(f"开始查询餐厅信息...")
        restaurants = []
        for cuisine_type in cuisine_types:
            print(f"查询菜系: {cuisine_type}")
            # 模糊查询餐厅
            found_restaurants = Restaurant.query.filter(
                Restaurant.cuisine_type.like(f'%{cuisine_type}%')
            ).order_by(Restaurant.evaluation.desc()).limit(limit).all()

            restaurants.extend(found_restaurants)

        # 去重并按评分排序
        unique_restaurants = list({r.id: r for r in restaurants}.values())
        unique_restaurants.sort(key=lambda x: x.evaluation or 0, reverse=True)
        unique_restaurants = unique_restaurants[:limit]

        print(f"找到餐厅数量: {len(unique_restaurants)}")
        if not unique_restaurants:
            print("错误: 没有找到相关餐厅")
            return error('No restaurants found for the specified cuisine types')

        # 准备餐厅信息
        restaurant_info = []
        for restaurant in unique_restaurants:
            dishes = []
            if restaurant.dishes_name_price:
                dishes.append(restaurant.parse_dish(restaurant.dishes_name_price, restaurant.dishes_image))
            if restaurant.dishes_name1_price:
                dishes.append(restaurant.parse_dish(restaurant.dishes_name1_price, restaurant.dishes_image1))
            if restaurant.dishes_name2_price:
                dishes.append(restaurant.parse_dish(restaurant.dishes_name2_price, restaurant.dishes_image2))

            # 过滤None值
            dishes = [dish for dish in dishes if dish is not None]

            restaurant_info.append({
                "id": restaurant.id,
                "name": restaurant.name,
                "cuisine_type": restaurant.cuisine_type,
                "evaluation": restaurant.evaluation,
                "average_price": restaurant.average_price_perperson,
                "dishes": dishes
            })

        # 构建AI提示词
        prompt = f"""
请为以下餐厅生成智能美食推荐，包括餐厅描述和菜系总结：

用户偏好菜系：{', '.join(cuisine_types)}
预算范围：{budget_range}
地点偏好：{location_preference if location_preference else '无特定要求'}

餐厅信息：
{json.dumps(restaurant_info, ensure_ascii=False, indent=2)}

请生成以下内容：
1. 每个餐厅的详细描述，包括特色菜品、环境、服务等
2. 对用户偏爱菜系的总结和特点分析
3. 推荐理由和用餐建议

请以JSON格式返回，格式如下：
{{
    "cuisine_summary": {{
        "preferred_cuisines": ["菜系1", "菜系2"],
        "characteristics": "菜系特点总结",
        "flavor_profile": "口味特色描述"
    }},
    "restaurant_recommendations": [
        {{
            "restaurant_id": 餐厅ID,
            "name": "餐厅名称",
            "ai_description": "AI生成的餐厅描述",
            "signature_dishes": ["招牌菜1", "招牌菜2"],
            "recommendation_reason": "推荐理由",
            "dining_tips": "用餐建议"
        }}
    ],
    "overall_recommendation": "整体推荐总结"
}}

只返回JSON格式，不要有其他说明文字。
"""

        try:
            # 调用AI生成
            print("开始调用AI服务...")
            from services.ai_service import AIService
            ai_service = AIService()
            print("AI服务实例创建成功")

            ai_response = ai_service.generate_text(prompt, max_tokens=2500)
            print(f"AI服务响应: {ai_response[:200]}...")

            try:
                # 尝试解析JSON响应
                recommendation = json.loads(ai_response)
                print("AI响应JSON解析成功")

                # 检查是否包含错误信息
                if "error" in recommendation:
                    print(f"AI服务返回错误: {recommendation['error']}")
                    print("使用本地生成方案...")
                    return generate_fallback_food_recommendation(unique_restaurants, cuisine_types, budget_range)

                # 添加餐厅基本信息
                for i, restaurant_rec in enumerate(recommendation.get('restaurant_recommendations', [])):
                    if i < len(unique_restaurants):
                        restaurant = unique_restaurants[i]
                        restaurant_rec['cuisine_type'] = restaurant.cuisine_type
                        restaurant_rec['evaluation'] = restaurant.evaluation
                        restaurant_rec['average_price'] = restaurant.average_price_perperson
                        restaurant_rec['image_url'] = restaurant.image_url

                print("美食推荐生成成功，返回结果")
                return success({'recommendation': recommendation}, 'Food recommendation generated successfully')

            except json.JSONDecodeError:
                # 如果AI返回的不是有效JSON，尝试提取JSON部分
                import re
                json_match = re.search(r'({[\s\S]*})', ai_response)
                if json_match:
                    try:
                        json_str = json_match.group(1)
                        recommendation = json.loads(json_str)
                        return success({'recommendation': recommendation}, 'Food recommendation generated successfully')
                    except:
                        pass

                # 如果仍然无法解析，回退到本地方法
                print("AI返回的不是有效JSON，回退到本地方法")
                return generate_fallback_food_recommendation(unique_restaurants, cuisine_types, budget_range)

        except Exception as e:
            # 如果AI服务调用失败，回退到本地方法
            print(f"AI服务调用失败: {str(e)}")
            import traceback
            traceback.print_exc()
            print("使用本地生成方案...")
            return generate_fallback_food_recommendation(unique_restaurants, cuisine_types, budget_range)

    except Exception as e:
        print(f"生成美食推荐时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating food recommendation: {str(e)}')

def generate_fallback_food_recommendation(restaurants, cuisine_types, budget_range):
    """
    本地美食推荐生成方案（作为AI服务失败时的回退方案）
    """
    try:
        # 生成菜系总结
        cuisine_summary = {
            "preferred_cuisines": cuisine_types,
            "characteristics": f"您偏爱的{', '.join(cuisine_types)}菜系具有独特的风味特色",
            "flavor_profile": "根据您的选择，为您推荐以下优质餐厅"
        }

        # 生成餐厅推荐
        restaurant_recommendations = []
        for restaurant in restaurants:
            # 获取菜品信息
            dishes = []
            if restaurant.dishes_name_price:
                dish = restaurant.parse_dish(restaurant.dishes_name_price, restaurant.dishes_image)
                if dish:
                    dishes.append(dish['name'])
            if restaurant.dishes_name1_price:
                dish = restaurant.parse_dish(restaurant.dishes_name1_price, restaurant.dishes_image1)
                if dish:
                    dishes.append(dish['name'])

            # 生成描述
            description = f"{restaurant.name}是一家优质的{restaurant.cuisine_type}餐厅"
            if restaurant.evaluation:
                description += f"，评分{restaurant.evaluation}分"
            if restaurant.average_price_perperson:
                description += f"，人均消费约{restaurant.average_price_perperson}元"
            description += "。"

            recommendation_reason = f"推荐理由：{restaurant.cuisine_type}正宗，口味地道"
            if restaurant.evaluation and restaurant.evaluation >= 4.0:
                recommendation_reason += "，顾客评价很高"

            dining_tips = "建议提前预订，用餐高峰期可能需要等位。"

            restaurant_recommendations.append({
                "restaurant_id": restaurant.id,
                "name": restaurant.name,
                "ai_description": description,
                "signature_dishes": dishes[:2] if dishes else ["招牌菜"],
                "recommendation_reason": recommendation_reason,
                "dining_tips": dining_tips,
                "cuisine_type": restaurant.cuisine_type,
                "evaluation": restaurant.evaluation,
                "average_price": restaurant.average_price_perperson,
                "image_url": restaurant.image_url
            })

        overall_recommendation = f"根据您对{', '.join(cuisine_types)}的偏好，为您推荐了{len(restaurants)}家优质餐厅。"

        recommendation = {
            "cuisine_summary": cuisine_summary,
            "restaurant_recommendations": restaurant_recommendations,
            "overall_recommendation": overall_recommendation
        }

        return success({
            'recommendation': recommendation,
            'note': 'Generated using fallback method due to AI service error'
        }, 'Food recommendation generated successfully (fallback method)')

    except Exception as e:
        print(f"本地美食推荐生成失败: {str(e)}")
        return error(f'Error generating fallback food recommendation: {str(e)}')


@ai_bp.route('/generate_article_summary', methods=['POST'])
def generate_article_summary():
    """
    生成文章摘要
    根据用户已发布的日记生成摘要
    """
    try:
        print("=== AI文章摘要生成接口被调用 ===")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        if not data:
            print("错误: 没有提供数据")
            return error('No data provided')

        article_id = data.get('article_id')
        max_length = data.get('max_length', 200)
        style = data.get('style', '简洁')

        print(f"解析后的参数: article_id={article_id}, max_length={max_length}, style={style}")

        if not article_id:
            print("错误: 没有提供文章ID")
            return error('No article_id provided')

        # 验证文章是否存在
        article = Article.query.get(article_id)
        if not article:
            print(f"错误: 文章不存在，ID: {article_id}")
            return error('Article not found')

        print(f"找到文章: {article.title}")

        # 构建提示词
        style_prompts = {
            '简洁': '请用简洁明了的语言',
            '详细': '请用详细丰富的描述',
            '情感': '请用情感丰富的语言',
            '客观': '请用客观中性的语言'
        }

        style_prompt = style_prompts.get(style, '请用简洁明了的语言')

        prompt = f"""
        {style_prompt}为以下旅游日记生成一个{max_length}字以内的摘要：

        标题：{article.title}
        地点：{article.location or article.location_name or '未知地点'}
        内容：{article.content}

        要求：
        1. 摘要长度控制在{max_length}字以内
        2. 突出旅游的亮点和感受
        3. 保持{style}的风格
        4. 只返回摘要内容，不要其他说明

        摘要：
        """

        try:
            # 调用AI生成
            print("开始调用AI服务...")
            ai_service = AIService()
            print("AI服务实例创建成功")

            ai_response = ai_service.generate_text(prompt, max_tokens=max_length + 50)
            print(f"AI服务响应: {ai_response[:100]}...")

            # 清理响应内容
            summary = ai_response.strip()

            # 如果摘要超过指定长度，进行截断
            if len(summary) > max_length:
                summary = summary[:max_length-3] + '...'

            print("AI生成成功，返回结果")
            return success({
                'summary': summary,
                'original_length': len(article.content) if article.content else 0,
                'summary_length': len(summary),
                'style': style,
                'article_title': article.title
            }, 'Article summary generated successfully')

        except Exception as ai_error:
            print(f"AI生成失败，使用备用方案: {str(ai_error)}")
            # 备用方案：简单截取文章开头
            content = article.content or ''
            if len(content) > max_length:
                summary = content[:max_length-3] + '...'
            else:
                summary = content

            return success({
                'summary': summary,
                'original_length': len(content),
                'summary_length': len(summary),
                'style': style,
                'article_title': article.title,
                'note': '使用备用摘要方案'
            }, 'Article summary generated with fallback method')

    except Exception as e:
        print(f"生成文章摘要时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating article summary: {str(e)}')


@ai_bp.route('/generate_location_description_by_name', methods=['POST'])
def generate_location_description_by_name():
    """
    根据地点名称生成地点描述
    支持手动输入地点名称
    """
    try:
        print("=== AI根据地点名称生成描述接口被调用 ===")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        if not data:
            print("错误: 没有提供数据")
            return error('No data provided')

        location_name = data.get('location_name', '')
        style = data.get('style', '详细')
        focus = data.get('focus', '')
        length = data.get('length', 500)

        print(f"解析后的参数: location_name={location_name}, style={style}, focus={focus}, length={length}")

        if not location_name or not location_name.strip():
            print("错误: 没有提供地点名称")
            return error('No location name provided')

        location_name = location_name.strip()

        # 构建提示词
        style_prompts = {
            '详细': '请提供详细全面的介绍',
            '简洁': '请提供简洁明了的概述',
            '文艺': '请用文艺优美的语言描述',
            '实用': '请提供实用的游览指南'
        }

        style_prompt = style_prompts.get(style, '请提供详细全面的介绍')
        focus_text = f"，重点关注{focus}" if focus else ""

        prompt = f"""
        {style_prompt}"{location_name}"这个地点{focus_text}。

        请生成一个约{length}字的地点描述，包含以下方面（如果适用）：
        1. 地点的基本信息和特色
        2. 历史背景和文化意义
        3. 主要景点和看点
        4. 游览建议和最佳时间
        5. 交通和门票信息
        6. 当地特色和美食

        要求：
        1. 描述长度约{length}字
        2. 语言风格：{style}
        3. 内容准确、有用
        4. 只返回描述内容，不要其他说明

        描述：
        """

        try:
            # 调用AI生成
            print("开始调用AI服务...")
            ai_service = AIService()
            print("AI服务实例创建成功")

            ai_response = ai_service.generate_text(prompt, max_tokens=length + 100)
            print(f"AI服务响应: {ai_response[:100]}...")

            # 清理响应内容
            description = ai_response.strip()

            print("AI生成成功，返回结果")
            return success({
                'description': description,
                'location_name': location_name,
                'style': style,
                'focus': focus,
                'length': len(description)
            }, 'Location description generated successfully')

        except Exception as ai_error:
            print(f"AI生成失败，使用备用方案: {str(ai_error)}")
            # 备用方案：生成基础描述
            description = f"{location_name}是一个值得游览的地点。这里有着独特的风景和文化特色，为游客提供了丰富的旅游体验。建议游客提前了解相关信息，合理安排行程，以获得最佳的游览效果。"

            return success({
                'description': description,
                'location_name': location_name,
                'style': style,
                'focus': focus,
                'length': len(description),
                'note': '使用备用描述方案'
            }, 'Location description generated with fallback method')

    except Exception as e:
        print(f"生成地点描述时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating location description: {str(e)}')


@ai_bp.route('/generate_image', methods=['POST'])
def generate_image():
    """
    AI图片生成
    基于腾讯混元大模型生成图片
    """
    try:
        print("=== AI图片生成接口被调用 ===")
        data = request.get_json()
        print(f"接收到的数据: {data}")

        if not data:
            print("错误: 没有提供数据")
            return error('No data provided')

        prompt = data.get('prompt', '')
        size = data.get('size', '1024x1024')
        style = data.get('style', 'realistic')
        count = data.get('count', 1)

        print(f"解析后的参数: prompt={prompt}, size={size}, style={style}, count={count}")

        if not prompt or not prompt.strip():
            print("错误: 没有提供图片描述")
            return error('No prompt provided')

        if count < 1 or count > 4:
            print("错误: 生成数量必须在1-4之间")
            return error('Count must be between 1 and 4')

        # 验证图片尺寸
        valid_sizes = ['1024x1024', '1280x720', '720x1280', '1920x1080']
        if size not in valid_sizes:
            print(f"错误: 无效的图片尺寸: {size}")
            return error(f'Invalid size. Valid sizes: {", ".join(valid_sizes)}')

        # 验证图片风格
        valid_styles = ['realistic', 'artistic', 'anime', 'oil_painting', 'watercolor', 'sketch']
        if style not in valid_styles:
            print(f"错误: 无效的图片风格: {style}")
            return error(f'Invalid style. Valid styles: {", ".join(valid_styles)}')

        try:
            # 优先尝试火山方舟API
            if AI_SERVICES_AVAILABLE and AIConfig and AIConfig.is_doubao_configured():
                print("开始调用火山方舟豆包API...")
                doubao_service = create_doubao_service()
                images = doubao_service.generate_image(prompt, size, style, count)

                if images:
                    print(f"火山方舟API图片生成成功，共生成{len(images)}张图片")
                    return success({
                        'images': images,
                        'prompt': prompt,
                        'size': size,
                        'style': style,
                        'count': len(images),
                        'provider': 'doubao'
                    }, 'Images generated successfully with Doubao API')

            # 如果火山方舟不可用，尝试腾讯混元API
            print("开始调用腾讯混元生图API...")
            images = generate_images_with_hunyuan(prompt, size, style, count)

            if images:
                print(f"图片生成成功，共生成{len(images)}张图片")
                return success({
                    'images': images,
                    'prompt': prompt,
                    'size': size,
                    'style': style,
                    'count': len(images),
                    'provider': 'hunyuan'
                }, 'Images generated successfully')
            else:
                print("图片生成失败")
                return error('Failed to generate images')

        except Exception as e:
            # 如果所有API调用失败，使用备用方案
            print(f"AI图片生成API调用失败，使用备用方案: {str(e)}")
            return generate_fallback_images(prompt, size, style, count)

    except Exception as e:
        print(f"生成图片时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error generating images: {str(e)}')


def generate_images_with_hunyuan(prompt, size, style, count):
    """
    调用腾讯混元生图API生成图片

    Args:
        prompt: 图片描述
        size: 图片尺寸
        style: 图片风格
        count: 生成数量

    Returns:
        生成的图片URL列表
    """
    try:
        import requests
        import time

        # 腾讯混元生图API配置
        # 注意：这里需要配置实际的API密钥和端点
        api_url = "https://image.hunyuan.tencent.com/api/generate"

        # 构建请求参数
        # 根据风格调整提示词
        style_prompts = {
            'realistic': f"{prompt}, photorealistic, high quality, detailed",
            'artistic': f"{prompt}, artistic style, creative, beautiful",
            'anime': f"{prompt}, anime style, manga, colorful",
            'oil_painting': f"{prompt}, oil painting style, classical art",
            'watercolor': f"{prompt}, watercolor painting, soft colors",
            'sketch': f"{prompt}, pencil sketch, black and white, artistic"
        }

        enhanced_prompt = style_prompts.get(style, prompt)

        # 解析尺寸
        width, height = map(int, size.split('x'))

        # 构建请求数据
        request_data = {
            "prompt": enhanced_prompt,
            "width": width,
            "height": height,
            "num_images": count,
            "style": style
        }

        print(f"发送到腾讯混元API的请求: {request_data}")

        # 这里应该是实际的API调用
        # 由于没有真实的API密钥，我们使用模拟数据
        # 在实际部署时，需要替换为真实的API调用

        # 模拟API响应
        images = []
        for i in range(count):
            # 生成模拟图片URL
            # 在实际实现中，这些应该是腾讯混元API返回的真实图片URL
            image_url = f"https://picsum.photos/{width}/{height}?random={int(time.time())}{i}"
            images.append({
                'url': image_url,
                'width': width,
                'height': height,
                'style': style
            })

        return images

    except Exception as e:
        print(f"腾讯混元API调用失败: {str(e)}")
        raise e


def generate_fallback_images(prompt, size, style, count):
    """
    备用图片生成方案（当腾讯混元API调用失败时）

    Args:
        prompt: 图片描述
        size: 图片尺寸
        style: 图片风格
        count: 生成数量

    Returns:
        API响应
    """
    try:
        import time

        # 解析尺寸
        width, height = map(int, size.split('x'))

        # 生成备用图片（使用占位图片服务）
        images = []
        for i in range(count):
            # 使用不同的占位图片服务
            services = [
                f"https://picsum.photos/{width}/{height}?random={int(time.time())}{i}",
                f"https://source.unsplash.com/{width}x{height}/?nature,travel&sig={int(time.time())}{i}",
                f"https://loremflickr.com/{width}/{height}/landscape,nature?random={int(time.time())}{i}"
            ]

            image_url = services[i % len(services)]
            images.append({
                'url': image_url,
                'width': width,
                'height': height,
                'style': style,
                'fallback': True
            })

        return success({
            'images': images,
            'prompt': prompt,
            'size': size,
            'style': style,
            'count': len(images),
            'note': 'Generated using fallback method due to API service error'
        }, 'Images generated successfully (fallback method)')

    except Exception as e:
        print(f"备用图片生成失败: {str(e)}")
        return error(f'Error generating fallback images: {str(e)}')


