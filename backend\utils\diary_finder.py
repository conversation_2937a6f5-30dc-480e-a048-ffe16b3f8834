"""
游记查找工具类

该模块实现了各种游记查找算法，包括：
1. 二分查找
2. 哈希查找
3. Boyer<PERSON>Moore文本搜索
"""

from typing import List, Optional, Tuple
from models.article import Article
import re

class DiaryFinder:
    """游记查找工具类"""
    
    @staticmethod
    def binary_search_by_title(diaries: List[Article], title: str) -> Optional[Article]:
        """
        使用二分查找算法按标题精确查找游记
        
        Args:
            diaries: 已按标题排序的游记列表
            title: 要查找的标题
            
        Returns:
            找到的游记，未找到则返回None
        """
        if not diaries or not title:
            return None
            
        # 二分查找
        left, right = 0, len(diaries) - 1
        
        while left <= right:
            mid = (left + right) // 2
            mid_title = diaries[mid].title
            
            if mid_title == title:
                return diaries[mid]
            elif mid_title < title:
                left = mid + 1
            else:
                right = mid - 1
                
        return None
    
    @staticmethod
    def hash_search_by_title(diaries: List[Article], title: str) -> Optional[Article]:
        """
        使用哈希表进行O(1)复杂度的标题精确查找
        
        Args:
            diaries: 游记列表
            title: 要查找的标题
            
        Returns:
            找到的游记，未找到则返回None
        """
        # 构建哈希表
        title_map = {diary.title: diary for diary in diaries}
        
        # O(1)查找
        return title_map.get(title)
    
    @staticmethod
    def boyer_moore_search(text: str, pattern: str) -> bool:
        """
        使用Boyer-Moore算法进行字符串匹配
        
        Args:
            text: 待搜索的文本
            pattern: 搜索模式
            
        Returns:
            是否匹配成功
        """
        if not pattern:
            return True
            
        if not text:
            return False
            
        # 将文本和模式转换为小写，用于不区分大小写的匹配
        text = text.lower()
        pattern = pattern.lower()
        
        n, m = len(text), len(pattern)
        
        if m > n:
            return False
            
        # 构建坏字符规则
        bad_char = {}
        for i in range(m - 1):
            bad_char[pattern[i]] = m - 1 - i
            
        # 模式串中最后一个字符的偏移量
        skip = bad_char.get(pattern[m - 1], m)
        bad_char[pattern[m - 1]] = skip
        
        # 搜索
        i = m - 1
        while i < n:
            j = m - 1
            k = i
            while j >= 0 and text[k] == pattern[j]:
                j -= 1
                k -= 1
                
            if j == -1:
                return True
                
            i += bad_char.get(text[i], m)
            
        return False
    
    @staticmethod
    def full_text_search(diaries: List[Article], keyword: str) -> List[Tuple[Article, float]]:
        """
        全文搜索，返回游记及其相关度得分
        
        Args:
            diaries: 游记列表
            keyword: 查询关键字
            
        Returns:
            匹配的游记列表及其相关度得分
        """
        if not keyword:
            return [(diary, 1.0) for diary in diaries]
            
        # 将关键字转换为小写，用于不区分大小写的匹配
        keyword_lower = keyword.lower()
        
        # 分词
        keywords = re.findall(r'\w+', keyword_lower)
        
        results = []
        for diary in diaries:
            # 获取游记内容（假设已解压）
            content = diary.content
            if isinstance(content, bytes) and hasattr(diary, 'huffman_codes') and diary.huffman_codes:
                try:
                    from services.article_service import ArticleService
                    import json
                    service = ArticleService()
                    huffman_codes = json.loads(diary.huffman_codes)
                    content = service.decompress_text(content, huffman_codes)
                except Exception:
                    content = ""
            
            # 如果内容仍然是bytes类型，尝试解码
            if isinstance(content, bytes):
                try:
                    content = content.decode('utf-8')
                except Exception:
                    content = ""
            
            # 计算相关度得分
            score = 0.0
            
            # 标题匹配权重更高
            title_lower = diary.title.lower() if diary.title else ""
            for word in keywords:
                if word in title_lower:
                    score += 0.6  # 标题匹配权重
            
            # 内容匹配
            content_lower = content.lower() if content else ""
            for word in keywords:
                if word in content_lower:
                    score += 0.3  # 内容匹配权重
                    
                    # 计算出现次数，增加权重
                    count = content_lower.count(word)
                    score += min(count / 10, 0.3)  # 最多增加0.3的权重
            
            # 只返回相关度大于0的结果
            if score > 0:
                results.append((diary, score))
        
        # 按相关度降序排序
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results
