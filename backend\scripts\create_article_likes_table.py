"""
创建文章点赞表的脚本
"""
import os
import sys
import pymysql
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('FLASK_DB_HOST', 'localhost'),
    'user': os.getenv('FLASK_DB_USER', 'root'),
    'password': os.getenv('FLASK_DB_PASSWORD', '123456'),
    'db': os.getenv('FLASK_DB_NAME', 'study_tour_system'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def create_article_likes_table():
    """创建文章点赞表"""
    # 连接到数据库
    conn = pymysql.connect(**DB_CONFIG)
    
    try:
        with conn.cursor() as cursor:
            # 创建文章点赞表
            sql = """
            CREATE TABLE IF NOT EXISTS article_likes (
                user_id INT NOT NULL,
                article_id INT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (user_id, article_id),
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                FOREIGN KEY (article_id) REFERENCES articles(article_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
            """
            cursor.execute(sql)
            
            # 提交更改
            conn.commit()
            print("文章点赞表创建成功！")
            
    except Exception as e:
        print(f"创建文章点赞表时出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    create_article_likes_table()
