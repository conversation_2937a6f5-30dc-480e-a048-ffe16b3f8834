"""
协同过滤测试数据生成脚本
用于生成测试用户、文章、浏览记录等数据，以便测试协同过滤推荐功能
"""
import os
import sys
import random
from datetime import datetime, timedelta
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入Flask应用和模型
from app import create_app
from models.user import User
from models.location import Location, LocationBrowseCount
from models.location_browse import LocationBrowseHistory
from models.article import Article, ArticleScore
from utils.database import db
from services.article_service import ArticleService
import json

# 根据User模型，密码没有使用哈希，直接存储明文密码
# 这在实际应用中是不安全的，但这里遵循现有模型的实现

def generate_users(num_users=10, start_id=1):
    """生成测试用户"""
    print(f"正在生成{num_users}个测试用户...")

    # 检查是否已有用户
    existing_users = User.query.count()
    if existing_users > 0:
        print(f"数据库中已有{existing_users}个用户")

    users = []
    for i in range(start_id, start_id + num_users):
        # 检查用户是否已存在
        existing_user = User.query.filter_by(username=f"user_{i}").first()
        if existing_user:
            print(f"用户 user_{i} 已存在，跳过")
            users.append(existing_user)
            continue

        # 创建新用户
        user = User(
            username=f"user_{i}",
            email=f"user{i}@example.com",
            password="123456",  # 所有用户密码都是123456（明文存储）
            avatar="default_avatar.jpg"
        )
        db.session.add(user)
        users.append(user)

    db.session.commit()
    print(f"成功生成{len(users)}个测试用户")
    return users

def generate_location_browse_history(users, locations=None, pattern_type='random'):
    """
    为用户生成地点浏览历史

    参数:
    - users: 用户列表
    - locations: 地点列表，如果为None则从数据库获取
    - pattern_type: 浏览模式类型
        - 'random': 完全随机浏览
        - 'clustered': 聚类浏览（用户倾向于浏览特定类型的地点）
        - 'similar_groups': 相似用户组（不同组的用户有不同的浏览偏好）
    """
    print(f"正在为用户生成地点浏览记录，模式: {pattern_type}...")

    # 获取所有地点
    if locations is None:
        locations = Location.query.all()

    if not locations:
        print("错误：数据库中没有地点数据，请先导入地点数据")
        return

    # 按类型分组地点
    locations_by_type = {}
    for location in locations:
        if location.type not in locations_by_type:
            locations_by_type[location.type] = []
        locations_by_type[location.type].append(location)

    # 按关键词分组地点
    locations_by_keyword = {}
    for location in locations:
        if location.keyword:
            keywords = [k.strip() for k in location.keyword.split(',')]
            for keyword in keywords:
                if keyword not in locations_by_keyword:
                    locations_by_keyword[keyword] = []
                locations_by_keyword[keyword].append(location)

    total_records = 0

    if pattern_type == 'random':
        # 完全随机浏览模式
        for user in users:
            # 随机选择地点数量（10-30之间）
            num_locations = random.randint(10, min(30, len(locations)))

            # 随机选择地点
            selected_locations = random.sample(locations, num_locations)

            # 为每个地点生成1-5条浏览记录
            for location in selected_locations:
                # 随机生成浏览次数
                browse_count = random.randint(1, 5)

                # 更新或创建LocationBrowseCount记录
                count_record = LocationBrowseCount.query.filter_by(
                    user_id=user.user_id,
                    location_id=location.location_id
                ).first()

                if count_record:
                    count_record.count += browse_count
                else:
                    count_record = LocationBrowseCount(
                        user_id=user.user_id,
                        location_id=location.location_id,
                        count=browse_count
                    )
                    db.session.add(count_record)

                # 生成浏览历史记录
                for _ in range(browse_count):
                    # 随机生成浏览时间（过去30天内）
                    browse_time = datetime.now() - timedelta(
                        days=random.randint(0, 30),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )

                    history = LocationBrowseHistory(
                        user_id=user.user_id,
                        location_id=location.location_id,
                        browse_time=browse_time
                    )
                    db.session.add(history)
                    total_records += 1

                # 更新地点人气
                location.popularity += browse_count

    elif pattern_type == 'clustered':
        # 聚类浏览模式 - 每个用户倾向于浏览特定类型或关键词的地点
        for user in users:
            # 为每个用户随机选择1-3个偏好类型
            if locations_by_type:
                preferred_types = random.sample(list(locations_by_type.keys()),
                                              min(random.randint(1, 3), len(locations_by_type)))
            else:
                preferred_types = []

            # 为每个用户随机选择2-5个偏好关键词
            if locations_by_keyword:
                preferred_keywords = random.sample(list(locations_by_keyword.keys()),
                                                 min(random.randint(2, 5), len(locations_by_keyword)))
            else:
                preferred_keywords = []

            # 收集符合偏好的地点
            preferred_locations = set()
            for type_id in preferred_types:
                preferred_locations.update(locations_by_type[type_id])

            for keyword in preferred_keywords:
                preferred_locations.update(locations_by_keyword[keyword])

            preferred_locations = list(preferred_locations)

            # 如果偏好地点不足，添加一些随机地点
            if len(preferred_locations) < 10:
                other_locations = [loc for loc in locations if loc not in preferred_locations]
                additional_count = min(10 - len(preferred_locations), len(other_locations))
                if additional_count > 0:
                    preferred_locations.extend(random.sample(other_locations, additional_count))

            # 为偏好地点生成更多浏览记录（3-8次）
            for location in preferred_locations:
                # 生成浏览次数
                browse_count = random.randint(3, 8)

                # 更新或创建LocationBrowseCount记录
                count_record = LocationBrowseCount.query.filter_by(
                    user_id=user.user_id,
                    location_id=location.location_id
                ).first()

                if count_record:
                    count_record.count += browse_count
                else:
                    count_record = LocationBrowseCount(
                        user_id=user.user_id,
                        location_id=location.location_id,
                        count=browse_count
                    )
                    db.session.add(count_record)

                # 生成浏览历史记录
                for _ in range(browse_count):
                    # 随机生成浏览时间（过去30天内）
                    browse_time = datetime.now() - timedelta(
                        days=random.randint(0, 30),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )

                    history = LocationBrowseHistory(
                        user_id=user.user_id,
                        location_id=location.location_id,
                        browse_time=browse_time
                    )
                    db.session.add(history)
                    total_records += 1

                # 更新地点人气
                location.popularity += browse_count

            # 为非偏好地点生成少量浏览记录（0-2次）
            non_preferred_locations = [loc for loc in locations if loc not in preferred_locations]
            # 随机选择5-15个非偏好地点
            selected_non_preferred = random.sample(
                non_preferred_locations,
                min(random.randint(5, 15), len(non_preferred_locations))
            )

            for location in selected_non_preferred:
                # 生成浏览次数
                browse_count = random.randint(1, 2)

                # 更新或创建LocationBrowseCount记录
                count_record = LocationBrowseCount.query.filter_by(
                    user_id=user.user_id,
                    location_id=location.location_id
                ).first()

                if count_record:
                    count_record.count += browse_count
                else:
                    count_record = LocationBrowseCount(
                        user_id=user.user_id,
                        location_id=location.location_id,
                        count=browse_count
                    )
                    db.session.add(count_record)

                # 生成浏览历史记录
                for _ in range(browse_count):
                    # 随机生成浏览时间（过去30天内）
                    browse_time = datetime.now() - timedelta(
                        days=random.randint(0, 30),
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )

                    history = LocationBrowseHistory(
                        user_id=user.user_id,
                        location_id=location.location_id,
                        browse_time=browse_time
                    )
                    db.session.add(history)
                    total_records += 1

                # 更新地点人气
                location.popularity += browse_count

    elif pattern_type == 'similar_groups':
        # 相似用户组模式 - 将用户分成几个组，每组有相似的浏览偏好
        num_groups = min(5, len(users) // 2)  # 最多5个组，每组至少2个用户
        if num_groups < 1:
            num_groups = 1

        # 随机分配用户到组
        random.shuffle(users)
        user_groups = [[] for _ in range(num_groups)]
        for i, user in enumerate(users):
            user_groups[i % num_groups].append(user)

        # 为每个组创建浏览偏好
        group_preferences = []
        for _ in range(num_groups):
            # 为每个组随机选择1-3个偏好类型
            if locations_by_type:
                preferred_types = random.sample(list(locations_by_type.keys()),
                                              min(random.randint(1, 3), len(locations_by_type)))
            else:
                preferred_types = []

            # 为每个组随机选择2-5个偏好关键词
            if locations_by_keyword:
                preferred_keywords = random.sample(list(locations_by_keyword.keys()),
                                                 min(random.randint(2, 5), len(locations_by_keyword)))
            else:
                preferred_keywords = []

            group_preferences.append((preferred_types, preferred_keywords))

        # 为每个组的用户生成浏览记录
        for group_idx, (users_in_group, (preferred_types, preferred_keywords)) in enumerate(zip(user_groups, group_preferences)):
            print(f"为用户组 {group_idx+1} 生成浏览记录 (用户数: {len(users_in_group)})")

            # 收集符合偏好的地点
            preferred_locations = set()
            for type_id in preferred_types:
                preferred_locations.update(locations_by_type[type_id])

            for keyword in preferred_keywords:
                preferred_locations.update(locations_by_keyword[keyword])

            preferred_locations = list(preferred_locations)

            # 如果偏好地点不足，添加一些随机地点
            if len(preferred_locations) < 10:
                other_locations = [loc for loc in locations if loc not in preferred_locations]
                additional_count = min(10 - len(preferred_locations), len(other_locations))
                if additional_count > 0:
                    preferred_locations.extend(random.sample(other_locations, additional_count))

            # 为该组的每个用户生成浏览记录
            for user in users_in_group:
                # 为偏好地点生成更多浏览记录（3-8次）
                for location in preferred_locations:
                    # 生成浏览次数
                    browse_count = random.randint(3, 8)

                    # 更新或创建LocationBrowseCount记录
                    count_record = LocationBrowseCount.query.filter_by(
                        user_id=user.user_id,
                        location_id=location.location_id
                    ).first()

                    if count_record:
                        count_record.count += browse_count
                    else:
                        count_record = LocationBrowseCount(
                            user_id=user.user_id,
                            location_id=location.location_id,
                            count=browse_count
                        )
                        db.session.add(count_record)

                    # 生成浏览历史记录
                    for _ in range(browse_count):
                        # 随机生成浏览时间（过去30天内）
                        browse_time = datetime.now() - timedelta(
                            days=random.randint(0, 30),
                            hours=random.randint(0, 23),
                            minutes=random.randint(0, 59)
                        )

                        history = LocationBrowseHistory(
                            user_id=user.user_id,
                            location_id=location.location_id,
                            browse_time=browse_time
                        )
                        db.session.add(history)
                        total_records += 1

                    # 更新地点人气
                    location.popularity += browse_count

                # 为非偏好地点生成少量浏览记录（0-2次）
                non_preferred_locations = [loc for loc in locations if loc not in preferred_locations]
                # 随机选择5-15个非偏好地点
                selected_non_preferred = random.sample(
                    non_preferred_locations,
                    min(random.randint(5, 15), len(non_preferred_locations))
                )

                for location in selected_non_preferred:
                    # 生成浏览次数
                    browse_count = random.randint(1, 2)

                    # 更新或创建LocationBrowseCount记录
                    count_record = LocationBrowseCount.query.filter_by(
                        user_id=user.user_id,
                        location_id=location.location_id
                    ).first()

                    if count_record:
                        count_record.count += browse_count
                    else:
                        count_record = LocationBrowseCount(
                            user_id=user.user_id,
                            location_id=location.location_id,
                            count=browse_count
                        )
                        db.session.add(count_record)

                    # 生成浏览历史记录
                    for _ in range(browse_count):
                        # 随机生成浏览时间（过去30天内）
                        browse_time = datetime.now() - timedelta(
                            days=random.randint(0, 30),
                            hours=random.randint(0, 23),
                            minutes=random.randint(0, 59)
                        )

                        history = LocationBrowseHistory(
                            user_id=user.user_id,
                            location_id=location.location_id,
                            browse_time=browse_time
                        )
                        db.session.add(history)
                        total_records += 1

                    # 更新地点人气
                    location.popularity += browse_count

    db.session.commit()
    print(f"成功生成{total_records}条地点浏览记录")
    return total_records

def generate_articles(users, locations=None, num_articles_per_user=3):
    """为用户生成文章"""
    print(f"正在为每个用户生成约{num_articles_per_user}篇文章...")

    # 获取所有地点
    if locations is None:
        locations = Location.query.all()

    if not locations:
        print("错误：数据库中没有地点数据，请先导入地点数据")
        return

    # 创建文章服务
    article_service = ArticleService()

    total_articles = 0

    # 为每个用户生成文章
    for user in users:
        # 随机选择地点数量（1-5之间）
        num_user_articles = random.randint(1, num_articles_per_user)

        # 随机选择地点
        selected_locations = random.sample(locations, min(num_user_articles, len(locations)))

        # 为每个地点生成一篇文章
        for location in selected_locations:
            # 生成文章标题
            title = f"{location.name}游记 - 由{user.username}撰写"

            # 生成文章内容
            content = f"""
            这是一篇关于{location.name}的游记。

            {location.name}是一个非常美丽的地方，位于中国的{random.choice(['北部', '南部', '东部', '西部', '中部'])}地区。

            我在{random.choice(['春天', '夏天', '秋天', '冬天'])}时去了那里，感觉非常{random.choice(['美妙', '惊喜', '放松', '愉快', '难忘'])}。

            这里的{random.choice(['风景', '建筑', '文化', '历史', '美食'])}非常有特色，给我留下了深刻的印象。

            推荐大家有机会也来{location.name}游玩！
            """

            # 压缩文章内容
            huffman_tree = article_service.build_huffman_tree(content)
            huffman_codes = article_service.generate_huffman_codes(huffman_tree)
            compressed_content = article_service.compress_text(content, huffman_codes)

            # 转换Huffman编码为JSON字符串
            huffman_codes_json = json.dumps(huffman_codes)

            # 创建文章
            article = Article(
                user_id=user.user_id,
                title=title,
                content=compressed_content,
                huffman_codes=huffman_codes_json,
                location_id=location.location_id,
                popularity=random.randint(0, 100),
                evaluation=random.randint(60, 100) / 10.0  # 6.0-10.0之间的评分
            )

            db.session.add(article)
            total_articles += 1

    db.session.commit()
    print(f"成功生成{total_articles}篇文章")
    return total_articles

def generate_article_scores(users, min_scores_per_user=5, pattern_type='random'):
    """
    为用户生成文章评分

    参数:
    - users: 用户列表
    - min_scores_per_user: 每个用户最少评分数量
    - pattern_type: 评分模式类型
        - 'random': 完全随机评分
        - 'similar_groups': 相似用户组（同组用户有相似的评分偏好）
    """
    print(f"正在为用户生成文章评分，模式: {pattern_type}...")

    # 获取所有文章
    articles = Article.query.all()
    if not articles:
        print("错误：数据库中没有文章数据，请先生成文章")
        return

    total_scores = 0

    if pattern_type == 'random':
        # 随机评分模式
        for user in users:
            # 随机选择文章数量（5-15之间，但不超过文章总数）
            num_articles = random.randint(min_scores_per_user, min(15, len(articles)))

            # 随机选择文章
            selected_articles = random.sample(articles, num_articles)

            # 为每篇文章生成评分
            for article in selected_articles:
                # 跳过用户自己的文章
                if article.user_id == user.user_id:
                    continue

                # 随机生成评分（1-5之间）
                score = random.randint(1, 5)

                # 创建或更新评分记录
                article_score = ArticleScore.query.filter_by(
                    user_id=user.user_id,
                    article_id=article.article_id
                ).first()

                if article_score:
                    article_score.score = score
                else:
                    article_score = ArticleScore(
                        user_id=user.user_id,
                        article_id=article.article_id,
                        score=score
                    )
                    db.session.add(article_score)
                    total_scores += 1

    elif pattern_type == 'similar_groups':
        # 相似用户组模式 - 将用户分成几个组，每组有相似的评分偏好
        num_groups = min(5, len(users) // 2)  # 最多5个组，每组至少2个用户
        if num_groups < 1:
            num_groups = 1

        # 随机分配用户到组
        random.shuffle(users)
        user_groups = [[] for _ in range(num_groups)]
        for i, user in enumerate(users):
            user_groups[i % num_groups].append(user)

        # 为每个组创建评分偏好
        for group_idx, users_in_group in enumerate(user_groups):
            print(f"为用户组 {group_idx+1} 生成评分 (用户数: {len(users_in_group)})")

            # 为该组选择一些"喜欢"的文章（评分4-5）
            liked_articles = random.sample(articles, min(len(articles) // 3, 10))

            # 为该组选择一些"不喜欢"的文章（评分1-2）
            disliked_articles = random.sample(
                [a for a in articles if a not in liked_articles],
                min(len(articles) // 3, 10)
            )

            # 其余文章为"中立"（评分3）
            neutral_articles = [a for a in articles if a not in liked_articles and a not in disliked_articles]

            # 为该组的每个用户生成评分
            for user in users_in_group:
                # 为"喜欢"的文章生成高评分
                for article in liked_articles:
                    # 跳过用户自己的文章
                    if article.user_id == user.user_id:
                        continue

                    # 生成高评分（4-5）
                    score = random.randint(4, 5)

                    # 创建或更新评分记录
                    article_score = ArticleScore.query.filter_by(
                        user_id=user.user_id,
                        article_id=article.article_id
                    ).first()

                    if article_score:
                        article_score.score = score
                    else:
                        article_score = ArticleScore(
                            user_id=user.user_id,
                            article_id=article.article_id,
                            score=score
                        )
                        db.session.add(article_score)
                        total_scores += 1

                # 为"不喜欢"的文章生成低评分
                for article in disliked_articles:
                    # 跳过用户自己的文章
                    if article.user_id == user.user_id:
                        continue

                    # 生成低评分（1-2）
                    score = random.randint(1, 2)

                    # 创建或更新评分记录
                    article_score = ArticleScore.query.filter_by(
                        user_id=user.user_id,
                        article_id=article.article_id
                    ).first()

                    if article_score:
                        article_score.score = score
                    else:
                        article_score = ArticleScore(
                            user_id=user.user_id,
                            article_id=article.article_id,
                            score=score
                        )
                        db.session.add(article_score)
                        total_scores += 1

                # 为一些"中立"的文章生成中等评分
                selected_neutral = random.sample(
                    neutral_articles,
                    min(random.randint(min_scores_per_user, 10), len(neutral_articles))
                )

                for article in selected_neutral:
                    # 跳过用户自己的文章
                    if article.user_id == user.user_id:
                        continue

                    # 生成中等评分（3）
                    score = 3

                    # 创建或更新评分记录
                    article_score = ArticleScore.query.filter_by(
                        user_id=user.user_id,
                        article_id=article.article_id
                    ).first()

                    if article_score:
                        article_score.score = score
                    else:
                        article_score = ArticleScore(
                            user_id=user.user_id,
                            article_id=article.article_id,
                            score=score
                        )
                        db.session.add(article_score)
                        total_scores += 1

    db.session.commit()
    print(f"成功生成{total_scores}条文章评分")
    return total_scores

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成协同过滤测试数据')
    parser.add_argument('--users', type=int, default=20, help='要生成的用户数量')
    parser.add_argument('--articles', type=int, default=3, help='每个用户生成的文章数量')
    parser.add_argument('--browse-pattern', choices=['random', 'clustered', 'similar_groups'],
                        default='similar_groups', help='浏览模式类型')
    parser.add_argument('--score-pattern', choices=['random', 'similar_groups'],
                        default='similar_groups', help='评分模式类型')
    parser.add_argument('--start-id', type=int, default=1, help='用户ID起始值')
    args = parser.parse_args()

    # 创建Flask应用上下文
    app = create_app()

    with app.app_context():
        # 获取所有地点
        locations = Location.query.all()
        if not locations:
            print("错误：数据库中没有地点数据，请先导入地点数据")
            return

        # 生成用户
        users = generate_users(args.users, args.start_id)

        # 生成地点浏览历史
        generate_location_browse_history(users, locations, args.browse_pattern)

        # 生成文章
        generate_articles(users, locations, args.articles)

        # 生成文章评分
        generate_article_scores(users, 5, args.score_pattern)

        print("测试数据生成完成！")

if __name__ == "__main__":
    main()
