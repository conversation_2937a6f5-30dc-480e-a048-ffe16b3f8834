{"name": "travel_system", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "core-js": "^3.8.3", "date-fns": "^4.1.0", "element-plus": "^2.9.7", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "marked": "^15.0.12", "vue": "^3.2.13", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.5.13", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "process": "^0.11.10", "resize-observer-polyfill": "^1.5.1", "typescript": "^5.8.3", "vue-loader": "^16.8.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}