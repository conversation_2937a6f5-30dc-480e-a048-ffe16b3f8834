<template>
  <div class="login-entry">
    <div class="login-container">
      <div class="login-header">
        <img src="@/assets/logo.png" alt="系统图标" class="login-logo">
        <h1 class="login-title">旅行助手</h1>
      </div>
      
      <div class="login-options">
        <el-button type="primary" class="action-btn login-btn" @click="showLoginDialog('login')">
          <el-icon><User /></el-icon>
          登录
        </el-button>
        
        <el-button type="success" class="action-btn register-btn" @click="showLoginDialog('register')">
          <el-icon><Plus /></el-icon>
          注册
        </el-button>
        
        <el-button class="action-btn guest-btn" @click="enterAsGuest">
          <el-icon><UserFilled /></el-icon>
          游客访问
        </el-button>
      </div>
    </div>
    <!-- 登录对话框组件 -->
    <LoginDialog ref="loginDialogRef" :initial-tab="activeTab" @login-success="handleLoginSuccess" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { User, Plus, UserFilled } from '@element-plus/icons-vue'
import LoginDialog from '@/components/LoginDialog.vue'

const router = useRouter()
const loginDialogRef = ref(null)
const activeTab = ref('login')
const userInfo = ref({
  username: '',
  email: '',
  avatar: null
})

const showLoginDialog = (tab) => {
  loginDialogRef.value?.show()
  loginDialogRef.value.activeTab = tab
}

const enterAsGuest = () => { 
  localStorage.clear()
  localStorage.setItem('userType', 'guest') 
  router.push('/home') 
}

const handleLoginSuccess = (user) => {
  // 登录成功后，更新用户信息
  userInfo.value = {
    username: user.username,
    email: user.email,
    avatar: user.avatar || null // 确保未上传头像时为 null
  }
  
  // 保存用户信息到 localStorage
  const userData = {
    id: user.id,
    username: user.username,
    email: user.email,
    avatar: user.avatar || null // 确保未上传头像时为 null
  }
  
  localStorage.setItem('currentUser', JSON.stringify(userData))
  console.log('登录成功，更新用户信息:', userData) 
  // 跳转到首页
  //router.push('/home')
}
</script>

<style scoped>
.login-entry {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('@/assets/log_bk.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.login-entry::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4); /* 调整遮罩透明度 */
  z-index: 1;
}

.login-container {
  background: rgba(255, 255, 255, 0.2); /* 更透明的背景 */
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  text-align: center;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  margin-bottom: 2.5rem;
}

.login-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.login-title {
  font-size: 2rem;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-weight: 600;
}

.login-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.action-btn {
  width: 100%;
  height: 48px;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 0 !important; /* 覆盖 Element Plus 的默认内边距 */
  margin: 0 !important; /* 覆盖 Element Plus 的默认外边距 */
  line-height: 1 !important; /* 覆盖 Element Plus 的行高 */
}

/* 覆盖 Element Plus 按钮的默认样式 */
:deep(.el-button) {
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1 !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.login-btn {
  background: rgba(64, 158, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  backdrop-filter: blur(4px);
}

.login-btn:hover {
  background: rgba(64, 158, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.register-btn {
  background: rgba(103, 194, 58, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  backdrop-filter: blur(4px);
}

.register-btn:hover {
  background: rgba(103, 194, 58, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.guest-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
}

.guest-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

/* 添加按钮图标样式 */
.el-icon {
  font-size: 1.2rem;
  margin-right: 4px !important; /* 确保图标和文字的间距一致 */
}

/* 确保按钮内容居中对齐 */
:deep(.el-button__content) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}
</style> 