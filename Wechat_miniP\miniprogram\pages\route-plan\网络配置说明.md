# 微信小程序网络配置说明

## 问题原因

微信小程序无法访问 `localhost`，需要使用实际的IP地址来访问本地开发服务器。

## 解决步骤

### 1. 获取本机IP地址

#### Windows系统：
1. 按 `Win + R` 打开运行对话框
2. 输入 `cmd` 并按回车
3. 在命令行中输入 `ipconfig`
4. 找到 "以太网适配器" 或 "无线局域网适配器" 下的 "IPv4 地址"
5. 记录这个IP地址（例如：*************）

#### Mac系统：
1. 打开终端
2. 输入 `ifconfig`
3. 找到 `en0` 或 `en1` 下的 `inet` 地址
4. 记录这个IP地址

#### Linux系统：
1. 打开终端
2. 输入 `ifconfig` 或 `ip addr show`
3. 找到网络接口下的IP地址

### 2. 配置API地址

编辑文件：`Wechat_miniP/miniprogram/config/api.ts`

将 `API_BASE_URL` 中的IP地址替换为您的实际IP地址：

```typescript
const DEV_CONFIG = {
  API_BASE_URL: 'http://您的IP地址:5000/api',  // 例如：http://*************:5000/api
}
```

### 3. 确保后端服务正确启动

1. 确保后端服务在 5000 端口启动
2. 确保后端服务绑定到 0.0.0.0 而不是 127.0.0.1
3. 检查防火墙设置，确保 5000 端口可以访问

### 4. 微信开发者工具配置

1. 在微信开发者工具中，点击右上角的"详情"
2. 在"本地设置"中勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"
3. 这样可以在开发环境中访问 HTTP 地址

### 5. 测试连接

1. 打开微信开发者工具的控制台
2. 查看网络请求是否成功
3. 如果失败，检查控制台的错误信息

## 常见问题

### 问题1：连接超时
- 检查IP地址是否正确
- 检查后端服务是否启动
- 检查防火墙设置

### 问题2：404错误
- 检查API路径是否正确
- 确保后端路由配置正确

### 问题3：CORS错误
- 确保后端已配置CORS
- 检查Flask-CORS设置

## 生产环境配置

在生产环境中，需要：
1. 将API地址改为实际的服务器域名
2. 使用HTTPS协议
3. 在微信公众平台配置合法域名

## 调试技巧

1. 使用微信开发者工具的网络面板查看请求
2. 查看控制台日志
3. 使用Postman等工具测试API接口
4. 检查后端日志
