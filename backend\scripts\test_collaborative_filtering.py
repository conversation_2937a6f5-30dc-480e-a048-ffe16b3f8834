"""
景点推荐系统全面测试脚本

此脚本用于全面测试景点推荐系统的各种功能。它会：
1. 创建大量测试用户和浏览历史数据
2. 测试多种推荐算法（协同过滤、基于内容、混合推荐）
3. 评估推荐结果的质量和性能
4. 输出详细的测试报告和分析结果
5. 模拟不同用户行为模式和偏好

使用方法：
    python scripts/test_collaborative_filtering.py [选项]

选项：
    --users N       创建N个测试用户（默认：20）
    --locations N   每个用户浏览N个地点（默认：10）
    --verbose       显示详细输出
    --visual        生成可视化结果（需要matplotlib）
"""

import os
import sys
import json
import time
import random
import datetime
import argparse
import statistics
from collections import defaultdict, Counter
from sqlalchemy import text
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Dict, Tuple, Set, Any, Optional

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取backend目录的路径
backend_dir = os.path.abspath(os.path.join(current_dir, '..'))
# 将backend目录添加到Python路径
sys.path.insert(0, backend_dir)

# 导入Flask应用和模型
from app import create_app
from utils.database import db
from models.user import User
from models.location import Location
from models.location_browse_count import LocationBrowseCount
from models.location_browse import LocationBrowseHistory
from utils.recommendation_factory import RecommendationFactory

# 服务器URL
SERVER_URL = "http://localhost:5000"

def create_test_app():
    """创建测试应用"""
    app = create_app()
    return app

def add_test_users(app):
    """添加测试用户"""
    with app.app_context():
        # 检查是否已有用户数据
        existing_users = User.query.all()
        existing_usernames = {user.username for user in existing_users}

        print(f"已存在 {len(existing_users)} 个用户: {[user.username for user in existing_users]}")

        # 添加测试用户
        users = []
        for i in range(1, 11):
            username = f"test_user{i}"
            if username not in existing_usernames:
                user = User(
                    username=username,
                    email=f"{username}@example.com",
                    avatar="default_avatar.jpg"
                )
                user.set_password("password123")
                users.append(user)

        if users:
            try:
                db.session.add_all(users)
                db.session.commit()
                print(f"成功添加 {len(users)} 个测试用户")
            except Exception as e:
                db.session.rollback()
                print(f"添加测试用户失败: {str(e)}")
        else:
            print("没有新的测试用户需要添加")

        # 返回所有用户
        return User.query.all()

def clear_browse_history(app):
    """清空浏览历史数据"""
    with app.app_context():
        try:
            # 清空浏览历史
            LocationBrowseHistory.query.delete()
            # 清空浏览计数
            LocationBrowseCount.query.delete()
            # 重置地点热度
            locations = Location.query.all()
            for location in locations:
                location.popularity = 0

            db.session.commit()
            print("成功清空浏览历史数据")

            # 刷新推荐系统的缓存
            try:
                recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()
                recommendation_system.refresh_all_data(force=True)
                print("成功刷新推荐系统缓存")
            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"刷新推荐系统缓存失败: {str(e)}")

        except Exception as e:
            db.session.rollback()
            import traceback
            traceback.print_exc()
            print(f"清空浏览历史数据失败: {str(e)}")

def add_browse_history(app, users, max_locations=10, interactive=False):
    """
    添加浏览历史数据

    Args:
        app: Flask应用
        users: 用户列表
        max_locations: 每个用户最多浏览的地点数量
        interactive: 是否交互式询问是否清空现有浏览历史
    """
    with app.app_context():
        # 检查是否已有浏览历史数据
        existing_history = LocationBrowseHistory.query.count()
        if existing_history > 0:
            print(f"已存在 {existing_history} 条浏览历史记录")
            if interactive:
                choice = input("是否清空现有浏览历史并添加新的测试数据？(y/n): ")
                if choice.lower() != 'y':
                    print("保留现有浏览历史数据，跳过添加测试数据")
                    return
                else:
                    # 清空现有浏览历史
                    clear_browse_history(app)
            else:
                print("使用现有浏览历史数据，跳过添加测试数据")
                return

        # 检查是否有地点数据
        locations_count = Location.query.count()
        if locations_count == 0:
            print("错误：数据库中没有地点数据，请先添加地点数据")
            return

        # 获取所有地点
        locations = Location.query.all()

        # 确保有用户数据
        if not users:
            print("错误：没有用户数据，请先添加用户数据")
            return

        print(f"找到 {len(users)} 个用户")
        print(f"找到 {len(locations)} 个地点")

        # 分类地点
        school_locations = [loc for loc in locations if loc.type == 0]
        scenic_locations = [loc for loc in locations if loc.type == 1]
        other_locations = [loc for loc in locations if loc.type not in [0, 1]]

        if not school_locations:
            # 如果没有学校类型的地点，使用前1/3地点作为学校
            school_locations = locations[:len(locations)//3]

        if not scenic_locations:
            # 如果没有景点类型的地点，使用中间1/3地点作为景点
            scenic_locations = locations[len(locations)//3:2*len(locations)//3]

        if not other_locations:
            # 如果没有其他类型的地点，使用后1/3地点作为其他类型
            other_locations = locations[2*len(locations)//3:]

        print(f"学校类型地点: {len(school_locations)} 个")
        print(f"景点类型地点: {len(scenic_locations)} 个")
        print(f"其他类型地点: {len(other_locations)} 个")

        # 添加浏览历史记录
        histories = []

        # 定义用户偏好类型
        user_preferences = [
            {"name": "学校爱好者", "school": 0.8, "scenic": 0.2, "other": 0.0},
            {"name": "景点爱好者", "school": 0.2, "scenic": 0.8, "other": 0.0},
            {"name": "混合偏好(偏学校)", "school": 0.6, "scenic": 0.3, "other": 0.1},
            {"name": "混合偏好(偏景点)", "school": 0.3, "scenic": 0.6, "other": 0.1},
            {"name": "均衡爱好者", "school": 0.4, "scenic": 0.4, "other": 0.2},
            {"name": "随机浏览者", "school": 0.33, "scenic": 0.33, "other": 0.34}
        ]

        # 为每个用户分配一个偏好类型
        user_preference_map = {}
        for i, user in enumerate(users):
            preference_index = i % len(user_preferences)
            user_preference_map[user.user_id] = user_preferences[preference_index]
            print(f"为用户 {user.username} (ID: {user.user_id}) 分配偏好类型: {user_preferences[preference_index]['name']}")

        # 为每个用户添加浏览历史
        all_locations = school_locations + scenic_locations + other_locations
        for user in users:
            preference = user_preference_map[user.user_id]
            print(f"为用户 {user.username} (ID: {user.user_id}) 添加浏览历史 (偏好: {preference['name']})")

            # 计算每种类型的地点数量
            school_count = min(int(max_locations * preference["school"]), len(school_locations))
            scenic_count = min(int(max_locations * preference["scenic"]), len(scenic_locations))
            other_count = min(int(max_locations * preference["other"]), len(other_locations))

            # 确保至少浏览一个地点
            total_count = school_count + scenic_count + other_count
            if total_count == 0:
                if len(all_locations) > 0:
                    selected_locations = random.sample(all_locations, 1)
                    browse_count = random.randint(1, 3)
                    for _ in range(browse_count):
                        histories.append(LocationBrowseHistory(
                            user_id=user.user_id,
                            location_id=selected_locations[0].location_id,
                            browse_time=datetime.datetime.now() - datetime.timedelta(hours=random.randint(1, 72))
                        ))
                continue

            # 选择学校类型的地点
            if school_count > 0 and school_locations:
                selected_schools = random.sample(school_locations, school_count)
                for loc in selected_schools:
                    # 根据偏好强度决定浏览次数
                    browse_count = random.randint(1, 5) if preference["school"] > 0.5 else random.randint(1, 3)
                    for _ in range(browse_count):
                        histories.append(LocationBrowseHistory(
                            user_id=user.user_id,
                            location_id=loc.location_id,
                            browse_time=datetime.datetime.now() - datetime.timedelta(hours=random.randint(1, 72))
                        ))

            # 选择景点类型的地点
            if scenic_count > 0 and scenic_locations:
                selected_scenics = random.sample(scenic_locations, scenic_count)
                for loc in selected_scenics:
                    # 根据偏好强度决定浏览次数
                    browse_count = random.randint(1, 5) if preference["scenic"] > 0.5 else random.randint(1, 3)
                    for _ in range(browse_count):
                        histories.append(LocationBrowseHistory(
                            user_id=user.user_id,
                            location_id=loc.location_id,
                            browse_time=datetime.datetime.now() - datetime.timedelta(hours=random.randint(1, 72))
                        ))

            # 选择其他类型的地点
            if other_count > 0 and other_locations:
                selected_others = random.sample(other_locations, other_count)
                for loc in selected_others:
                    # 根据偏好强度决定浏览次数
                    browse_count = random.randint(1, 5) if preference["other"] > 0.5 else random.randint(1, 3)
                    for _ in range(browse_count):
                        histories.append(LocationBrowseHistory(
                            user_id=user.user_id,
                            location_id=loc.location_id,
                            browse_time=datetime.datetime.now() - datetime.timedelta(hours=random.randint(1, 72))
                        ))

        print(f"共生成 {len(histories)} 条浏览历史记录")

        # 分批提交，避免一次提交太多数据
        batch_size = 50
        for i in range(0, len(histories), batch_size):
            batch = histories[i:i+batch_size]
            try:
                db.session.add_all(batch)
                db.session.commit()
                print(f"成功添加第 {i//batch_size + 1}/{(len(histories)-1)//batch_size + 1} 批浏览历史记录，共 {len(batch)} 条")
            except Exception as e:
                db.session.rollback()
                print(f"添加第 {i//batch_size + 1} 批浏览历史记录失败: {str(e)}")
                # 尝试逐条添加，以便找出问题
                for record in batch:
                    try:
                        db.session.add(record)
                        db.session.commit()
                        print(f"  成功添加记录: 用户ID={record.user_id}, 地点ID={record.location_id}")
                    except Exception as e2:
                        db.session.rollback()
                        print(f"  添加记录失败: 用户ID={record.user_id}, 地点ID={record.location_id}, 错误: {str(e2)}")

        # 更新浏览计数
        update_browse_counts(app)

        print(f"浏览历史数据添加完成，共 {len(histories)} 条记录")

def update_browse_counts(app):
    """更新浏览计数表"""
    with app.app_context():
        try:
            # 清空现有计数
            LocationBrowseCount.query.delete()
            db.session.commit()
            print("已清空现有浏览计数记录")

            # 统计浏览次数并插入
            sql = """
            INSERT INTO location_browse_counts (user_id, location_id, count)
            SELECT user_id, location_id, COUNT(*) as count
            FROM location_browse_history
            GROUP BY user_id, location_id
            """
            db.session.execute(text(sql))

            # 更新地点热度
            sql = """
            UPDATE locations l
            SET popularity = (
                SELECT COUNT(*)
                FROM location_browse_history lbh
                WHERE lbh.location_id = l.location_id
            )
            """
            db.session.execute(text(sql))
            db.session.commit()

            # 统计更新了多少条记录
            count_records = LocationBrowseCount.query.count()
            print(f"成功更新 {count_records} 条浏览计数记录")

        except Exception as e:
            db.session.rollback()
            print(f"更新浏览计数失败: {str(e)}")

def test_similar_users(app, user_id=1):
    """测试相似用户功能"""
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data(force=True)

        # 获取相似用户
        similar_users = recommendation_system.find_similar_users(user_id, limit=5)

        print(f"\n用户{user_id}的相似用户:")
        if similar_users:
            for similar_user_id, similarity in similar_users:
                print(f"  用户{similar_user_id}: 相似度 {similarity:.4f}")
        else:
            print("  没有找到相似用户（可能是因为只有一个用户）")

        return similar_users

def test_collaborative_filtering(app, user_id=1, evaluate_features=False):
    """
    测试协同过滤推荐功能并详细解释推荐过程

    Args:
        app: Flask应用
        user_id: 用户ID
        evaluate_features: 是否评估协同过滤算法的核心特性

    Returns:
        推荐结果列表 [(location_id, score), ...]
    """
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data(force=True)

        # 获取用户浏览历史
        user_history = recommendation_system.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # 获取用户信息
        user = User.query.get(user_id)
        username = user.username if user else f"用户{user_id}"

        print(f"\n===== {username} (ID: {user_id}) 的协同过滤推荐分析 =====")

        # 显示用户浏览历史
        print(f"\n1. {username}的浏览历史:")
        if user_history:
            for loc_id, count in user_history.items():
                location = recommendation_system.locations.get(loc_id)
                if location:
                    print(f"  • 地点{loc_id} ({location.name}): 浏览{count}次, 类型: {'学校' if location.type == 0 else '景点' if location.type == 1 else '其他'}")
        else:
            print("  • 没有浏览历史")

        # 获取相似用户
        similar_users = recommendation_system.find_similar_users(user_id, limit=5)

        # 显示相似用户
        print(f"\n2. 与{username}相似的用户:")
        if similar_users:
            for similar_user_id, similarity in similar_users:
                similar_user = User.query.get(similar_user_id)
                similar_username = similar_user.username if similar_user else f"用户{similar_user_id}"
                print(f"  • {similar_username} (ID: {similar_user_id}): 相似度 {similarity:.4f}")

                # 显示相似用户的浏览历史
                similar_user_history = recommendation_system.user_browse_history.get(similar_user_id, {})
                if similar_user_history:
                    print(f"    浏览历史:")
                    for loc_id, count in similar_user_history.items():
                        location = recommendation_system.locations.get(loc_id)
                        if location:
                            print(f"    - 地点{loc_id} ({location.name}): 浏览{count}次, 类型: {'学校' if location.type == 0 else '景点' if location.type == 1 else '其他'}")
        else:
            print("  • 没有找到相似用户")

        # 获取协同过滤推荐
        recommendations = recommendation_system.get_collaborative_filtering_recommendations(user_id, limit=5)

        # 显示推荐结果
        print(f"\n3. 基于协同过滤的推荐结果:")
        if recommendations:
            for location_id, score in recommendations:
                location = recommendation_system.locations.get(location_id)
                if location:
                    # 查找哪些相似用户浏览过这个地点
                    recommenders = []
                    for similar_user_id, similarity in similar_users:
                        similar_user_history = recommendation_system.user_browse_history.get(similar_user_id, {})
                        if location_id in similar_user_history:
                            similar_user = User.query.get(similar_user_id)
                            similar_username = similar_user.username if similar_user else f"用户{similar_user_id}"
                            recommenders.append(f"{similar_username} (相似度: {similarity:.2f}, 浏览次数: {similar_user_history[location_id]})")

                    # 显示推荐地点信息
                    print(f"  • 地点{location_id} ({location.name}): 分数 {score:.4f}, 类型: {'学校' if location.type == 0 else '景点'}")

                    # 显示推荐原因
                    if recommenders:
                        print(f"    推荐原因: 以下相似用户浏览过此地点:")
                        for recommender in recommenders:
                            print(f"    - {recommender}")
                    else:
                        print(f"    推荐原因: 此地点很受欢迎")
        else:
            print("  • 没有推荐结果")

        # 解释协同过滤的工作原理
        print(f"\n4. 协同过滤工作原理解释:")
        print("  • 步骤1: 找到与当前用户浏览习惯相似的其他用户")
        print("  • 步骤2: 分析这些相似用户浏览过但当前用户未浏览的地点")
        print("  • 步骤3: 根据相似用户的相似度和浏览次数计算推荐分数")
        print("  • 步骤4: 返回分数最高的地点作为推荐结果")

        # 判断协同过滤是否有效
        if not user_history:
            print("\n协同过滤效果评估: 由于用户没有浏览历史，协同过滤退化为热门推荐")
        elif not similar_users:
            print("\n协同过滤效果评估: 由于没有找到相似用户，协同过滤退化为热门推荐")
        elif not recommendations:
            print("\n协同过滤效果评估: 没有找到适合推荐的地点")
        else:
            # 检查推荐的地点类型是否与用户浏览历史的地点类型不同
            user_location_types = {}
            for loc_id in user_locations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    loc_type = location.type
                    user_location_types[loc_type] = user_location_types.get(loc_type, 0) + 1

            recommended_location_types = {}
            for loc_id, _ in recommendations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    loc_type = location.type
                    recommended_location_types[loc_type] = recommended_location_types.get(loc_type, 0) + 1

            # 判断是否推荐了用户未浏览过的类型
            has_new_types = False
            for loc_type in recommended_location_types:
                if loc_type not in user_location_types:
                    has_new_types = True
                    break

            if has_new_types:
                print("\n协同过滤效果评估: 有效 - 推荐了用户未浏览过类型的地点")
            else:
                # 检查是否推荐了用户未浏览过的地点
                has_new_locations = False
                for loc_id, _ in recommendations:
                    if loc_id not in user_locations:
                        has_new_locations = True
                        break

                if has_new_locations:
                    print("\n协同过滤效果评估: 有效 - 推荐了用户未浏览过的地点")
                else:
                    print("\n协同过滤效果评估: 无效 - 只推荐了用户已浏览过的地点")

        # 如果需要评估协同过滤算法的核心特性
        if evaluate_features:
            evaluate_collaborative_filtering_features(app, user_id)

        return recommendations

def test_content_based(app, user_id=1):
    """测试基于内容的推荐功能并详细解释推荐过程"""
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data(force=True)

        # 获取用户浏览历史
        user_history = recommendation_system.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # 获取用户信息
        user = User.query.get(user_id)
        username = user.username if user else f"用户{user_id}"

        print(f"\n===== {username} (ID: {user_id}) 的基于内容的推荐分析 =====")

        # 显示用户浏览历史
        print(f"\n1. {username}的浏览历史:")
        if user_history:
            for loc_id, count in user_history.items():
                location = recommendation_system.locations.get(loc_id)
                if location:
                    print(f"  • 地点{loc_id} ({location.name}): 浏览{count}次, 类型: {'学校' if location.type == 0 else '景点'}")
                    if location.keyword:
                        print(f"    关键词: {location.keyword}")
        else:
            print("  • 没有浏览历史")

        # 获取基于内容的推荐
        recommendations = recommendation_system.get_content_based_recommendations(user_id, limit=5)

        # 显示推荐结果
        print(f"\n2. 基于内容的推荐结果:")
        if recommendations:
            for location_id, score in recommendations:
                location = recommendation_system.locations.get(location_id)
                if location:
                    # 显示推荐地点信息
                    print(f"  • 地点{location_id} ({location.name}): 分数 {score:.4f}, 类型: {'学校' if location.type == 0 else '景点'}")
                    if location.keyword:
                        print(f"    关键词: {location.keyword}")

                    # 查找与用户浏览过的地点的相似之处
                    similar_locations = []
                    for loc_id in user_locations:
                        browsed_location = recommendation_system.locations.get(loc_id)
                        if browsed_location and browsed_location.type == location.type:
                            similar_locations.append(f"{browsed_location.name} (ID: {loc_id}, 类型: {'学校' if browsed_location.type == 0 else '景点'})")

                    # 显示推荐原因
                    if similar_locations:
                        print(f"    推荐原因: 与您浏览过的以下地点类型相似:")
                        for similar_location in similar_locations[:3]:  # 最多显示3个
                            print(f"    - {similar_location}")
                    else:
                        print(f"    推荐原因: 此地点与您的兴趣相符")
        else:
            print("  • 没有推荐结果")

        # 解释基于内容的推荐工作原理
        print(f"\n3. 基于内容的推荐工作原理解释:")
        print("  • 步骤1: 分析用户浏览过的地点的特征（类型、关键词等）")
        print("  • 步骤2: 寻找具有相似特征但用户未浏览过的地点")
        print("  • 步骤3: 根据特征相似度计算推荐分数")
        print("  • 步骤4: 返回分数最高的地点作为推荐结果")

        # 判断基于内容的推荐是否有效
        if not user_history:
            print("\n基于内容的推荐效果评估: 由于用户没有浏览历史，基于内容的推荐退化为热门推荐")
        elif not recommendations:
            print("\n基于内容的推荐效果评估: 没有找到适合推荐的地点")
        else:
            # 检查推荐的地点是否与用户浏览历史的地点有相似之处
            has_similar_features = False
            for loc_id, _ in recommendations:
                if loc_id not in user_locations:  # 确保不推荐已浏览过的地点
                    location = recommendation_system.locations.get(loc_id)
                    if location:
                        for browsed_loc_id in user_locations:
                            browsed_location = recommendation_system.locations.get(browsed_loc_id)
                            if browsed_location and browsed_location.type == location.type:
                                has_similar_features = True
                                break
                if has_similar_features:
                    break

            if has_similar_features:
                print("\n基于内容的推荐效果评估: 有效 - 推荐了与用户浏览历史相似的地点")
            else:
                print("\n基于内容的推荐效果评估: 无效 - 推荐的地点与用户浏览历史无明显相似之处")

        return recommendations

def test_hybrid_recommendations(app, user_id, limit=10, verbose=True):
    """
    测试混合推荐功能并详细解释推荐过程

    Args:
        app: Flask应用
        user_id: 用户ID
        limit: 返回结果数量限制
        verbose: 是否显示详细输出

    Returns:
        推荐结果列表 [(location_id, score), ...]
    """
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data(force=True)

        # 获取用户浏览历史
        user_history = recommendation_system.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # 获取用户信息
        user = User.query.get(user_id)
        username = user.username if user else f"用户{user_id}"

        if verbose:
            print(f"\n===== {username} (ID: {user_id}) 的混合推荐分析 =====")

            # 显示用户浏览历史
            print(f"\n1. {username}的浏览历史:")
            if user_history:
                for loc_id, count in user_history.items():
                    location = recommendation_system.locations.get(loc_id)
                    if location:
                        print(f"  • 地点{loc_id} ({location.name}): 浏览{count}次, 类型: {'学校' if location.type == 0 else '景点' if location.type == 1 else '其他'}")
            else:
                print("  • 没有浏览历史")

        # 获取各种推荐结果
        start_time = time.time()
        collaborative_recs = recommendation_system.get_collaborative_filtering_recommendations(user_id, limit=limit)
        collaborative_time = time.time() - start_time

        start_time = time.time()
        content_recs = recommendation_system.get_content_based_recommendations(user_id, limit=limit)
        content_time = time.time() - start_time

        # 使用不同的权重组合
        weights = {
            'collaborative': 1.0,
            'content': 1.0,
            'popularity': 0.5
        }

        start_time = time.time()
        recommendations = recommendation_system.get_hybrid_recommendations(user_id, weights=weights, limit=limit)
        hybrid_time = time.time() - start_time

        if verbose:
            # 显示各种推荐结果
            print(f"\n2. 协同过滤推荐结果 (耗时: {collaborative_time:.4f}秒):")
            if collaborative_recs:
                for location_id, score in collaborative_recs:
                    location = recommendation_system.locations.get(location_id)
                    if location:
                        print(f"  • 地点{location_id} ({location.name}): 分数 {score:.4f}, 类型: {'学校' if location.type == 0 else '景点' if location.type == 1 else '其他'}")
            else:
                print("  • 没有推荐结果")

            print(f"\n3. 基于内容的推荐结果 (耗时: {content_time:.4f}秒):")
            if content_recs:
                for location_id, score in content_recs:
                    location = recommendation_system.locations.get(location_id)
                    if location:
                        print(f"  • 地点{location_id} ({location.name}): 分数 {score:.4f}, 类型: {'学校' if location.type == 0 else '景点' if location.type == 1 else '其他'}")
            else:
                print("  • 没有推荐结果")

            print(f"\n4. 混合推荐结果 (耗时: {hybrid_time:.4f}秒):")
            if recommendations:
                for location_id, score in recommendations:
                    location = recommendation_system.locations.get(location_id)
                    if location:
                        # 查找在其他推荐中的分数
                        cf_score = next((s for lid, s in collaborative_recs if lid == location_id), 0)
                        cb_score = next((s for lid, s in content_recs if lid == location_id), 0)

                        print(f"  • 地点{location_id} ({location.name}): 分数 {score:.4f}, 类型: {'学校' if location.type == 0 else '景点' if location.type == 1 else '其他'}")
                        print(f"    - 协同过滤分数: {cf_score:.4f}")
                        print(f"    - 基于内容分数: {cb_score:.4f}")
                        print(f"    - 热度因子: {location.popularity or 0}")
            else:
                print("  • 没有推荐结果")

            # 评估推荐结果
            metrics = evaluate_recommendations(app, user_id, recommendations)

            print(f"\n5. 推荐结果评估:")
            print(f"  • 推荐数量: {metrics['recommendation_count']}")
            print(f"  • 多样性: {metrics['diversity']:.4f} (类型种类/推荐数量)")
            print(f"  • 新颖性: {metrics['novelty']:.4f} (新地点比例)")

            # 显示地点类型分布
            print(f"\n6. 地点类型分布:")
            print(f"  • 用户浏览历史: {metrics['user_history_type_distribution']}")
            print(f"  • 推荐结果: {metrics['location_type_distribution']}")

            # 解释混合推荐的工作原理
            print(f"\n7. 混合推荐工作原理解释:")
            print("  • 步骤1: 分别获取协同过滤、基于内容和热度排序的推荐结果")
            print("  • 步骤2: 归一化各个推荐方法的分数")
            print("  • 步骤3: 根据权重计算每个地点的加权平均分数")
            print(f"    - 协同过滤权重: {weights['collaborative']}")
            print(f"    - 基于内容权重: {weights['content']}")
            print(f"    - 热度权重: {weights['popularity']}")
            print("  • 步骤4: 返回分数最高的地点作为推荐结果")

        return recommendations

def compare_recommendations(app, user_id):
    """比较协同过滤和基于内容的推荐结果"""
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data(force=True)

        # 获取用户信息
        user = User.query.get(user_id)
        username = user.username if user else f"用户{user_id}"

        print(f"\n===== {username} (ID: {user_id}) 的推荐方法比较 =====")

        # 获取协同过滤推荐
        cf_recommendations = recommendation_system.get_collaborative_filtering_recommendations(user_id, limit=5)
        cf_locations = {loc_id: score for loc_id, score in cf_recommendations}

        # 获取基于内容的推荐
        cb_recommendations = recommendation_system.get_content_based_recommendations(user_id, limit=5)
        cb_locations = {loc_id: score for loc_id, score in cb_recommendations}

        # 找出两种方法共同推荐的地点
        common_locations = set(cf_locations.keys()) & set(cb_locations.keys())

        # 找出协同过滤独有的推荐
        cf_only_locations = set(cf_locations.keys()) - common_locations

        # 找出基于内容独有的推荐
        cb_only_locations = set(cb_locations.keys()) - common_locations

        # 显示比较结果
        print("\n1. 两种推荐方法的比较:")
        print(f"  • 协同过滤推荐数量: {len(cf_locations)}")
        print(f"  • 基于内容推荐数量: {len(cb_locations)}")
        print(f"  • 共同推荐数量: {len(common_locations)}")
        print(f"  • 协同过滤独有推荐数量: {len(cf_only_locations)}")
        print(f"  • 基于内容独有推荐数量: {len(cb_only_locations)}")

        # 显示共同推荐的地点
        if common_locations:
            print("\n2. 两种方法共同推荐的地点:")
            for loc_id in common_locations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    print(f"  • 地点{loc_id} ({location.name})")
                    print(f"    - 协同过滤分数: {cf_locations[loc_id]:.4f}")
                    print(f"    - 基于内容分数: {cb_locations[loc_id]:.4f}")

        # 显示协同过滤独有的推荐
        if cf_only_locations:
            print("\n3. 协同过滤独有的推荐:")
            for loc_id in cf_only_locations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    print(f"  • 地点{loc_id} ({location.name}): 分数 {cf_locations[loc_id]:.4f}")

        # 显示基于内容独有的推荐
        if cb_only_locations:
            print("\n4. 基于内容独有的推荐:")
            for loc_id in cb_only_locations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    print(f"  • 地点{loc_id} ({location.name}): 分数 {cb_locations[loc_id]:.4f}")

        # 分析结果
        print("\n5. 分析结果:")
        if len(common_locations) > 0:
            print("  • 两种推荐方法有一定的重叠，说明它们在某些情况下会得出相似的结论")
        else:
            print("  • 两种推荐方法没有重叠，说明它们关注的特征完全不同")

        if len(cf_only_locations) > 0:
            print("  • 协同过滤能够发现基于内容无法发现的推荐，这些可能是基于用户行为模式而非内容特征的推荐")

        if len(cb_only_locations) > 0:
            print("  • 基于内容能够发现协同过滤无法发现的推荐，这些可能是基于内容特征而非用户行为模式的推荐")

        # 总结
        print("\n6. 总结:")
        print("  • 协同过滤优势: 能够发现基于用户行为模式的隐含兴趣，不需要内容特征")
        print("  • 基于内容优势: 能够基于内容特征进行推荐，不受冷启动问题影响")
        print("  • 最佳实践: 结合两种方法，形成混合推荐系统，取长补短")

def run_tests(app, users):
    """运行所有测试"""
    print("开始测试景点推荐系统的协同过滤功能...")

    # 确保有用户数据
    if not users:
        print("错误：没有用户数据，无法进行测试")
        return

    # 测试用户1（喜欢学校类型的地点）
    user1 = users[0]
    print(f"\n===== 测试用户 {user1.username} (ID: {user1.user_id})（喜欢学校类型的地点）=====")
    test_similar_users(app, user_id=user1.user_id)
    test_collaborative_filtering(app, user_id=user1.user_id)
    test_content_based(app, user_id=user1.user_id)
    test_hybrid_recommendations(app, user_id=user1.user_id)

    # 如果有第二个用户，测试第二个用户（喜欢景点类型的地点）
    if len(users) > 1:
        user2 = users[1]
        print(f"\n===== 测试用户 {user2.username} (ID: {user2.user_id})（喜欢景点类型的地点）=====")
        test_similar_users(app, user_id=user2.user_id)
        test_collaborative_filtering(app, user_id=user2.user_id)
        test_content_based(app, user_id=user2.user_id)
        test_hybrid_recommendations(app, user_id=user2.user_id)

    # 如果有第五个用户，测试第五个用户（与用户1相似）
    if len(users) > 4:
        user5 = users[4]
        print(f"\n===== 测试用户 {user5.username} (ID: {user5.user_id})（与用户1相似）=====")
        test_similar_users(app, user_id=user5.user_id)
        test_collaborative_filtering(app, user_id=user5.user_id)
        test_hybrid_recommendations(app, user_id=user5.user_id)

    # 如果有第六个用户，测试第六个用户（与用户2相似）
    if len(users) > 5:
        user6 = users[5]
        print(f"\n===== 测试用户 {user6.username} (ID: {user6.user_id})（与用户2相似）=====")
        test_similar_users(app, user_id=user6.user_id)
        test_collaborative_filtering(app, user_id=user6.user_id)
        test_hybrid_recommendations(app, user_id=user6.user_id)

    print("\n测试完成！")

# 解析命令行参数
def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='景点推荐系统全面测试脚本')
    parser.add_argument('--users', type=int, default=20, help='创建的测试用户数量（默认：20）')
    parser.add_argument('--locations', type=int, default=10, help='每个用户浏览的地点数量（默认：10）')
    parser.add_argument('--verbose', action='store_true', help='显示详细输出')
    parser.add_argument('--visual', action='store_true', help='生成可视化结果（需要matplotlib）')
    parser.add_argument('--test-user', type=int, default=None, help='指定测试用户ID')
    parser.add_argument('--algorithm', choices=['collaborative', 'content', 'hybrid', 'all'],
                        default='all', help='测试的推荐算法（默认：all）')
    parser.add_argument('--clear-history', action='store_true', help='清空现有浏览历史')
    parser.add_argument('--seed', type=int, default=42, help='随机数种子（默认：42）')
    parser.add_argument('--evaluate-features', action='store_true', help='评估协同过滤算法的核心特性')
    return parser.parse_args()

# 评估协同过滤算法的核心特性
def evaluate_collaborative_filtering_features(app, user_id, verbose=True):
    """
    评估协同过滤算法是否体现出了其核心特性

    Args:
        app: Flask应用
        user_id: 用户ID
        verbose: 是否显示详细输出

    Returns:
        评估结果字典，包含各项特性的评分和总体评分
    """
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data(force=True)

        # 获取用户信息
        user = User.query.get(user_id)
        username = user.username if user else f"用户{user_id}"

        # 获取用户浏览历史
        user_history = recommendation_system.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # 获取所有用户
        all_users = User.query.all()
        all_user_ids = [u.user_id for u in all_users]

        # 获取相似用户
        similar_users = recommendation_system.find_similar_users(user_id, limit=None)
        similar_user_ids = [uid for uid, _ in similar_users]

        # 获取协同过滤推荐
        cf_recommendations = recommendation_system.get_collaborative_filtering_recommendations(user_id, limit=10)

        # 获取基于内容的推荐
        cb_recommendations = recommendation_system.get_content_based_recommendations(user_id, limit=10)

        # 获取热门推荐
        popularity_recommendations = recommendation_system.get_popularity_recommendations(limit=10)

        # 初始化评估结果
        evaluation = {
            'user_similarity_calculation': 0,  # 用户相似性计算
            'cold_start_handling': 0,          # 冷启动问题处理
            'recommendation_diversity': 0,      # 推荐多样性
            'serendipity': 0,                  # 意外发现
            'user_preference_adaptation': 0,    # 用户偏好适应
            'scalability': 0,                  # 可扩展性
            'overall_score': 0                 # 总体评分
        }

        # 1. 评估用户相似性计算
        if similar_users:
            # 检查相似用户的浏览历史是否与当前用户有相似之处
            similarity_scores = []
            for similar_user_id, similarity in similar_users:
                similar_user_history = recommendation_system.user_browse_history.get(similar_user_id, {})
                similar_user_locations = set(similar_user_history.keys())

                # 计算浏览历史的交集比例
                intersection = user_locations & similar_user_locations
                union = user_locations | similar_user_locations

                if union:
                    jaccard_similarity = len(intersection) / len(union)
                    similarity_scores.append((jaccard_similarity, similarity))

            # 计算Jaccard相似度与系统计算的相似度的相关性
            if similarity_scores:
                jaccard_similarities = [s[0] for s in similarity_scores]
                system_similarities = [s[1] for s in similarity_scores]

                # 计算平均相似度
                avg_jaccard = sum(jaccard_similarities) / len(jaccard_similarities)
                avg_system = sum(system_similarities) / len(system_similarities)

                # 评分标准：平均相似度越高，评分越高
                evaluation['user_similarity_calculation'] = min(5, avg_system * 5)

        # 2. 评估冷启动问题处理
        if not user_history:
            # 冷启动情况：用户没有浏览历史
            # 检查推荐是否退化为热门推荐
            cf_ids = set(loc_id for loc_id, _ in cf_recommendations)
            pop_ids = set(loc_id for loc_id, _ in popularity_recommendations)

            overlap_ratio = len(cf_ids & pop_ids) / len(pop_ids) if pop_ids else 0

            # 评分标准：如果完全退化为热门推荐，评分为1；如果有一定的个性化，评分更高
            evaluation['cold_start_handling'] = 1 + (1 - overlap_ratio) * 4
        else:
            # 非冷启动情况：用户有浏览历史
            # 检查推荐是否基于用户偏好
            user_location_types = {}
            for loc_id in user_locations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    loc_type = location.type
                    user_location_types[loc_type] = user_location_types.get(loc_type, 0) + 1

            recommended_location_types = {}
            for loc_id, _ in cf_recommendations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    loc_type = location.type
                    recommended_location_types[loc_type] = recommended_location_types.get(loc_type, 0) + 1

            # 计算类型分布的相似度
            type_similarity = 0
            total_types = set(user_location_types.keys()) | set(recommended_location_types.keys())
            if total_types:
                similarities = []
                for loc_type in total_types:
                    user_count = user_location_types.get(loc_type, 0)
                    rec_count = recommended_location_types.get(loc_type, 0)

                    # 归一化计数
                    user_norm = user_count / sum(user_location_types.values()) if user_location_types else 0
                    rec_norm = rec_count / sum(recommended_location_types.values()) if recommended_location_types else 0

                    # 计算差异
                    diff = abs(user_norm - rec_norm)
                    similarities.append(1 - diff)

                type_similarity = sum(similarities) / len(similarities)

            # 评分标准：类型分布越相似，评分越高
            evaluation['cold_start_handling'] = type_similarity * 5

        # 3. 评估推荐多样性
        if cf_recommendations:
            # 计算推荐结果中不同类型的比例
            cf_types = set()
            for loc_id, _ in cf_recommendations:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    cf_types.add(location.type)

            # 计算多样性得分
            diversity_score = len(cf_types) / len(cf_recommendations)

            # 评分标准：类型越多样，评分越高
            evaluation['recommendation_diversity'] = diversity_score * 5

        # 4. 评估意外发现（Serendipity）
        if cf_recommendations and cb_recommendations:
            # 比较协同过滤和基于内容的推荐结果
            cf_ids = set(loc_id for loc_id, _ in cf_recommendations)
            cb_ids = set(loc_id for loc_id, _ in cb_recommendations)

            # 计算协同过滤独有的推荐
            cf_only = cf_ids - cb_ids

            # 评分标准：协同过滤独有的推荐越多，意外发现的可能性越高
            serendipity_score = len(cf_only) / len(cf_ids) if cf_ids else 0
            evaluation['serendipity'] = serendipity_score * 5

        # 5. 评估用户偏好适应
        if user_history and similar_users:
            # 检查推荐是否基于相似用户的偏好
            similar_user_locations = set()
            for similar_user_id, _ in similar_users[:5]:  # 取前5个相似用户
                similar_user_history = recommendation_system.user_browse_history.get(similar_user_id, {})
                similar_user_locations.update(similar_user_history.keys())

            # 计算推荐中来自相似用户的比例
            cf_ids = set(loc_id for loc_id, _ in cf_recommendations)
            from_similar_users = cf_ids & similar_user_locations

            adaptation_score = len(from_similar_users) / len(cf_ids) if cf_ids else 0

            # 评分标准：来自相似用户的推荐越多，适应性越好
            evaluation['user_preference_adaptation'] = adaptation_score * 5

        # 6. 评估可扩展性
        # 这里简单根据用户数量和地点数量评估
        user_count = len(all_user_ids)
        location_count = len(recommendation_system.locations)

        # 评分标准：用户和地点数量越多，可扩展性挑战越大
        scale_factor = min(1, (user_count * location_count) / (100 * 100))  # 假设100用户*100地点是基准
        evaluation['scalability'] = 3 + scale_factor * 2  # 基础分3分，最高5分

        # 计算总体评分
        weights = {
            'user_similarity_calculation': 0.25,
            'cold_start_handling': 0.15,
            'recommendation_diversity': 0.15,
            'serendipity': 0.15,
            'user_preference_adaptation': 0.2,
            'scalability': 0.1
        }

        weighted_sum = sum(evaluation[key] * weights[key] for key in weights)
        evaluation['overall_score'] = weighted_sum

        # 输出评估结果
        if verbose:
            print(f"\n===== 协同过滤算法核心特性评估 ({username}, ID: {user_id}) =====")
            print(f"1. 用户相似性计算: {evaluation['user_similarity_calculation']:.2f}/5.00")
            print(f"2. 冷启动问题处理: {evaluation['cold_start_handling']:.2f}/5.00")
            print(f"3. 推荐多样性: {evaluation['recommendation_diversity']:.2f}/5.00")
            print(f"4. 意外发现: {evaluation['serendipity']:.2f}/5.00")
            print(f"5. 用户偏好适应: {evaluation['user_preference_adaptation']:.2f}/5.00")
            print(f"6. 可扩展性: {evaluation['scalability']:.2f}/5.00")
            print(f"总体评分: {evaluation['overall_score']:.2f}/5.00")

            # 给出总体评价
            if evaluation['overall_score'] >= 4.5:
                print("总体评价: 优秀 - 协同过滤算法表现出色，充分体现了其核心特性")
            elif evaluation['overall_score'] >= 3.5:
                print("总体评价: 良好 - 协同过滤算法表现良好，体现了大部分核心特性")
            elif evaluation['overall_score'] >= 2.5:
                print("总体评价: 一般 - 协同过滤算法表现一般，部分体现了其核心特性")
            else:
                print("总体评价: 不佳 - 协同过滤算法表现不佳，未能充分体现其核心特性")

        return evaluation

# 评估推荐结果
def evaluate_recommendations(app, user_id, recommendations, ground_truth=None):
    """
    评估推荐结果的质量

    Args:
        app: Flask应用
        user_id: 用户ID
        recommendations: 推荐结果列表 [(location_id, score), ...]
        ground_truth: 真实喜好列表 [location_id, ...] (可选)

    Returns:
        评估指标字典
    """
    with app.app_context():
        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 获取用户浏览历史
        user_history = recommendation_system.user_browse_history.get(user_id, {})
        user_locations = set(user_history.keys())

        # 计算推荐的地点类型分布
        location_types = {}
        for loc_id, _ in recommendations:
            location = recommendation_system.locations.get(loc_id)
            if location:
                loc_type = location.type
                location_types[loc_type] = location_types.get(loc_type, 0) + 1

        # 计算用户浏览历史的地点类型分布
        user_location_types = {}
        for loc_id in user_locations:
            location = recommendation_system.locations.get(loc_id)
            if location:
                loc_type = location.type
                user_location_types[loc_type] = user_location_types.get(loc_type, 0) + 1

        # 计算评估指标
        metrics = {
            'recommendation_count': len(recommendations),
            'location_type_distribution': location_types,
            'user_history_type_distribution': user_location_types,
            'diversity': len(location_types) / max(1, len(recommendations)),  # 类型多样性
            'novelty': sum(1 for loc_id, _ in recommendations if loc_id not in user_locations) / max(1, len(recommendations))  # 新颖性
        }

        # 如果提供了真实喜好，计算准确率、召回率和F1分数
        if ground_truth:
            true_positives = sum(1 for loc_id, _ in recommendations if loc_id in ground_truth)
            precision = true_positives / max(1, len(recommendations))
            recall = true_positives / max(1, len(ground_truth))
            f1 = 2 * precision * recall / max(0.001, precision + recall)

            metrics.update({
                'precision': precision,
                'recall': recall,
                'f1_score': f1
            })

        return metrics

# 可视化推荐结果
def visualize_recommendations(app, user_id, collaborative_recs, content_recs, hybrid_recs, output_file=None):
    """
    可视化不同推荐算法的结果对比

    Args:
        app: Flask应用
        user_id: 用户ID
        collaborative_recs: 协同过滤推荐结果
        content_recs: 基于内容的推荐结果
        hybrid_recs: 混合推荐结果
        output_file: 输出文件路径（可选）
    """
    try:
        with app.app_context():
            # 获取推荐系统
            recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

            # 获取用户信息
            user = User.query.get(user_id)
            username = user.username if user else f"用户{user_id}"

            # 创建图表
            plt.figure(figsize=(15, 10))

            # 1. 推荐结果重叠分析
            plt.subplot(2, 2, 1)

            # 获取各算法推荐的地点ID集合
            cf_ids = {loc_id for loc_id, _ in collaborative_recs}
            cb_ids = {loc_id for loc_id, _ in content_recs}
            hybrid_ids = {loc_id for loc_id, _ in hybrid_recs}

            # 计算重叠
            cf_only = len(cf_ids - (cb_ids | hybrid_ids))
            cb_only = len(cb_ids - (cf_ids | hybrid_ids))
            hybrid_only = len(hybrid_ids - (cf_ids | cb_ids))
            cf_cb = len((cf_ids & cb_ids) - hybrid_ids)
            cf_hybrid = len((cf_ids & hybrid_ids) - cb_ids)
            cb_hybrid = len((cb_ids & hybrid_ids) - cf_ids)
            all_common = len(cf_ids & cb_ids & hybrid_ids)

            # 绘制Venn图
            from matplotlib_venn import venn3
            venn3(subsets=(cf_only, cb_only, cf_cb, hybrid_only, cf_hybrid, cb_hybrid, all_common),
                  set_labels=('协同过滤', '基于内容', '混合推荐'))
            plt.title(f"{username}的推荐结果重叠分析")

            # 2. 地点类型分布
            plt.subplot(2, 2, 2)

            # 获取用户浏览历史
            user_history = recommendation_system.user_browse_history.get(user_id, {})

            # 统计各算法推荐的地点类型
            cf_types = Counter()
            cb_types = Counter()
            hybrid_types = Counter()
            history_types = Counter()

            # 统计用户浏览历史的地点类型
            for loc_id in user_history:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    history_types[location.type] += 1

            # 统计各算法推荐的地点类型
            for loc_id, _ in collaborative_recs:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    cf_types[location.type] += 1

            for loc_id, _ in content_recs:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    cb_types[location.type] += 1

            for loc_id, _ in hybrid_recs:
                location = recommendation_system.locations.get(loc_id)
                if location:
                    hybrid_types[location.type] += 1

            # 获取所有类型
            all_types = set(history_types.keys()) | set(cf_types.keys()) | set(cb_types.keys()) | set(hybrid_types.keys())

            # 准备数据
            labels = [f"类型{t}" for t in all_types]
            history_data = [history_types.get(t, 0) for t in all_types]
            cf_data = [cf_types.get(t, 0) for t in all_types]
            cb_data = [cb_types.get(t, 0) for t in all_types]
            hybrid_data = [hybrid_types.get(t, 0) for t in all_types]

            # 绘制柱状图
            x = np.arange(len(labels))
            width = 0.2

            plt.bar(x - 1.5*width, history_data, width, label='浏览历史')
            plt.bar(x - 0.5*width, cf_data, width, label='协同过滤')
            plt.bar(x + 0.5*width, cb_data, width, label='基于内容')
            plt.bar(x + 1.5*width, hybrid_data, width, label='混合推荐')

            plt.xlabel('地点类型')
            plt.ylabel('数量')
            plt.title(f"{username}的地点类型分布")
            plt.xticks(x, labels)
            plt.legend()

            # 3. 评分分布
            plt.subplot(2, 2, 3)

            # 获取各算法推荐的地点评分
            cf_scores = [score for _, score in collaborative_recs]
            cb_scores = [score for _, score in content_recs]
            hybrid_scores = [score for _, score in hybrid_recs]

            # 绘制箱线图
            plt.boxplot([cf_scores, cb_scores, hybrid_scores], labels=['协同过滤', '基于内容', '混合推荐'])
            plt.ylabel('推荐分数')
            plt.title(f"{username}的推荐分数分布")

            # 4. 新颖性分析
            plt.subplot(2, 2, 4)

            # 获取用户浏览过的地点ID集合
            user_locations = set(user_history.keys())

            # 计算各算法推荐的新地点比例
            cf_novelty = sum(1 for loc_id, _ in collaborative_recs if loc_id not in user_locations) / max(1, len(collaborative_recs))
            cb_novelty = sum(1 for loc_id, _ in content_recs if loc_id not in user_locations) / max(1, len(content_recs))
            hybrid_novelty = sum(1 for loc_id, _ in hybrid_recs if loc_id not in user_locations) / max(1, len(hybrid_recs))

            # 绘制条形图
            plt.bar(['协同过滤', '基于内容', '混合推荐'], [cf_novelty, cb_novelty, hybrid_novelty])
            plt.ylabel('新颖性（推荐新地点的比例）')
            plt.title(f"{username}的推荐新颖性分析")

            # 调整布局
            plt.tight_layout()

            # 保存或显示图表
            if output_file:
                plt.savefig(output_file)
                print(f"可视化结果已保存到 {output_file}")
            else:
                plt.show()
    except Exception as e:
        import traceback
        print(f"可视化推荐结果时出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    try:
        # 解析命令行参数
        args = parse_args()

        # 设置随机数种子
        random.seed(args.seed)

        print(f"开始测试景点推荐系统...")
        print(f"配置: 用户数={args.users}, 地点数={args.locations}, 详细输出={args.verbose}, 可视化={args.visual}")

        # 创建测试应用
        app = create_test_app()

        # 添加测试用户
        users = add_test_users(app)

        # 如果指定了清空浏览历史，则清空
        if args.clear_history:
            print("清空现有浏览历史...")
            clear_browse_history(app)

        # 添加浏览历史数据
        add_browse_history(app, users, max_locations=args.locations)

        # 如果指定了测试用户，则只测试该用户
        if args.test_user is not None:
            test_users = [user for user in users if user.user_id == args.test_user]
            if not test_users:
                print(f"错误：找不到ID为{args.test_user}的用户")
                sys.exit(1)
        else:
            # 随机选择几个用户进行测试
            test_users = random.sample(users, min(5, len(users)))

        # 根据指定的算法运行测试
        if args.algorithm in ['collaborative', 'all']:
            print("\n===== 测试协同过滤推荐 =====")
            for user in test_users:
                test_collaborative_filtering(app, user.user_id, evaluate_features=args.evaluate_features)

        if args.algorithm in ['content', 'all']:
            print("\n===== 测试基于内容的推荐 =====")
            for user in test_users:
                test_content_based(app, user.user_id)

        if args.algorithm in ['hybrid', 'all']:
            print("\n===== 测试混合推荐 =====")
            for user in test_users:
                test_hybrid_recommendations(app, user.user_id, verbose=args.verbose)

        # 如果启用了可视化，为每个测试用户生成可视化结果
        if args.visual:
            print("\n===== 生成可视化结果 =====")
            for user in test_users:
                with app.app_context():
                    # 获取推荐系统
                    recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

                    # 获取各种推荐结果
                    collaborative_recs = recommendation_system.get_collaborative_filtering_recommendations(user.user_id, limit=10)
                    content_recs = recommendation_system.get_content_based_recommendations(user.user_id, limit=10)
                    hybrid_recs = recommendation_system.get_hybrid_recommendations(user.user_id, limit=10)

                    # 生成可视化结果
                    output_file = f"recommendation_analysis_user_{user.user_id}.png"
                    visualize_recommendations(app, user.user_id, collaborative_recs, content_recs, hybrid_recs, output_file)

        print("\n测试完成！")
    except Exception as e:
        import traceback
        print(f"\n测试过程中发生错误: {str(e)}")
        traceback.print_exc()
        print("\n请检查数据库连接和用户数据是否正确。")
