/* place-search.wxss */

.container {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.search-box {
  width: 100%;
  display: flex;
  margin-bottom: 30rpx;
}

.search-box input {
  flex: 1;
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 160rpx;
  height: 80rpx;
  background-color: #409EFF;
  color: white;
  font-size: 28rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.filter-section {
  margin-top: 100rpx;
  text-align: center;
}

.filter-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-desc {
  font-size: 28rpx;
  color: #666;
}
