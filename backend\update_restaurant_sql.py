"""
更新restaurant表的SQL语句，添加image_url字段
"""
import re

# 读取SQL文件
with open('database/study_tour_system.sql', 'r', encoding='utf-8') as f:
    sql_content = f.read()

# 定义正则表达式模式，匹配餐馆的INSERT语句
pattern = r'INSERT INTO `restaurant` \(`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name\(price\)`, `dishes_name1\(price\)`, `dishes_name2\(price\)`, `average_price_perperson`, `x`, `y`\) VALUES \((\d+), \'([^\']+)\', \'([^\']+)\', (\d+), ([0-9.]+), (\d+), \'([^\']*)\', \'([^\']*)\', \'([^\']*)\', ([0-9.]+), (-?\d+), (-?\d+)\);'

# 替换函数
def replace_insert(match):
    id = match.group(1)
    name = match.group(2)
    cuisine_type = match.group(3)
    popularity = match.group(4)
    evaluation = match.group(5)
    number_of_view = match.group(6)
    dishes1 = match.group(7).replace("'", "''")  # 处理单引号
    dishes2 = match.group(8).replace("'", "''")  # 处理单引号
    dishes3 = match.group(9).replace("'", "''") if match.group(9) else ''  # 处理单引号和NULL值
    avg_price = match.group(10)
    x = match.group(11)
    y = match.group(12)
    
    # 构建新的INSERT语句，包含image_url字段
    return f"INSERT INTO `restaurant` (`id`, `name`, `cuisine_type`, `popularity`, `evaluation`, `number_of_view`, `dishes_name(price)`, `dishes_name1(price)`, `dishes_name2(price)`, `average_price_perperson`, `x`, `y`, `image_url`) VALUES ({id}, '{name}', '{cuisine_type}', {popularity}, {evaluation}, {number_of_view}, '{dishes1}', '{dishes2}', '{dishes3}', {avg_price}, {x}, {y}, '/uploads/restaurants/{name}.jpg');"

# 使用正则表达式替换所有匹配项
updated_sql = re.sub(pattern, replace_insert, sql_content)

# 写回SQL文件
with open('database/study_tour_system.sql', 'w', encoding='utf-8') as f:
    f.write(updated_sql)

print('SQL文件更新完成！')
