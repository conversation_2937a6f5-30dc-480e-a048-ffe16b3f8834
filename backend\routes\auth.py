from flask import Blueprint, request, jsonify
from models.user import User
from utils.database import db
import time
from utils.response import success, error, unauthorized

auth_bp = Blueprint('auth', __name__)

# 注册新用户
@auth_bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    try:
        # 检查必填字段
        if not data or not data.get('username') or not data.get('password') or not data.get('email'):
            return error('Missing required fields')

        # 用户名已经存在
        if User.query.filter_by(username=data['username']).first():
            return error('Username already exists')

        # 邮箱已经存在
        if User.query.filter_by(email=data['email']).first():
            return error('Email already exists')

        # 添加密码确认逻辑
        if data.get('password') != data.get('confirmPassword'):
            return error('Passwords do not match')

        # 创建新用户
        user = User(
            username=data['username'],
            email=data['email'],
            avatar=data.get('avatar', 'default_avatar.jpg')  # 使用默认头像
        )
        user.set_password(data['password'])

        db.session.add(user)
        db.session.commit()

        # 返回包含token的响应
        token = f"user-{user.user_id}-{int(time.time())}"
        return success({
            'user': {
                'user_id': user.user_id,
                'username': user.username,
                'email': user.email,
                'avatar': user.avatar
            },
            'token': token
        }, 'User registered successfully')

    except Exception as e:
        print('注册错误:', str(e))  # 后端日志
        return error(str(e))

# 登录
@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    if not data or not data.get('username') or not data.get('password'):
        return error('Missing username or password')

    user = User.query.filter_by(username=data['username']).first()
    if not user or not user.check_password(data['password']):
        return unauthorized('Invalid username or password')

    # 生成简单token (实际项目应使用JWT)
    token = f"user-{user.user_id}-{int(time.time())}"

    return success({
        'user': {
            'user_id': user.user_id,
            'username': user.username,
            'email': user.email,
            'avatar': user.avatar
        },
        'token': token
    }, 'Login successful')

