/* route-plan.wxss */

.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: #409EFF;
  padding-top: env(safe-area-inset-top);
}

.nav-bar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back-icon {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: bold;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.nav-placeholder {
  width: 60rpx;
}

/* 地图容器 */
.map-container {
  flex: 1;
  width: 100%;
  height: calc(100vh - 44px - env(safe-area-inset-top));
  min-height: 400rpx;
  margin-top: calc(44px + env(safe-area-inset-top));
  background-color: #f0f0f0; /* 添加背景色以便调试 */
  position: relative;
}

/* 控制面板 */
.control-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 45vh;
  overflow-y: auto;
  z-index: 1000;
}

/* 区域标题 */
.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  padding: 0 30rpx;
}

/* 地点选择区域 */
.location-section {
  padding: 20rpx 0 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-item {
  display: flex;
  align-items: flex-start;
  padding: 15rpx 30rpx;
  margin-bottom: 8rpx;
}

.location-label {
  width: 100rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  margin-top: 20rpx;
}

.location-input {
  flex: 1;
}

.autocomplete-container {
  position: relative;
}

.location-input-field {
  width: 100%;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #e5e5e5;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 1.4;
}

.location-input-field:focus {
  border-color: #409EFF;
  background: #ffffff;
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1rpx solid #e5e5e5;
  border-top: none;
  border-radius: 0 0 10rpx 10rpx;
  max-height: 300rpx;
  overflow-y: auto;
  z-index: 1001;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
  background: #ffffff;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: #f8f9fa;
}

/* 途径点区域 */
.waypoints-section {
  margin-top: 20rpx;
  padding: 0 30rpx;
}

.waypoints-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.waypoint-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.waypoint-name {
  font-size: 26rpx;
  color: #333;
}

.waypoint-remove {
  font-size: 24rpx;
  color: #ff4444;
  padding: 5rpx 10rpx;
}

.add-waypoint-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  margin: 20rpx 30rpx 0;
  background: #f8f9fa;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.add-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

/* 策略选择区域 */
.strategy-section {
  padding: 20rpx 0 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.transport-modes {
  display: flex;
  padding: 0 30rpx;
  margin-bottom: 15rpx;
}

.mode-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  margin-right: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}

.mode-btn:last-child {
  margin-right: 0;
}

.mode-btn.active {
  background: #409EFF;
  color: #ffffff;
}

.strategy-options {
  display: flex;
  padding: 0 30rpx;
}

.strategy-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  margin-right: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
}

.strategy-btn:last-child {
  margin-right: 0;
}

.strategy-btn.active {
  background: #67C23A;
  color: #ffffff;
}

/* 规划按钮区域 */
.plan-section {
  padding: 20rpx 30rpx;
}

.plan-btn {
  width: 100%;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.plan-btn.enabled {
  background: #409EFF;
  color: #ffffff;
}

.plan-btn.disabled {
  background: #f0f0f0;
  color: #999;
}

/* 路线信息区域 */
.route-info {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.route-summary {
  padding: 0 30rpx;
  margin-bottom: 15rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 路径详情 */
.route-details {
  padding: 0 30rpx;
}

.details-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-from,
.detail-to {
  font-size: 26rpx;
  color: #333;
}

.detail-arrow {
  margin: 0 15rpx;
  font-size: 24rpx;
  color: #999;
}

.detail-info {
  margin-left: auto;
  text-align: right;
}

.detail-distance,
.detail-time {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 途径点选择弹窗 */
.waypoint-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.waypoint-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.waypoint-search-container {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.waypoint-search-input {
  width: 100%;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #e5e5e5;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 1.4;
}

.waypoint-search-input:focus {
  border-color: #409EFF;
  background: #ffffff;
}

.location-list {
  flex: 1;
  max-height: 400rpx;
}

.location-option {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
}

.location-option:last-child {
  border-bottom: none;
}

.location-option:active {
  background: #f8f9fa;
}

.no-results {
  padding: 60rpx 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}

.modal-actions {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn {
  width: 100%;
  padding: 25rpx;
  background: #f8f9fa;
  color: #666;
  border: none;
  border-radius: 15rpx;
  font-size: 28rpx;
}
