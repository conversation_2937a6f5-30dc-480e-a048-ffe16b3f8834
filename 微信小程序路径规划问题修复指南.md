# 微信小程序路径规划问题修复指南

## 问题描述
微信小程序端路径规划部分无法选择起点和终点，也无法在地图上标点，显示地点加载失败。

## 根本原因
1. **网络配置问题**：小程序无法访问 `localhost`
2. **CORS配置不完整**：后端未正确配置跨域访问
3. **服务器绑定问题**：后端只绑定到 `127.0.0.1` 而非所有接口

## 修复步骤

### 第一步：获取本机IP地址

#### 方法1：使用提供的脚本
双击运行项目根目录下的 `get_ip.bat` 文件，它会自动显示您的IP地址。

#### 方法2：手动获取
**Windows:**
```cmd
ipconfig
```
找到 "IPv4 地址"，例如：*************

**Mac/Linux:**
```bash
ifconfig
```
找到 `inet` 地址

### 第二步：配置小程序API地址

编辑文件：`Wechat_miniP/miniprogram/config/api.ts`

将第9行的IP地址替换为您的实际IP：
```typescript
API_BASE_URL: 'http://您的IP地址:5000/api',  // 例如：http://*************:5000/api
```

### 第三步：启动后端服务

1. 确保在虚拟环境中：
```bash
cd backend
.\venv\Scripts\Activate.ps1  # Windows PowerShell
# 或
source venv/bin/activate     # Mac/Linux
```

2. 启动服务：
```bash
python app.py
```

确保看到类似输出：
```
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:5000
* Running on http://*************:5000
```

### 第四步：配置微信开发者工具

1. 打开微信开发者工具
2. 点击右上角"详情"
3. 在"本地设置"中勾选：
   - "不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"
   - "不校验 Secure 域名"

### 第五步：测试连接

1. 在微信开发者工具中打开小程序
2. 进入"路线"页面
3. 查看控制台是否有网络请求
4. 检查是否成功加载地点数据

## 验证修复

### 成功标志：
1. 控制台显示："API连接检查完成"
2. 显示："已加载X个地点"
3. 起点和终点选择器中有地点选项
4. 地图上显示地点标记

### 如果仍然失败：

#### 检查网络连接
在浏览器中访问：`http://您的IP地址:5000/api/path/vertices`
应该返回JSON格式的地点数据。

#### 检查防火墙
确保Windows防火墙允许5000端口的入站连接。

#### 检查后端日志
查看后端控制台输出，确认是否收到请求。

## 常见错误及解决方案

### 错误1：连接超时
**原因**：IP地址错误或防火墙阻止
**解决**：
- 重新获取正确的IP地址
- 检查防火墙设置
- 确保手机/电脑在同一网络

### 错误2：404错误
**原因**：API路径错误
**解决**：
- 确认后端服务正常启动
- 检查API路径是否正确

### 错误3：CORS错误
**原因**：跨域配置问题
**解决**：已在后端修复，重启后端服务

### 错误4：数据格式错误
**原因**：数据库中没有地点数据
**解决**：
- 检查数据库连接
- 确认数据库中有地点数据

## 生产环境部署

在生产环境中需要：
1. 使用HTTPS协议
2. 配置真实域名
3. 在微信公众平台配置合法域名
4. 移除开发者工具的域名校验豁免

## 技术支持

如果问题仍然存在，请提供：
1. 微信开发者工具控制台的完整错误信息
2. 后端服务器的日志输出
3. 网络请求的详细信息（在开发者工具的Network面板中查看）
