<template>
  <div class="search-page">
    <div class="search-container">
      <!-- 查询模式按钮右上角浮动 -->
      <!-- 只修改查询模式按钮的位置 -->
      <div class="search-mode-float">
        <el-radio-group v-model="searchMode" @change="handleModeChange" class="modern-radio-group small-radio-group">
          <el-radio-button label="name">名称查询</el-radio-button>
          <el-radio-button label="type">类型查询</el-radio-button>
        </el-radio-group>
      </div>
      <div class="form-row dual-column align-center">
        <!-- 起始地点 -->
        <div class="input-card">
          <label class="form-label light-label">
            <el-icon class="label-icon"><Location /></el-icon>
            起始地点
          </label>
          <div class="input-icon-wrapper">
            <el-icon class="input-icon"><MapLocation /></el-icon>
            <el-autocomplete
              v-model="startLocation"
              class="modern-input"
              :fetch-suggestions="queryLocationSuggestions"
              placeholder="输入起始地点"
              @select="handleStartSelect"
              clearable
            />
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="input-card">
          <label class="form-label light-label">
            <el-icon class="label-icon"><Flag /></el-icon>
            目的地
          </label>
          <div class="input-icon-wrapper">
            <el-icon class="input-icon"><Position /></el-icon>
            <el-autocomplete
              v-if="searchMode === 'name'"
              v-model="searchKeyword"
              class="modern-input"
              :fetch-suggestions="queryNameSuggestions"
              placeholder="输入地点名称"
              @select="handleNameSelect"
              clearable
            />
            <el-select
              v-else
              v-model="selectedType"
              class="modern-select elegant-select"
              placeholder="选择场所类型"
              @change="handleTypeChange"
            >
              <el-option
                v-for="type in typeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 距离范围和搜索按钮同一行 -->
      <div class="form-row slider-row">
        <label class="form-label light-label">距离范围（米）</label>
        <div class="slider-btn-group">
          <div class="slider-container modern-slider">
            <el-slider v-model="searchDistance" :min="100" :max="5000" :step="100" class="short-slider" />
            <div class="distance-display">{{ searchDistance }} 米</div>
          </div>
          <el-button class="search-btn" type="primary" @click="handleSearch" :loading="loading">
            <el-icon class="btn-icon"><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>
    </div>

    <div class="place-grid">
      <div v-for="place in results" :key="place.vertex_id" class="place-card fancy-card">
        <div class="card-header">
          <el-icon class="pin-icon"><LocationFilled /></el-icon>
          <h3 class="place-title">{{ place.name }}</h3>
          <span class="type-tag" :style="tagStyle(place.type)">{{ getTypeLabel(place.type) }}</span>
        </div>
        <div class="metric-group">
          <div class="metric-item">
            <div class="metric-icon"><el-icon><Timer /></el-icon></div>
            <div class="metric-value">
              距离 <span class="number">{{ (place.path_distance)}}</span>
              <span class="unit">米</span>
              <span v-if="place.path_distance > 30000" class="exceed-tag">较远</span>
            </div>
          </div>
        </div>
        <div class="action-buttons">
          <button class="map-btn" @click="showOnMap(place)">
            <div class="btn-gradient"></div>
            <el-icon class="btn-icon"><MapLocation /></el-icon>
            <span class="btn-text">查看地图</span>
          </button>
        </div>
        <div class="card-footer-bar"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getSpotsByCriteria,
  getLocationSuggestions,
  searchSpotsByName
} from '@/api/path'  // 确保路径正确

import { useRouter } from 'vue-router' 
const router = useRouter() 
// 状态管理
const loading = ref(false)
const searchMode = ref('name')
const startLocation = ref('')
const startVertexId = ref(null)
const searchKeyword = ref('')
const selectedType = ref('')
const searchDistance = ref(1000)
const results = ref([])
const selectedPlace = ref(null) 
const typeOptions = ref([
  { label: '大门', value: 1 },
  { label: '路口', value: 2 },
  { label: '教学楼', value: 3 }, 
  { label: '功能楼', value: 4 }, 
  { label: '生活服务', value: 5 }, 
  { label: '停车场', value: 6 }, 
  { label: '餐饮', value: 7 }, 
  { label: '宿舍楼', value: 8 }, 
  { label: '体育场所', value: 9 },
  { label: '文化标识及其他', value: 10 },  
  { label: '交通设施', value: 11 }, 
])

const showOnMap = (place) => {
  // 跳转到路由页面，并传递必要的参数
  router.push({
    path: '/route',
    query: {
      startId: startVertexId.value,
      startName: startLocation.value,
      endId: place.vertex_id,
      endName: place.name
    }
  })
}

const queryLocationSuggestions = async (queryString, cb) => {
  if (!queryString || !queryString.trim()) {
    cb([])
    return
  }
  try {
    const response = await getLocationSuggestions(queryString)
    const suggestions = response.data.map(item => ({
      value: item.label,
      id: item.vertex_id
    }))
    cb(suggestions)
  } catch (error) {
    console.error('获取地点建议失败:', error)
    ElMessage.error('获取地点建议失败，请稍后重试')
    cb([])
  }
}

const handleStartSelect = (item) => {
  startVertexId.value = item.id
  startLocation.value = item.value
  console.log(startLocation)
  console.log("value", startLocation.value)
}
const queryNameSuggestions = async (queryString, cb) => {
  if (!queryString || !queryString.trim()) {
    cb([])
    return
  }
  try {
    const response = await searchSpotsByName({ name: queryString })
    const suggestions = response.data.map(item => ({
      value: item.name,
      id: item.vertex_id,
      ...item // 保留所有属性，方便后续直接用
    }))
    cb(suggestions)
  } catch (error) {
    ElMessage.error('获取名称建议失败，请稍后重试')
    cb([])
  }
}

const handleNameSelect = (item) => {
  searchKeyword.value = item.value
  selectedPlace.value = item // 只记录，不直接赋值给results
}
const handleSearch = async () => {
  // 验证逻辑
  if (!startVertexId.value) {
    ElMessage.warning('请先选择起始地点')
    return
  }
  if (searchMode.value === 'name') {
    if (!searchKeyword.value.trim()) {
      ElMessage.warning('请输入搜索名称')
      return
    }
    // 如果已经选中下拉项，调用后端接口查距离
    if (selectedPlace.value && selectedPlace.value.value === searchKeyword.value) {
      try {
        loading.value = true
        // 用关键词数组精准查找
        const params = {
          startVertexId: startVertexId.value,
          distance: searchDistance.value,
          keywords: [selectedPlace.value.value], // 关键点
          limit: 1
        }
        const response = await getSpotsByCriteria(params)
        if (response.data.nearby_spots && response.data.nearby_spots.length) {
          results.value = response.data.nearby_spots.map(spot => ({
            ...spot,
            path_distance: spot.path_distance ?? spot.distance, // 兼容不同字段
            typeLabel: getTypeLabel(spot.type)
          }))
        } else {
          results.value = []
          ElMessage.info('未找到匹配的场所')
        }
      } catch (error) {
        console.error('搜索失败:', error)
        ElMessage.error(error.message || '搜索请求失败，请检查网络')
      } finally {
        loading.value = false
      }
      return
    }
  }

  try {
    loading.value = true
    const params = {
      startVertexId: startVertexId.value,
      distance: searchDistance.value,
      typeVal: searchMode.value === 'type' ? selectedType.value : undefined,
      keywords: [],
      limit: 15
    }
    const response = await getSpotsByCriteria(params)
    results.value = response.data.nearby_spots || []
    if (response.data.nearby_spots && response.data.nearby_spots.length) {
      results.value = response.data.nearby_spots.map(spot => ({
        ...spot,
        typeLabel: getTypeLabel(spot.type)
      }))
    } else {
      results.value = []
      ElMessage.info('未找到匹配的场所')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error(error.message || '搜索请求失败，请检查网络')
  } finally {
    loading.value = false
  }
}


const tagStyle = (type) => {
  const colorMap = {
    2: '#6c5ce7',
    3: '#2e7d32',
    4: '#1976d2'
  }
  return `background: ${colorMap[type] || '#aaa'}; color: white;`
}

// 获取类型标签文本
const getTypeLabel = (type) => {
  return typeOptions.value.find(t => t.value === type)?.label || '未知类型'
}
 

const handleTypeChange = (value) => {
  selectedType.value = value
}

const handleModeChange = () => {
  searchKeyword.value = ''
  selectedType.value = ''
  selectedPlace.value = null // 切换模式时清空
} 
</script>
 
<style scoped>
.search-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 恢复经典搜索卡片样式 */
.search-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
  padding: 2rem;
  margin: 2rem auto;
  max-width: 1000px; /* 从800px增加到1000px */
  position: relative; /* 确保相对定位 */
}

/* 输入区域卡片化美化 */
.input-card {
  background: #f6f8ff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.06);
  padding: 1.1rem 1.2rem 0.7rem 1.2rem;
  margin-bottom: 0.5rem;
  margin-right: 1.2rem;
  display: flex;
  flex-direction: column;
  min-width: 260px;
  max-width: 600px;
}

/* 保证输入框和图标对齐 */
.input-icon-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  background: #fff;
  border-radius: 8px;
  padding: 0.2rem 0.7rem 0.2rem 0.3rem;
  box-shadow: 0 1px 4px rgba(108, 92, 231, 0.04);
  border: 1px solid #e0e7ff;
}
.input-icon {
  color: #6c5ce7;
  font-size: 1.3rem;
  margin-right: 8px;
  margin-left: 2px;
  flex-shrink: 0;
}
.input-card .modern-input,
.input-card .modern-select {
  flex: 1;
}
.input-card .el-autocomplete,
.input-card .el-select {
  width: 100%;
}

/* label图标样式 */
.label-icon {
  color: #6c5ce7;
  font-size: 1.1rem;
  margin-right: 6px;
  vertical-align: middle;
}

/* 距离滑动条和搜索按钮同一行 */
.slider-row {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}
.slider-btn-group {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  flex: 1;
}
.slider-container.modern-slider {
  flex: 1;
  min-width: 160px;
  max-width: 320px;
  display: flex;
  align-items: center;
  gap: 0.7rem;
}
.short-slider {
  width: 140px !important;
  min-width: 100px;
  max-width: 180px;
}
.distance-display {
  font-size: 0.98rem;
  color: #6c5ce7;
  font-weight: 500;
  margin-left: 8px;
  white-space: nowrap;
}
.search-btn {
  min-width: 100px;
  margin-left: 10px;
  background: linear-gradient(135deg, #6c5ce7 0%, #8075e5 100%);
  border: none;
  border-radius: 10px;
  padding: 10px 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(108, 92, 231, 0.3);
  background: linear-gradient(135deg, #7d6de8 0%, #9186e9 100%);
}

.search-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.2);
}

.search-btn .btn-icon {
  margin-right: 6px;
  font-size: 16px;
}

.search-mode-float {
  position: absolute;
  top: 2rem;   
  right: 2rem;
  z-index: 2;
  display: inline-flex; /* 改为inline-flex */
  align-items: center;
  justify-content: flex-end;
  width: auto; /* 自适应宽度 */
}

.small-radio-group {
  display: inline-flex; /* 确保按钮组是inline */
  flex-wrap: nowrap; /* 禁止换行 */
}

.small-radio-group :deep(.el-radio-button__inner) {
  padding: 0.1rem 0.4rem; /* 更小的内边距 */
  font-size: 0.75rem; /* 更小的字体 */
  border-radius: 4px !important;
  height: 22px; /* 更小的高度 */
  line-height: 22px; /* 设置行高等于高度，确保文字垂直居中 */
  white-space: nowrap;
  min-width: 0; /* 移除最小宽度限制 */
  display: flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.small-radio-group :deep(.el-radio-button) {
  margin-right: 0; /* 移除右边距 */
  --el-radio-button-checked-bg-color: #6c5ce7;
  --el-radio-button-checked-text-color: white;
  --el-radio-button-checked-border-color: #6c5ce7;
}
 


/* label颜色更浅 */
.form-label.light-label {
  color: #b0b6c3 !important;
  font-weight: 500;
  font-size: 0.97rem;
}

/* 其余样式保持不变 ... */
.form-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #2d3436;
  font-size: 0.95rem;
  margin-bottom: 0.75rem;
}

/* 查询模式按钮靠右 */
.dual-column.align-center {
  align-items: center;
}
.search-mode.right-align {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

/* 现代单选按钮组美化 */
.modern-radio-group :deep(.el-radio-button) {
  --el-radio-button-checked-bg-color: #6c5ce7;
  --el-radio-button-checked-text-color: white;
  .el-radio-button__inner {
    border-radius: 8px !important;
    padding: 0.7rem 1.2rem;
    transition: all 0.2s ease;
    border: 2px solid #e0e0e0;
    font-weight: 500;
    font-size: 1rem;
    background: #f8f9fa;
  }
}

/* 结果卡片美化（保持不变） */
.place-card.fancy-card {
  background: linear-gradient(135deg, #f8f9fa 60%, #e0e7ff 100%);
  border-radius: 20px;
  padding: 22px 24px 18px 24px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(108, 92, 231, 0.10), 0 1.5px 6px rgba(0,0,0,0.04);
  border: 1.5px solid #e0e7ff;
  transition: box-shadow 0.3s, transform 0.3s;
  min-height: 170px;
}
.place-card.fancy-card:hover {
  transform: translateY(-6px) scale(1.025);
  box-shadow: 0 16px 32px rgba(108, 92, 231, 0.18), 0 2px 8px rgba(0,0,0,0.08);
}
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  position: relative;
}
.pin-icon {
  color: #ff6b6b;
  font-size: 22px;
  margin-right: 10px;
  filter: drop-shadow(0 2px 4px rgba(108, 92, 231, 0.08));
}
.place-title {
  font-size: 20px;
  font-weight: 700;
  color: #2d3436;
  margin: 0;
  flex: 1;
  letter-spacing: 0.5px;
}
.type-tag {
  font-size: 13px;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e7ff;
  background: #6c5ce7;
  color: #fff;
  margin-left: 10px;
  transition: background 0.2s;
}
.type-tag:hover {
  background: #857ceb;
}
.metric-group {
  background: rgba(247, 250, 252, 0.85);
  border-radius: 14px;
  padding: 1.1rem 1.2rem;
  margin: 1.1rem 0 0.7rem 0;
  border: 1px solid #e0e7ff;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.04);
}
.metric-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.metric-item:last-child {
  margin-bottom: 0;
}
.metric-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(108, 92, 231, 0.13);
  border-radius: 8px;
  color: #6c5ce7;
  margin-right: 12px;
  font-size: 1.2rem;
}
.metric-value {
  font-size: 15px;
  color: #636e72;
}
.number {
  font-weight: 700;
  color: #2d3436;
  margin: 0 4px;
  font-size: 1.1em;
}
.unit {
  font-size: 0.95em;
}
.exceed-tag {
  background: #fff3cd;
  color: #856404;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: 8px;
}
.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}
.map-btn, .route-btn {
  position: relative;
  height: 40px;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  padding: 0 18px;
  font-size: 15px;
}
.map-btn {
  color: #1976d2;
}
.route-btn {
  color: #2e7d32;
}
.btn-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}
.map-btn .btn-gradient {
  background: linear-gradient(135deg, #1976d2 0%, #64b5f6 100%);
}
.route-btn .btn-gradient {
  background: linear-gradient(135deg, #2e7d32 0%, #81c784 100%);
}
.btn-icon {
  font-size: 18px;
  margin-right: 8px;
  position: relative;
  z-index: 1;
}
.btn-text {
  font-weight: 500;
  position: relative;
  z-index: 1;
}
.map-btn:hover, .route-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.10);
}
.map-btn:hover .btn-gradient,
.route-btn:hover .btn-gradient {
  opacity: 0.18;
}
/* 卡片底部渐变装饰条 */
.card-footer-bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 6px;
  background: linear-gradient(90deg, #6c5ce7 0%, #64b5f6 100%);
  opacity: 0.18;
  border-radius: 0 0 20px 20px;
}
.place-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 28px;
  padding: 15px 0;
}
@media (max-width: 768px) {
  .form-row.dual-column {
    grid-template-columns: 1fr;
  }
  .search-container {
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 14px;
    top: 0;
  }
  .place-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
