@echo off
echo ========================================
echo 微信小程序API代理服务器启动脚本
echo ========================================
echo.

echo 正在检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js环境检查通过
echo.

echo 正在安装依赖包...
npm install

if %errorlevel% neq 0 (
    echo 错误：依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 依赖包安装完成
echo.

echo ========================================
echo 启动代理服务器...
echo ========================================
echo.
echo 代理服务器将在以下地址运行：
echo http://localhost:3000
echo http://*************:3000
echo.
echo 请确保：
echo 1. 后端服务已启动（http://*************:5000）
echo 2. 小程序配置中 API_STRATEGY = 1
echo.
echo 按 Ctrl+C 停止服务器
echo.

npm start
