<template>
  <el-card class="diary-card">
    <div class="diary-header">
      <div class="author-info">
        <img :src="authorAvatar" class="author-avatar">
        <div>
          <h3 class="author-name">{{ diary.author.username }}</h3>
          <span class="post-date">{{ formatDate(diary.createTime) }}</span>
        </div>
      </div>
    </div>

    <div class="diary-content">
      <h2 class="diary-title">{{ diary.title }}</h2>

      <!-- 标签区域 -->
      <div class="diary-tags">
        <!-- 地点标签 -->
        <el-tag
          v-if="diary.location"
          size="small"
          type="warning"
          effect="plain"
          class="diary-tag location-tag"
        >
          <el-icon><Location /></el-icon>
          {{ diary.location }}
        </el-tag>
        <!-- 其他标签 -->
        <el-tag
          v-for="(tag, index) in diary.tags"
          :key="index"
          size="small"
          type="info"
          effect="plain"
          class="diary-tag"
        >
          {{ tag }}
        </el-tag>
      </div>

      <p class="diary-excerpt" v-if="diary.content">{{ stripHtml(diary.content).substring(0, 150) }}{{ stripHtml(diary.content).length > 150 ? '...' : '' }}</p>
      <p class="diary-excerpt" v-else>暂无内容</p>

      <!-- 图片展示区域 -->
      <div class="diary-media" v-if="diary.images && diary.images.length">
        <img
          v-for="(img, index) in diary.images.slice(0, 2)"
          :key="index"
          :src="getFullImageUrl(img)"
          class="diary-image"
          @error="handleImageError"
        >
        <div v-if="diary.images.length > 2" class="more-images">
          <span>+{{ diary.images.length - 2 }}</span>
        </div>
      </div>

      <!-- 视频展示区域 -->
      <div class="diary-media video-container" v-if="diary.video">
        <video
          controls
          class="diary-video"
          :src="getFullVideoUrl(diary.video)"
          @error="handleVideoError"
        ></video>
      </div>
    </div>

    <div class="diary-footer">
      <div class="diary-stats">
        <div class="stats-row">
          <div class="stat-item" @click="$emit('like', diary)">
            <el-icon class="up-icon" :class="{ 'liked-icon': diary.isLiked }"><CaretTop /></el-icon>
            <span class="stat-count">{{ likesCount }}</span>
            <span class="stat-label">点赞</span>
          </div>
          <div class="stat-item" @click="$emit('favorite', diary)">
            <el-icon :class="{ 'favorited-icon': diary.isFavorited }"><Star /></el-icon>
            <span class="stat-count">{{ favoritesCount }}</span>
            <span class="stat-label">收藏</span>
          </div>
          <div class="stat-item">
            <el-icon><Message /></el-icon>
            <span class="stat-count">{{ commentsCount }}</span>
            <span class="stat-label">评论</span>
          </div>
          <div class="stat-item">
            <el-icon><View /></el-icon>
            <span class="stat-count">{{ viewsCount }}</span>
            <span class="stat-label">浏览</span>
          </div>
          <div class="heat-indicator" v-if="diary.heat">
            <span>热度</span>
            <el-progress class="heat-progress" :percentage="diary.heat" :stroke-width="8" :show-text="false" status="warning"></el-progress>
          </div>
        </div>
      </div>
      <div class="rating-display">
        <el-rate
          :model-value="diary.avgRating || 0"
          disabled
          text-color="#ff9900"
        ></el-rate>
        <el-button type="primary" class="read-more-btn" @click="$emit('view', diary)">
          阅读全文
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue';
import { Message, Star, View, CaretTop, Location } from '@element-plus/icons-vue';
import defaultAvatar from '@/assets/belog.jpg';

const props = defineProps({
  diary: {
    type: Object,
    required: true
  }
});

defineEmits(['like', 'favorite', 'view']);

// 计算属性，确保显示正确的数值
const likesCount = computed(() => {
  const count = Number(props.diary.likes || props.diary.likes_count || 0);
  console.log(`DiaryCard - 文章ID: ${props.diary.id} 的点赞数: ${count}`);
  return count;
});

const favoritesCount = computed(() => {
  const count = Number(props.diary.favorites || props.diary.favorites_count || 0);
  console.log(`DiaryCard - 文章ID: ${props.diary.id} 的收藏数: ${count}`);
  return count;
});

// 计算作者头像URL，确保始终有有效的头像
const authorAvatar = computed(() => {
  if (!props.diary.author) return defaultAvatar;

  const avatar = props.diary.author.avatar;
  if (!avatar || avatar === '' || avatar === null || avatar === undefined || avatar === 'default_avatar.jpg') {
    return defaultAvatar;
  }

  // 如果是完整URL或数据URL，直接返回
  if (avatar.startsWith('http') || avatar.startsWith('data:')) {
    return avatar;
  }

  // 如果是相对路径，添加后端基础URL
  if (avatar.startsWith('/uploads/')) {
    return `http://localhost:5000${avatar}`;
  }

  // 其他情况返回默认头像
  return defaultAvatar;
});

const commentsCount = computed(() => {
  const count = Number(props.diary.commentsCount || props.diary.comments_count || 0);
  console.log(`DiaryCard - 文章ID: ${props.diary.id} 的评论数: ${count}`);
  return count;
});

const viewsCount = computed(() => {
  const count = Number(props.diary.views || props.diary.popularity || 0);
  console.log(`DiaryCard - 文章ID: ${props.diary.id} 的浏览数: ${count}`);
  return count;
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '未知时间';

  let date;

  // 处理不同类型的时间输入
  if (typeof timestamp === 'string') {
    // 如果是ISO格式的字符串
    if (timestamp.includes('T') || timestamp.includes('Z') || timestamp.includes('+')) {
      date = new Date(timestamp);
    } else {
      // 如果是普通字符串，尝试解析
      date = new Date(timestamp);
    }
  } else if (typeof timestamp === 'number') {
    // 如果是时间戳
    date = new Date(timestamp);
  } else {
    // 其他情况，直接尝试创建Date对象
    date = new Date(timestamp);
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.error('无效的时间戳:', timestamp);
    return '未知时间';
  }

  // 使用toLocaleString获取本地时间格式
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  };

  // 返回本地时间格式的日期和时间
  return date.toLocaleString('zh-CN', options).replace(/\//g, '-');
};

// 去除HTML标签
const stripHtml = (html) => {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '');
};

// 获取完整图片URL
const getFullImageUrl = (url) => {
  if (!url) return '';
  if (url.startsWith('http')) return url;
  if (url.startsWith('data:')) return url;
  return `http://localhost:5000${url.startsWith('/') ? '' : '/'}${url}`;
};

// 获取完整视频URL
const getFullVideoUrl = (url) => {
  if (!url) return '';
  if (url.startsWith('http')) return url;
  if (url.startsWith('data:')) return url;
  return `http://localhost:5000${url.startsWith('/') ? '' : '/'}${url}`;
};

// 处理图片加载错误
const handleImageError = (e) => {
  console.error('图片加载失败:', e);
  e.target.src = defaultAvatar; // 使用导入的默认头像
};

// 处理视频加载错误
const handleVideoError = (e) => {
  console.error('视频加载失败:', e);
  const videoContainer = e.target.parentNode;
  if (videoContainer) {
    const errorMsg = document.createElement('div');
    errorMsg.className = 'video-error-message';
    errorMsg.textContent = '视频加载失败';
    videoContainer.appendChild(errorMsg);
    e.target.style.display = 'none';
  }
};


</script>

<style scoped>
.diary-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
}

.diary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.author-info {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 10px;
}

.author-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.post-date {
  font-size: 12px;
  color: #999;
}

.diary-title {
  margin: 0 0 10px;
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

.diary-tags {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.diary-tag {
  margin-right: 5px;
}

.location-tag {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.location-tag .el-icon {
  margin-right: 3px;
}

.diary-excerpt {
  margin: 0 0 15px;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.diary-media {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  position: relative;
}

.diary-image {
  width: calc(50% - 5px);
  height: 180px;
  object-fit: cover;
  border-radius: 8px;
}

.more-images {
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
}

.video-container {
  width: 100%;
  height: 250px;
  position: relative;
}

.diary-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.diary-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.diary-stats {
  display: flex;
  flex-direction: column;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 8px;
  border-radius: 15px;
  transition: background-color 0.2s;
}

.stat-item:hover {
  background-color: #f5f7fa;
}

.stat-count {
  margin: 0 5px;
  font-size: 14px;
  color: #606266;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.liked-icon {
  color: #f56c6c;
}

.favorited-icon {
  color: #e6a23c;
}

.heat-indicator {
  display: flex;
  align-items: center;
  margin-left: 15px;
}

.heat-indicator span {
  font-size: 12px;
  color: #909399;
  margin-right: 5px;
}

.heat-progress {
  width: 80px;
}

.rating-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.read-more-btn {
  font-size: 14px;
  padding: 8px 15px;
}

.video-error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
}
</style>
