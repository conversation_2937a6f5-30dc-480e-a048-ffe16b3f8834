<template>
  <div class="place-detail">
    <div v-if="loading" class="loading-container">
      <div class="loader"></div>
      <p>加载中...</p>
    </div>

    <div v-else-if="!place" class="error-container">
      <h2>未找到景区信息</h2>
      <button @click="$router.push('/search')" class="back-button">返回搜索</button>
    </div>

    <div v-else class="detail-container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <button @click="$router.push('/search')" class="back-button">
          <i class="fas fa-arrow-left"></i> 返回搜索
        </button>
      </div>

      <!-- 景区基本信息 -->
      <div class="place-header">
        <div class="place-image-container">
          <img :src="place.Img" :alt="place.name" class="place-main-image" />
        </div>
        <div class="place-info">
          <h1 class="place-name">{{ place.name }}</h1>
          <div class="place-meta">
            <div class="rating">
              <span class="stars">⭐ {{ place.rating }}</span>
              <span class="reviews">({{ place.reviews }} 条评价)</span>
            </div>
            <div class="category">
              <span class="category-label">分类:</span>
              <span class="category-value">{{ getCategoryName(place.category) }}</span>
            </div>
            <div class="address">
              <i class="fas fa-map-marker-alt"></i>
              <span>{{ place.address }}</span>
            </div>
          </div>
          <div class="tags">
            <span v-for="(tag, index) in place.tags" :key="index" class="tag">{{ tag }}</span>
          </div>
        </div>
      </div>

      <!-- 景区详细描述 -->
      <div class="place-description">
        <h2>景区介绍</h2>
        <p>{{ place.description || '暂无详细介绍' }}</p>
      </div>

      <!-- 景区设施信息 -->
      <div class="facilities-section">
        <h2>景区设施</h2>
        <div class="facilities-grid">
          <!-- 厕所信息 -->
          <div class="facility-card">
            <div class="facility-icon">🚻</div>
            <div class="facility-info">
              <h3>厕所</h3>
              <ul class="facility-list">
                <li v-for="(toilet, index) in place.facilities.toilets" :key="'toilet-'+index">
                  <span class="facility-name">{{ toilet.name }}</span>
                  <span class="facility-location">位置: {{ toilet.location }}</span>
                </li>
                <li v-if="!place.facilities.toilets || place.facilities.toilets.length === 0">
                  暂无厕所信息
                </li>
              </ul>
            </div>
          </div>

          <!-- 餐厅信息 -->
          <div class="facility-card">
            <div class="facility-icon">🍽️</div>
            <div class="facility-info">
              <h3>餐厅</h3>
              <ul class="facility-list">
                <li v-for="(restaurant, index) in place.facilities.restaurants" :key="'restaurant-'+index">
                  <span class="facility-name">{{ restaurant.name }}</span>
                  <span class="facility-location">位置: {{ restaurant.location }}</span>
                  <span class="facility-description">{{ restaurant.description }}</span>
                </li>
                <li v-if="!place.facilities.restaurants || place.facilities.restaurants.length === 0">
                  暂无餐厅信息
                </li>
              </ul>
            </div>
          </div>

          <!-- 休息区信息 -->
          <div class="facility-card">
            <div class="facility-icon">🪑</div>
            <div class="facility-info">
              <h3>休息区</h3>
              <ul class="facility-list">
                <li v-for="(restArea, index) in place.facilities.restAreas" :key="'restArea-'+index">
                  <span class="facility-name">{{ restArea.name }}</span>
                  <span class="facility-location">位置: {{ restArea.location }}</span>
                </li>
                <li v-if="!place.facilities.restAreas || place.facilities.restAreas.length === 0">
                  暂无休息区信息
                </li>
              </ul>
            </div>
          </div>

          <!-- 商店信息 -->
          <div class="facility-card">
            <div class="facility-icon">🛍️</div>
            <div class="facility-info">
              <h3>商店</h3>
              <ul class="facility-list">
                <li v-for="(shop, index) in place.facilities.shops" :key="'shop-'+index">
                  <span class="facility-name">{{ shop.name }}</span>
                  <span class="facility-location">位置: {{ shop.location }}</span>
                  <span class="facility-description">{{ shop.description }}</span>
                </li>
                <li v-if="!place.facilities.shops || place.facilities.shops.length === 0">
                  暂无商店信息
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 交通信息 -->
      <div class="transportation-section">
        <h2>交通信息</h2>
        <div class="transportation-info">
          <div v-if="place.transportation && place.transportation.length > 0">
            <div v-for="(item, index) in place.transportation" :key="'transport-'+index" class="transport-item">
              <h3>{{ item.type }}</h3>
              <p>{{ item.description }}</p>
            </div>
          </div>
          <div v-else class="no-data">暂无交通信息</div>
        </div>
      </div>

      <!-- 开放时间 -->
      <div class="opening-hours-section">
        <h2>开放时间</h2>
        <div class="opening-hours-info">
          <p v-if="place.openingHours">{{ place.openingHours }}</p>
          <p v-else class="no-data">暂无开放时间信息</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const place = ref(null)
const loading = ref(true)

// 模拟数据 - 实际应用中应该从API获取
const placesData = [
  {
    id: 1,
    name: '故宫博物院',
    address: '北京市东城区景山前街4号',
    category: 'culture',
    region: '北京',
    rating: 4.8,
    reviews: 1250,
    tags: ['历史', '免费', '室内'],
    Img: require('@/assets/forbidden_city.jpg'),
    description: '故宫博物院，旧称为紫禁城，是中国明清两代的皇家宫殿，位于北京中轴线的中心，是中国古代宫廷建筑之精华。故宫于明成祖永乐四年（1406年）开始建设，以南京故宫为蓝本营建，到永乐十八年（1420年）建成。是世界上现存规模最大、保存最为完整的木质结构古建筑之一。',
    openingHours: '4月1日-10月31日: 8:30-17:00，11月1日-次年3月31日: 8:30-16:30，周一闭馆（国家法定节假日除外）',
    facilities: {
      toilets: [
        { name: '东华门厕所', location: '东华门附近' },
        { name: '西华门厕所', location: '西华门附近' },
        { name: '神武门厕所', location: '神武门附近' }
      ],
      restaurants: [
        { name: '故宫角楼咖啡', location: '西北角楼', description: '提供咖啡、简餐等' },
        { name: '故宫冰窖餐厅', location: '御花园北侧', description: '提供中式简餐' }
      ],
      restAreas: [
        { name: '中央休息区', location: '太和殿广场东侧' },
        { name: '御花园休息区', location: '御花园内' }
      ],
      shops: [
        { name: '故宫博物院文创店', location: '午门附近', description: '销售故宫文创产品' },
        { name: '故宫书店', location: '神武门附近', description: '销售书籍、明信片等' }
      ]
    },
    transportation: [
      { type: '地铁', description: '1号线天安门东站下车，步行约10分钟可到达' },
      { type: '公交', description: '乘坐1、2、52、59、82、120路等公交车在天安门站下车' }
    ]
  },
  {
    id: 2,
    name: '外滩观光隧道',
    address: '上海市黄浦区中山东一路',
    category: 'sightseeing',
    region: '上海',
    rating: 4.7,
    reviews: 980,
    tags: ['夜景', '网红打卡', '江景'],
    Img: require('@/assets/waitan.jpg'),
    description: '外滩位于上海市中心黄浦区的黄浦江畔，是上海最具特色的旅游景点之一。这里集中了上海最丰富的近代城市建筑，被称为"万国建筑博览会"。外滩的精华是沿黄浦江一公里长的外滩建筑群，有着"外滩万国建筑博览群"之称，是中国近现代重要史迹及代表性建筑。',
    openingHours: '全天开放',
    facilities: {
      toilets: [
        { name: '外滩公共厕所', location: '外滩中心位置' },
        { name: '南京东路厕所', location: '南京东路与外滩交界处' }
      ],
      restaurants: [
        { name: '外滩三号餐厅', location: '外滩3号楼', description: '高档西餐' },
        { name: '外滩18号餐厅', location: '外滩18号楼', description: '中式料理' },
        { name: 'M on the Bund', location: '外滩5号楼', description: '欧式餐厅，可俯瞰外滩美景' }
      ],
      restAreas: [
        { name: '外滩观景平台', location: '外滩中心位置' }
      ],
      shops: [
        { name: '外滩纪念品商店', location: '外滩游客中心', description: '销售上海特色纪念品' }
      ]
    },
    transportation: [
      { type: '地铁', description: '乘坐地铁2号线或10号线在南京东路站下车，步行约10分钟可到达' },
      { type: '公交', description: '乘坐20、37、55、65、123等公交车在外滩站下车' }
    ]
  },
  {
    id: 3,
    name: '颐和园',
    address: '北京市海淀区新建宫门路19号',
    category: 'culture',
    region: '北京',
    rating: 4.9,
    reviews: 1560,
    tags: ['园林', '湖景', '历史'],
    Img: require('@/assets/Summer_Palace2.jpg'), // 使用相同图片作为替代
    description: '颐和园，中国清朝时期皇家园林，前身为清漪园，坐落在北京西郊，距城区15公里，占地约290公顷，与圆明园毗邻。它是以昆明湖、万寿山为基址，以杭州西湖为蓝本，汲取江南园林的设计手法而建成的一座大型山水园林，也是保存最完整的一座皇家行宫御苑，被誉为"皇家园林博物馆"。',
    openingHours: '4月1日-10月31日: 6:30-18:00，11月1日-次年3月31日: 7:00-17:00',
    facilities: {
      toilets: [
        { name: '东宫门厕所', location: '东宫门附近' },
        { name: '北宫门厕所', location: '北宫门附近' },
        { name: '昆明湖厕所', location: '昆明湖畔' }
      ],
      restaurants: [
        { name: '听鹂馆', location: '长廊北侧', description: '提供传统北京小吃' },
        { name: '荷花餐厅', location: '昆明湖畔', description: '湖景餐厅，提供中式简餐' }
      ],
      restAreas: [
        { name: '长廊休息区', location: '长廊中段' },
        { name: '佛香阁休息区', location: '佛香阁下' }
      ],
      shops: [
        { name: '颐和园纪念品商店', location: '东宫门附近', description: '销售颐和园特色纪念品' },
        { name: '传统工艺品店', location: '苏州街', description: '销售传统手工艺品' }
      ]
    },
    transportation: [
      { type: '地铁', description: '乘坐地铁4号线在北宫门站下车，步行约10分钟可到达' },
      { type: '公交', description: '乘坐303、330、331、346、375、384、634、696、718、801、808、817、826路公交车在颐和园站下车' }
    ]
  },
  {
    id: 4,
    name: '上海迪士尼乐园',
    address: '上海市浦东新区申迪西路753号',
    category: 'sightseeing',
    region: '上海',
    rating: 4.6,
    reviews: 2100,
    tags: ['主题乐园', '亲子', '游乐设施'],
    Img: require('@/assets/Disney_Shanghai.jpg'), // 使用相同图片作为替代
    description: '上海迪士尼度假区是中国内地首座迪士尼主题度假区，位于上海市浦东新区。度假区由上海迪士尼乐园、迪士尼小镇、星愿公园以及两间迪士尼主题酒店组成。上海迪士尼乐园于2016年6月16日正式开园，是中国第二个、中国内地第一个、亚洲第三个，世界第六个迪士尼主题公园。',
    openingHours: '具体开放时间请查看官网，一般为8:00-20:00，节假日会有所调整',
    facilities: {
      toilets: [
        { name: '米奇大道厕所', location: '米奇大道' },
        { name: '明日世界厕所', location: '明日世界区域' },
        { name: '梦幻世界厕所', location: '梦幻世界区域' },
        { name: '宝藏湾厕所', location: '宝藏湾区域' }
      ],
      restaurants: [
        { name: '皇家宴会厅', location: '奇幻童话城堡', description: '提供西式正餐，可与迪士尼公主共进午餐' },
        { name: '米奇厨师餐厅', location: '米奇大道', description: '提供自助餐' },
        { name: '巴巴萨巴', location: '宝藏湾', description: '提供海盗主题餐饮' },
        { name: '星露台餐厅', location: '明日世界', description: '提供中西式简餐' }
      ],
      restAreas: [
        { name: '花园广场休息区', location: '米奇大道入口处' },
        { name: '奇想花园休息区', location: '奇想花园' },
        { name: '探险岛休息区', location: '探险岛' }
      ],
      shops: [
        { name: '世界商店', location: '米奇大道', description: '销售迪士尼主题商品' },
        { name: '宝藏湾商店', location: '宝藏湾', description: '销售海盗主题商品' },
        { name: '明日世界商店', location: '明日世界', description: '销售科技主题商品' },
        { name: '奇幻童话城堡商店', location: '奇幻童话城堡', description: '销售公主主题商品' }
      ]
    },
    transportation: [
      { type: '地铁', description: '乘坐地铁11号线在迪士尼站下车' },
      { type: '公交', description: '乘坐50、51、52路公交车在迪士尼站下车' },
      { type: '自驾', description: '导航至上海迪士尼度假区，园区设有停车场' }
    ]
  }
]

onMounted(() => {
  const id = parseInt(route.params.id)
  // 模拟API请求
  setTimeout(() => {
    place.value = placesData.find(p => p.id === id) || null
    loading.value = false
  }, 500)
})

// 分类名称映射
const getCategoryName = (code) => {
  const categoryMap = {
    culture: '文化场馆',
    sightseeing: '观光景点',
    dining: '餐饮美食',
    shopping: '购物娱乐'
  }
  return categoryMap[code] || code
}
</script>

<style scoped>
.place-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  padding: 3rem;
}

.back-section {
  margin-bottom: 2rem;
}

.back-button {
  background-color: #f0f0f0;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.place-header {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.place-image-container {
  flex: 1;
  max-width: 500px;
}

.place-main-image {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.place-info {
  flex: 1;
}

.place-name {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #333;
}

.place-meta {
  margin-bottom: 1.5rem;
}

.rating, .category, .address {
  margin-bottom: 0.5rem;
}

.stars {
  color: #ffd700;
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.reviews {
  color: #666;
}

.category-label {
  font-weight: bold;
  margin-right: 0.5rem;
}

.address {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #555;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background-color: #f0f0f0;
  padding: 0.3rem 0.8rem;
  border-radius: 16px;
  font-size: 0.9rem;
}

.place-description, .facilities-section, .transportation-section, .opening-hours-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.place-description h2, .facilities-section h2, .transportation-section h2, .opening-hours-section h2 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.5rem;
}

.facilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.facility-card {
  display: flex;
  background-color: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.facility-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.facility-info {
  flex: 1;
}

.facility-info h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
}

.facility-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.facility-list li {
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.facility-list li:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.facility-name {
  font-weight: bold;
  display: block;
}

.facility-location, .facility-description {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.2rem;
}

.transportation-info, .opening-hours-info {
  background-color: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.transport-item {
  margin-bottom: 1rem;
}

.transport-item h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.no-data {
  color: #888;
  font-style: italic;
}

@media (max-width: 768px) {
  .place-header {
    flex-direction: column;
  }

  .place-image-container {
    max-width: 100%;
  }

  .facilities-grid {
    grid-template-columns: 1fr;
  }
}
</style>
