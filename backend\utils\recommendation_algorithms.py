"""
景点推荐算法模块

该模块实现了各种景点排序和推荐算法，包括：
1. 基于热度的排序
2. 基于评分的排序
3. 基于协同过滤的推荐
4. 基于内容的推荐
5. 混合推荐

所有算法都使用纯Python实现，不依赖数据库查询。
"""

import heapq
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set, Any, Optional
import math
import time


class RecommendationAlgorithms:
    """景点推荐算法类"""

    @staticmethod
    def sort_by_popularity(locations: List[Dict], limit: Optional[int] = None) -> List[Dict]:
        """
        按热度排序景点

        Args:
            locations: 景点列表
            limit: 返回结果数量限制

        Returns:
            按热度排序的景点列表
        """
        print(f"开始按热度排序 {len(locations)} 个景点")

        # 检查是否包含北京邮电大学
        bupt_locations = [loc for loc in locations if loc.get('name') == '北京邮电大学']
        print(f"排序前包含 {len(bupt_locations)} 个北京邮电大学")
        if bupt_locations:
            print(f"北京邮电大学的热度: {bupt_locations[0].get('popularity')}")

        # 使用堆排序算法 (O(n log n))
        result = sorted(locations, key=lambda x: x.get('popularity', 0) or 0, reverse=True)

        print(f"排序后，前5个景点的热度: {[(loc.get('name'), loc.get('popularity')) for loc in result[:5]]}")

        # 检查排序后是否包含北京邮电大学
        bupt_locations_after = [loc for loc in result if loc.get('name') == '北京邮电大学']
        print(f"排序后包含 {len(bupt_locations_after)} 个北京邮电大学")
        if bupt_locations_after:
            bupt_index = result.index(bupt_locations_after[0])
            print(f"北京邮电大学在排序后的位置: {bupt_index}")

        # 应用限制
        if limit:
            result = result[:limit]
            print(f"应用限制 {limit}，返回 {len(result)} 个景点")

            # 检查限制后是否包含北京邮电大学
            bupt_locations_limit = [loc for loc in result if loc.get('name') == '北京邮电大学']
            print(f"限制后包含 {len(bupt_locations_limit)} 个北京邮电大学")

        return result

    @staticmethod
    def sort_by_rating(locations: List[Dict], limit: Optional[int] = None) -> List[Dict]:
        """
        按评分排序景点

        Args:
            locations: 景点列表
            limit: 返回结果数量限制

        Returns:
            按评分排序的景点列表
        """
        # 使用堆排序算法 (O(n log n))
        result = sorted(locations, key=lambda x: x.get('evaluation', 0), reverse=True)

        # 应用限制
        if limit:
            result = result[:limit]

        return result

    @staticmethod
    def filter_by_type(locations: List[Dict], location_type: int) -> List[Dict]:
        """
        按类型过滤景点

        Args:
            locations: 景点列表
            location_type: 景点类型

        Returns:
            过滤后的景点列表
        """
        return [loc for loc in locations if loc.get('type') == location_type]

    @staticmethod
    def collaborative_filtering(locations: List[Dict], user_history: Dict[int, int],
                               all_users_history: Dict[int, Dict[int, int]],
                               limit: Optional[int] = None) -> List[Dict]:
        """
        基于协同过滤的景点推荐

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 如果用户没有浏览历史，返回热门景点
        if not user_history:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览的景点ID集合
        user_locations = set(user_history.keys())

        # 计算用户间的相似度
        user_id = -1  # 当前用户的ID，这里假设为-1
        similar_users = RecommendationAlgorithms._find_similar_users(
            user_id, user_history, all_users_history, limit=20
        )

        # 如果没有相似用户，返回热门景点
        if not similar_users:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 计算所有景点的得分
        location_scores = defaultdict(float)

        # 对于每个相似用户
        for similar_user_id, similarity in similar_users:
            # 获取他们的浏览历史
            similar_user_history = all_users_history.get(similar_user_id, {})

            # 对于他们浏览的每个景点
            for location_id, count in similar_user_history.items():
                # 跳过用户已浏览的景点
                if location_id in user_locations:
                    continue

                # 添加加权得分
                location_scores[location_id] += similarity * count

        # 如果没有推荐结果，返回热门景点
        if not location_scores:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 创建location_id到location的映射
        location_map = {loc.get('location_id'): loc for loc in locations}

        # 使用堆排序获取得分最高的景点
        top_location_ids = heapq.nlargest(
            limit if limit else len(location_scores),
            location_scores.items(),
            key=lambda x: x[1]
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['collaborative_score'] = score
                result.append(location_copy)

        return result

    @staticmethod
    def _find_similar_users(user_id: int, user_history: Dict[int, int],
                           all_users_history: Dict[int, Dict[int, int]],
                           limit: int = 10) -> List[Tuple[int, float]]:
        """
        找到与指定用户相似的用户

        Args:
            user_id: 用户ID
            user_history: 用户浏览历史 {location_id: count}
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            limit: 返回结果数量限制

        Returns:
            相似用户列表 [(user_id, similarity)]
        """
        similarities = []

        # 对于每个用户
        for other_id, other_history in all_users_history.items():
            # 跳过自己
            if other_id == user_id:
                continue

            # 计算余弦相似度
            similarity = RecommendationAlgorithms._cosine_similarity(user_history, other_history)

            # 添加到结果
            if similarity > 0:
                similarities.append((other_id, similarity))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)

        # 应用限制
        if limit:
            similarities = similarities[:limit]

        return similarities

    @staticmethod
    def _cosine_similarity(vec1: Dict[int, int], vec2: Dict[int, int]) -> float:
        """
        计算两个向量的余弦相似度

        Args:
            vec1: 第一个向量 {id: value}
            vec2: 第二个向量 {id: value}

        Returns:
            余弦相似度
        """
        # 找到共同的键
        common_keys = set(vec1.keys()) & set(vec2.keys())

        # 如果没有共同的键，相似度为0
        if not common_keys:
            return 0.0

        # 计算点积
        dot_product = sum(vec1[k] * vec2[k] for k in common_keys)

        # 计算向量模长
        norm1 = math.sqrt(sum(v * v for v in vec1.values()))
        norm2 = math.sqrt(sum(v * v for v in vec2.values()))

        # 避免除以0
        if norm1 == 0 or norm2 == 0:
            return 0.0

        # 计算余弦相似度
        return dot_product / (norm1 * norm2)

    @staticmethod
    def content_based_filtering(locations: List[Dict], user_history: Dict[int, int],
                               limit: Optional[int] = None) -> List[Dict]:
        """
        基于内容的景点推荐

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 如果用户没有浏览历史，返回热门景点
        if not user_history:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览的景点ID集合
        user_locations = set(user_history.keys())

        # 创建location_id到location的映射
        location_map = {loc.get('location_id'): loc for loc in locations}

        # 分析用户偏好
        location_types = Counter()
        keywords = Counter()

        for location_id, count in user_history.items():
            location = location_map.get(location_id)
            if location:
                # 统计景点类型
                location_types[location.get('type')] += count

                # 统计关键词
                if location.get('keyword'):
                    for keyword in location.get('keyword', '').split(','):
                        keywords[keyword.strip()] += count

        # 获取最常见的景点类型和关键词
        common_types = [t for t, _ in location_types.most_common()]
        common_keywords = [k for k, _ in keywords.most_common(10)]  # 限制为前10个关键词

        # 计算所有景点的得分
        location_scores = defaultdict(float)

        for location in locations:
            location_id = location.get('location_id')

            # 跳过用户已浏览的景点
            if location_id in user_locations:
                continue

            score = 0.0

            # 类型匹配
            if location.get('type') in common_types:
                # 更常见的类型得分更高
                type_rank = common_types.index(location.get('type'))
                type_score = 1.0 / (type_rank + 1)  # 最常见的为1.0，第二常见的为0.5，依此类推
                score += type_score

            # 关键词匹配
            if location.get('keyword'):
                location_keywords = [k.strip() for k in location.get('keyword', '').split(',')]
                for keyword in location_keywords:
                    if keyword in common_keywords:
                        # 更常见的关键词得分更高
                        keyword_rank = common_keywords.index(keyword)
                        keyword_score = 1.0 / (keyword_rank + 1)
                        score += keyword_score

            # 添加热度因素（小权重）
            score += (location.get('popularity', 0) or 0) / 1000

            # 如果得分为正，添加到结果
            if score > 0:
                location_scores[location_id] = score

        # 如果没有推荐结果，返回热门景点
        if not location_scores:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 使用堆排序获取得分最高的景点
        top_location_ids = heapq.nlargest(
            limit if limit else len(location_scores),
            location_scores.items(),
            key=lambda x: x[1]
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['content_score'] = score
                result.append(location_copy)

        return result

    @staticmethod
    def hybrid_recommendation(locations: List[Dict], user_history: Dict[int, int],
                             all_users_history: Dict[int, Dict[int, int]],
                             weights: Dict[str, float] = None,
                             limit: Optional[int] = None) -> List[Dict]:
        """
        混合推荐算法

        Args:
            locations: 景点列表
            user_history: 用户浏览历史 {location_id: count}
            all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
            weights: 各个推荐方法的权重 {'collaborative': w1, 'content': w2, 'popularity': w3}
            limit: 返回结果数量限制

        Returns:
            推荐的景点列表
        """
        # 设置默认权重
        if weights is None:
            weights = {
                'collaborative': 1.0,
                'content': 1.0,
                'popularity': 0.5
            }

        # 如果用户没有浏览历史，返回热门景点
        if not user_history:
            return RecommendationAlgorithms.sort_by_popularity(locations, limit)

        # 用户已浏览的景点ID集合
        user_locations = set(user_history.keys())

        # 创建location_id到location的映射
        location_map = {loc.get('location_id'): loc for loc in locations}

        # 获取协同过滤推荐结果
        collaborative_recs = RecommendationAlgorithms.collaborative_filtering(
            locations, user_history, all_users_history, limit=None
        )
        collaborative_scores = {
            loc.get('location_id'): loc.get('collaborative_score', 0)
            for loc in collaborative_recs
        }

        # 获取基于内容的推荐结果
        content_recs = RecommendationAlgorithms.content_based_filtering(
            locations, user_history, limit=None
        )
        content_scores = {
            loc.get('location_id'): loc.get('content_score', 0)
            for loc in content_recs
        }

        # 获取热门景点
        popular_recs = RecommendationAlgorithms.sort_by_popularity(locations, limit=None)
        # 归一化热度得分
        max_popularity = max((loc.get('popularity', 0) or 0) for loc in popular_recs) if popular_recs else 1
        popular_scores = {
            loc.get('location_id'): (loc.get('popularity', 0) or 0) / max_popularity
            for loc in popular_recs
        }

        # 计算混合得分
        hybrid_scores = defaultdict(float)

        # 获取所有景点ID
        all_location_ids = set()
        all_location_ids.update(collaborative_scores.keys())
        all_location_ids.update(content_scores.keys())
        all_location_ids.update(popular_scores.keys())

        # 跳过用户已浏览的景点
        all_location_ids = all_location_ids - user_locations

        for location_id in all_location_ids:
            # 获取各个方法的得分（默认为0）
            collaborative_score = collaborative_scores.get(location_id, 0) * weights['collaborative']
            content_score = content_scores.get(location_id, 0) * weights['content']
            popularity_score = popular_scores.get(location_id, 0) * weights['popularity']

            # 计算加权和
            total_weight = weights['collaborative'] + weights['content'] + weights['popularity']
            hybrid_score = (collaborative_score + content_score + popularity_score) / total_weight

            hybrid_scores[location_id] = hybrid_score

        # 使用堆排序获取得分最高的景点
        top_location_ids = heapq.nlargest(
            limit if limit else len(hybrid_scores),
            hybrid_scores.items(),
            key=lambda x: x[1]
        )

        # 转换为景点列表
        result = []
        for location_id, score in top_location_ids:
            location = location_map.get(location_id)
            if location:
                location_copy = location.copy()
                location_copy['hybrid_score'] = score
                result.append(location_copy)

        return result
