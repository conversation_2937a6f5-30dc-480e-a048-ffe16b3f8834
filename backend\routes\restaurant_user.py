"""
餐馆用户交互路由模块

该模块提供餐馆用户交互的API接口，包括：
1. 收藏餐馆
2. 取消收藏餐馆
3. 检查是否已收藏餐馆
4. 获取用户收藏的餐馆列表
5. 评论餐馆
6. 获取餐馆评论列表
"""

from flask import Blueprint, request, current_app
from models.restaurant import Restaurant
from models.restaurant_favorite import RestaurantFavorite
from models.restaurant_comment import RestaurantComment
from models.restaurant_rating import RestaurantRating
from models.user import User
from utils.database import db
from utils.response import success, error
from sqlalchemy import desc, func

restaurant_user_bp = Blueprint('restaurant_user', __name__)


@restaurant_user_bp.route('/favorite', methods=['POST'])
def favorite_restaurant():
    """
    收藏餐馆
    """
    try:
        # 获取请求参数
        data = request.get_json()
        user_id = data.get('user_id')
        restaurant_id = data.get('restaurant_id')

        # 检查参数
        if not user_id or not restaurant_id:
            return error("缺少必要参数")

        # 检查餐馆是否存在
        restaurant = Restaurant.query.get(restaurant_id)
        if not restaurant:
            return error("餐馆不存在")

        # 检查是否已收藏
        favorite = RestaurantFavorite.query.filter_by(
            user_id=user_id,
            restaurant_id=restaurant_id
        ).first()

        if favorite:
            return error("已收藏该餐馆")

        # 创建收藏记录
        favorite = RestaurantFavorite(
            user_id=user_id,
            restaurant_id=restaurant_id
        )
        db.session.add(favorite)
        db.session.commit()

        return success(favorite.to_dict())
    except Exception as e:
        current_app.logger.error(f"收藏餐馆出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/unfavorite', methods=['POST'])
def unfavorite_restaurant():
    """
    取消收藏餐馆
    """
    try:
        # 获取请求参数
        data = request.get_json()
        user_id = data.get('user_id')
        restaurant_id = data.get('restaurant_id')

        # 检查参数
        if not user_id or not restaurant_id:
            return error("缺少必要参数")

        # 查询收藏记录
        favorite = RestaurantFavorite.query.filter_by(
            user_id=user_id,
            restaurant_id=restaurant_id
        ).first()

        if not favorite:
            return error("未收藏该餐馆")

        # 删除收藏记录
        db.session.delete(favorite)
        db.session.commit()

        return success({"message": "取消收藏成功"})
    except Exception as e:
        current_app.logger.error(f"取消收藏餐馆出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/favorite/check', methods=['POST', 'GET'])
def check_favorite():
    """
    检查是否已收藏餐馆
    支持GET和POST方法
    """
    try:
        # 根据请求方法获取参数
        if request.method == 'POST':
            data = request.get_json()
            user_id = data.get('user_id')
            restaurant_id = data.get('restaurant_id')
        else:  # GET方法
            user_id = request.args.get('user_id')
            restaurant_id = request.args.get('restaurant_id')

        # 检查参数
        if not user_id or not restaurant_id:
            return error("缺少必要参数")

        # 查询收藏记录
        favorite = RestaurantFavorite.query.filter_by(
            user_id=user_id,
            restaurant_id=restaurant_id
        ).first()

        return success({"is_favorite": favorite is not None})
    except Exception as e:
        current_app.logger.error(f"检查收藏状态出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/favorite/user/<int:user_id>', methods=['GET'])
def get_user_favorites(user_id):
    """
    获取用户收藏的餐馆列表
    """
    try:
        # 查询用户收藏的餐馆
        favorites = RestaurantFavorite.query.filter_by(user_id=user_id).all()

        # 获取餐馆详情
        result = []
        for favorite in favorites:
            restaurant = Restaurant.query.get(favorite.restaurant_id)
            if restaurant:
                restaurant_dict = restaurant.to_dict()
                restaurant_dict['favorite_id'] = favorite.id
                restaurant_dict['favorite_time'] = favorite.created_at.strftime('%Y-%m-%d %H:%M:%S') if favorite.created_at else None
                result.append(restaurant_dict)

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取用户收藏餐馆出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/comment', methods=['POST'])
def comment_restaurant():
    """
    评论餐馆
    """
    try:
        # 获取请求参数
        data = request.get_json()
        user_id = data.get('user_id')
        restaurant_id = data.get('restaurant_id')
        content = data.get('content')
        rating = data.get('rating')

        # 检查参数
        if not user_id or not restaurant_id or not content or rating is None:
            return error("缺少必要参数")

        # 检查餐馆是否存在
        restaurant = Restaurant.query.get(restaurant_id)
        if not restaurant:
            return error("餐馆不存在")

        # 创建评论记录
        comment = RestaurantComment(
            user_id=user_id,
            restaurant_id=restaurant_id,
            content=content,
            rating=rating
        )
        db.session.add(comment)

        # 更新餐馆评分
        comments = RestaurantComment.query.filter_by(restaurant_id=restaurant_id).all()
        total_rating = sum([c.rating for c in comments]) + rating
        avg_rating = total_rating / (len(comments) + 1)
        restaurant.evaluation = avg_rating

        db.session.commit()

        return success(comment.to_dict())
    except Exception as e:
        current_app.logger.error(f"评论餐馆出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/comment/<int:restaurant_id>', methods=['GET'])
def get_restaurant_comments(restaurant_id):
    """
    获取餐馆评论列表
    """
    try:
        # 获取请求参数
        limit = request.args.get('limit', 10, type=int)

        # 查询餐馆评论，包含用户信息
        comments = db.session.query(RestaurantComment, User)\
            .join(User, RestaurantComment.user_id == User.user_id)\
            .filter(RestaurantComment.restaurant_id == restaurant_id)\
            .order_by(desc(RestaurantComment.created_at))\
            .limit(limit)\
            .all()

        # 转换为字典列表，包含用户信息
        result = []
        for comment, user in comments:
            comment_dict = {
                'id': comment.id,
                'user_id': comment.user_id,
                'restaurant_id': comment.restaurant_id,
                'content': comment.content,
                'rating': comment.rating,
                'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M:%S') if comment.created_at else None,
                'username': user.username if user else f'用户{comment.user_id}',
                'user_avatar': user.avatar if user and user.avatar else '/uploads/avatars/default.jpg'
            }
            result.append(comment_dict)

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取餐馆评论出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/rating', methods=['POST'])
def rate_restaurant():
    """
    对餐馆进行评分
    """
    try:
        # 获取请求参数
        data = request.get_json()
        user_id = data.get('user_id')
        restaurant_id = data.get('restaurant_id')
        rating = data.get('rating')

        # 检查参数
        if not user_id or not restaurant_id or rating is None:
            return error("缺少必要参数")

        # 检查餐馆是否存在
        restaurant = Restaurant.query.get(restaurant_id)
        if not restaurant:
            return error("餐馆不存在")

        # 检查用户是否已评分
        existing_rating = RestaurantRating.query.filter_by(
            user_id=user_id,
            restaurant_id=restaurant_id
        ).first()

        if existing_rating:
            # 更新评分
            existing_rating.rating = rating
            db.session.commit()

            # 更新餐馆平均评分
            update_restaurant_average_rating(restaurant_id)

            return success(existing_rating.to_dict())
        else:
            # 创建新评分
            new_rating = RestaurantRating(
                user_id=user_id,
                restaurant_id=restaurant_id,
                rating=rating
            )
            db.session.add(new_rating)
            db.session.commit()

            # 更新餐馆平均评分
            update_restaurant_average_rating(restaurant_id)

            return success(new_rating.to_dict())
    except Exception as e:
        current_app.logger.error(f"评分餐馆出错: {str(e)}")
        return error(str(e))


@restaurant_user_bp.route('/rating/<int:restaurant_id>', methods=['GET'])
def get_restaurant_rating(restaurant_id):
    """
    获取餐馆评分
    """
    try:
        # 计算餐馆平均评分
        avg_rating = db.session.query(func.avg(RestaurantRating.rating))\
            .filter(RestaurantRating.restaurant_id == restaurant_id)\
            .scalar()

        # 计算评分人数
        rating_count = db.session.query(func.count(RestaurantRating.id))\
            .filter(RestaurantRating.restaurant_id == restaurant_id)\
            .scalar()

        # 获取用户评分（如果提供了用户ID）
        user_id = request.args.get('user_id', type=int)
        user_rating = None

        if user_id:
            user_rating_obj = RestaurantRating.query.filter_by(
                user_id=user_id,
                restaurant_id=restaurant_id
            ).first()

            if user_rating_obj:
                user_rating = user_rating_obj.rating

        result = {
            'restaurant_id': restaurant_id,
            'average_rating': float(avg_rating) if avg_rating else 0,
            'rating_count': rating_count or 0,
            'user_rating': user_rating
        }

        return success(result)
    except Exception as e:
        current_app.logger.error(f"获取餐馆评分出错: {str(e)}")
        return error(str(e))


def update_restaurant_average_rating(restaurant_id):
    """
    更新餐馆平均评分

    Args:
        restaurant_id: 餐馆ID
    """
    # 计算餐馆平均评分
    avg_rating = db.session.query(func.avg(RestaurantRating.rating))\
        .filter(RestaurantRating.restaurant_id == restaurant_id)\
        .scalar()

    # 更新餐馆评分
    restaurant = Restaurant.query.get(restaurant_id)
    if restaurant and avg_rating:
        restaurant.evaluation = float(avg_rating)
        db.session.commit()
