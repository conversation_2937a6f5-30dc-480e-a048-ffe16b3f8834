from flask import Blueprint, request, jsonify, current_app
from models.article import Article, ArticleFavorite
from models.user import User
from models.location import Location
from models.article_like import ArticleLike
from models.article_comment import ArticleComment
from services.article_service import ArticleService
from utils.database import db
from utils.response import success, error
from utils.recommendation_factory import RecommendationFactory
from utils.text_search import TextSearchEngine
import json
import os
import uuid
import time

article_bp = Blueprint('article', __name__)

@article_bp.route('', methods=['GET'])
def get_articles():
    """
    Get all articles with pagination and sorting
    """
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 10, type=int)
        sort = request.args.get('sort', 'created_at')
        direction = request.args.get('direction', 'desc')

        # Validate sort field
        valid_sort_fields = ['created_at', 'popularity', 'evaluation']
        if sort not in valid_sort_fields:
            sort = 'created_at'

        # Validate direction
        if direction not in ['asc', 'desc']:
            direction = 'desc'

        # Build query
        query = Article.query

        # Apply sorting
        if direction == 'desc':
            query = query.order_by(getattr(Article, sort).desc())
        else:
            query = query.order_by(getattr(Article, sort).asc())

        # Apply pagination
        pagination = query.paginate(page=page, per_page=size, error_out=False)
        articles = pagination.items

        # Get user information
        user_ids = [article.user_id for article in articles]
        users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()}

        # 创建文章服务实例
        article_service = ArticleService()

        # Build response
        result = []
        for article in articles:
            # 解压文章内容
            try:
                huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""
            except Exception as e:
                print(f"Error decompressing article {article.article_id}: {e}")
                content = "内容解压失败"

            # 获取点赞、收藏和评论数量
            likes_count = ArticleLike.query.filter_by(article_id=article.article_id).count()
            favorites_count = ArticleFavorite.query.filter_by(article_id=article.article_id).count()
            comments_count = ArticleComment.query.filter_by(article_id=article.article_id).count()

            # 收集所有图片和视频URL
            image_urls = {
                'image_url': article.image_url,
                'image_url_2': article.image_url_2,
                'image_url_3': article.image_url_3,
                'image_url_4': article.image_url_4,
                'image_url_5': article.image_url_5,
                'image_url_6': article.image_url_6
            }

            video_urls = {
                'video_url': article.video_url,
                'video_url_2': article.video_url_2,
                'video_url_3': article.video_url_3
            }

            # 解析标签
            try:
                tags = json.loads(article.tags) if article.tags else []
            except:
                tags = []

            result.append({
                'article_id': article.article_id,
                'user_id': article.user_id,
                'username': users[article.user_id].username if article.user_id in users else None,
                'avatar': users[article.user_id].avatar if article.user_id in users else None,  # 添加用户头像
                'title': article.title,
                'content': content,  # 添加解压后的内容
                'location': article.location,
                'tags': tags,  # 添加标签信息
                'popularity': article.popularity,
                'evaluation': article.evaluation,
                **image_urls,
                **video_urls,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                'likes_count': likes_count,
                'favorites_count': favorites_count,
                'comments_count': comments_count
            })

        return success({
            'articles': result,
            'total': pagination.total,
            'page': page,
            'size': size,
            'pages': pagination.pages
        }, 'Articles retrieved successfully')

    except Exception as e:
        return error(str(e))

@article_bp.route('', methods=['POST'])
def add_article():
    """
    Add a new article
    支持JSON和表单两种提交方式
    """
    try:
        # 检查是否是表单提交
        if request.content_type and 'multipart/form-data' in request.content_type:
            # 表单提交
            user_id = request.form.get('user_id')
            title = request.form.get('title')
            content = request.form.get('content')
            location = request.form.get('location')
            tags = request.form.get('tags')  # 获取标签字符串

            # 解析标签
            tags_list = []
            if tags:
                try:
                    tags_list = json.loads(tags) if isinstance(tags, str) else tags
                except:
                    tags_list = []

            # 检查必填字段
            if not user_id or not title or not content or not location:
                return error('Missing required fields')

            # 初始化服务
            service = ArticleService()

            # 添加文章
            article_id = service.add_article(int(user_id), title, content, location, tags_list)

            if not article_id:
                return error('Failed to add article')

            # 处理图片和视频
            article = Article.query.get(article_id)

            # 处理图片 - 支持多图片上传（最多6张）
            # 图片字段名称: image, image_2, image_3, image_4, image_5, image_6
            image_fields = ['image', 'image_2', 'image_3', 'image_4', 'image_5', 'image_6']
            allowed_image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

            for i, field_name in enumerate(image_fields, 1):
                if field_name in request.files:
                    image_file = request.files[field_name]
                    if image_file and image_file.filename:
                        # 检查文件类型
                        if '.' in image_file.filename and \
                           image_file.filename.rsplit('.', 1)[1].lower() in allowed_image_extensions:
                            # 生成唯一文件名
                            filename = f"image_{article_id}_{i}_{uuid.uuid4().hex}.{image_file.filename.rsplit('.', 1)[1].lower()}"

                            # 确保上传目录存在
                            upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'images')
                            os.makedirs(upload_folder, exist_ok=True)

                            # 保存文件
                            file_path = os.path.join(upload_folder, filename)
                            image_file.save(file_path)

                            # 更新文章
                            if i == 1:
                                article.image_url = f"/uploads/images/{filename}"
                            else:
                                setattr(article, f"image_url_{i}", f"/uploads/images/{filename}")

            # 处理视频 - 支持多视频上传（最多3个）
            # 视频字段名称: video, video_2, video_3
            video_fields = ['video', 'video_2', 'video_3']
            allowed_video_extensions = {'mp4', 'webm', 'ogg', 'mov'}

            for i, field_name in enumerate(video_fields, 1):
                if field_name in request.files:
                    video_file = request.files[field_name]
                    if video_file and video_file.filename:
                        # 检查文件类型
                        if '.' in video_file.filename and \
                           video_file.filename.rsplit('.', 1)[1].lower() in allowed_video_extensions:
                            # 生成唯一文件名
                            filename = f"video_{article_id}_{i}_{uuid.uuid4().hex}.{video_file.filename.rsplit('.', 1)[1].lower()}"

                            # 确保上传目录存在
                            upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], 'videos')
                            os.makedirs(upload_folder, exist_ok=True)

                            # 保存文件
                            file_path = os.path.join(upload_folder, filename)
                            video_file.save(file_path)

                            # 更新文章
                            if i == 1:
                                article.video_url = f"/uploads/videos/{filename}"
                            else:
                                setattr(article, f"video_url_{i}", f"/uploads/videos/{filename}")

            # 保存更改
            db.session.commit()

            # 收集所有图片和视频URL
            image_urls = {
                'image_url': article.image_url,
                'image_url_2': article.image_url_2,
                'image_url_3': article.image_url_3,
                'image_url_4': article.image_url_4,
                'image_url_5': article.image_url_5,
                'image_url_6': article.image_url_6
            }

            video_urls = {
                'video_url': article.video_url,
                'video_url_2': article.video_url_2,
                'video_url_3': article.video_url_3
            }

            return success({
                'article_id': article_id,
                **image_urls,
                **video_urls
            }, 'Article added successfully')
        else:
            # JSON提交
            data = request.get_json()

            # 检查必填字段
            if not data:
                return error('No data provided')

            user_id = data.get('user_id')
            title = data.get('title')
            content = data.get('content')
            location = data.get('location')
            tags = data.get('tags', [])  # 获取标签数组

            # 获取所有图片URL
            image_url = data.get('image_url')
            image_url_2 = data.get('image_url_2')
            image_url_3 = data.get('image_url_3')
            image_url_4 = data.get('image_url_4')
            image_url_5 = data.get('image_url_5')
            image_url_6 = data.get('image_url_6')

            # 获取所有视频URL
            video_url = data.get('video_url')
            video_url_2 = data.get('video_url_2')
            video_url_3 = data.get('video_url_3')

            if not user_id or not title or not content or not location:
                return error('Missing required fields')

            # 初始化服务
            service = ArticleService()

            # 添加文章
            article_id = service.add_article(user_id, title, content, location, tags)

            if not article_id:
                return error('Failed to add article')

            # 更新文章的图片和视频URL
            article = Article.query.get(article_id)
            if article:
                # 更新图片URL
                if image_url:
                    article.image_url = image_url
                if image_url_2:
                    article.image_url_2 = image_url_2
                if image_url_3:
                    article.image_url_3 = image_url_3
                if image_url_4:
                    article.image_url_4 = image_url_4
                if image_url_5:
                    article.image_url_5 = image_url_5
                if image_url_6:
                    article.image_url_6 = image_url_6

                # 更新视频URL
                if video_url:
                    article.video_url = video_url
                if video_url_2:
                    article.video_url_2 = video_url_2
                if video_url_3:
                    article.video_url_3 = video_url_3

                db.session.commit()

            # 收集所有图片和视频URL
            image_urls = {
                'image_url': article.image_url,
                'image_url_2': article.image_url_2,
                'image_url_3': article.image_url_3,
                'image_url_4': article.image_url_4,
                'image_url_5': article.image_url_5,
                'image_url_6': article.image_url_6
            }

            video_urls = {
                'video_url': article.video_url,
                'video_url_2': article.video_url_2,
                'video_url_3': article.video_url_3
            }

            return success({
                'article_id': article_id,
                **image_urls,
                **video_urls
            }, 'Article added successfully')

    except Exception as e:
        db.session.rollback()
        return error(str(e))

@article_bp.route('/<int:article_id>', methods=['GET'])
def get_article(article_id):
    """
    Get article by ID
    Equivalent to Java's ArticleController.getArticle()

    Query parameters:
    - content_only: If set to 'true', only returns the article content
    - increment_popularity: If set to 'false', does not increment the popularity counter
    """
    try:
        # Check if only content is requested
        content_only = request.args.get('content_only', 'false').lower() == 'true'

        # Check if popularity should be incremented (默认为true，因为这是查看文章详情)
        increment_popularity = request.args.get('increment_popularity', 'true').lower() == 'true'

        # Initialize service
        service = ArticleService()

        # Get article with the increment_popularity parameter
        article = service.get_article(article_id, increment_popularity)

        if not article:
            return error('Article not found', 404)

        # If only content is requested, return only the content
        if content_only:
            return success({'content': article['content']}, 'Article content retrieved successfully')

        return success(article, 'Article retrieved successfully')

    except Exception as e:
        return error(str(e))

# 路由已合并到 get_article 中，通过 content_only 参数控制是否只返回内容

@article_bp.route('/<int:article_id>', methods=['DELETE'])
def delete_article(article_id):
    """
    Delete article by ID
    Equivalent to Java's ArticleController.deleteArticle()
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return error('No data provided', 400)

        user_id = data.get('user_id')

        if not user_id:
            return error('Missing user_id parameter', 400)

        # Get article
        article = Article.query.get(article_id)

        if not article:
            return error('Article not found', 404)

        # Check if user is the owner
        if article.user_id != user_id:
            return error('Unauthorized', 403)

        # Delete article
        db.session.delete(article)
        db.session.commit()

        return success({}, 'Article deleted successfully')

    except Exception as e:
        db.session.rollback()
        return error(str(e))

@article_bp.route('/<int:article_id>', methods=['PUT'])
def update_article(article_id):
    """
    Update article by ID
    Equivalent to Java's ArticleController.updateArticle()
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data:
            return error('No data provided', 400)

        user_id = data.get('user_id')
        title = data.get('title')
        content = data.get('content')
        location = data.get('location')

        # 获取所有图片URL
        image_url = data.get('image_url')
        image_url_2 = data.get('image_url_2')
        image_url_3 = data.get('image_url_3')
        image_url_4 = data.get('image_url_4')
        image_url_5 = data.get('image_url_5')
        image_url_6 = data.get('image_url_6')

        # 获取所有视频URL
        video_url = data.get('video_url')
        video_url_2 = data.get('video_url_2')
        video_url_3 = data.get('video_url_3')

        if not user_id or not title or not content or not location:
            return error('Missing required fields', 400)

        # Get article
        article = Article.query.get(article_id)

        if not article:
            return error('Article not found', 404)

        # Check if user is the owner
        if article.user_id != user_id:
            return error('Unauthorized', 403)

        # Initialize service
        service = ArticleService()

        # Compress content using Huffman coding
        huffman_tree = service.build_huffman_tree(content)
        huffman_codes = service.generate_huffman_codes(huffman_tree)
        compressed_content = service.compress_text(content, huffman_codes)

        # Convert Huffman codes to JSON string
        import json
        huffman_codes_json = json.dumps(huffman_codes)

        # Update article
        article.title = title
        article.content = compressed_content
        article.huffman_codes = huffman_codes_json
        article.location = location

        # 更新图片URL
        if image_url:
            article.image_url = image_url
        if image_url_2:
            article.image_url_2 = image_url_2
        if image_url_3:
            article.image_url_3 = image_url_3
        if image_url_4:
            article.image_url_4 = image_url_4
        if image_url_5:
            article.image_url_5 = image_url_5
        if image_url_6:
            article.image_url_6 = image_url_6

        # 更新视频URL
        if video_url:
            article.video_url = video_url
        if video_url_2:
            article.video_url_2 = video_url_2
        if video_url_3:
            article.video_url_3 = video_url_3

        db.session.commit()

        # 收集所有图片和视频URL
        image_urls = {
            'image_url': article.image_url,
            'image_url_2': article.image_url_2,
            'image_url_3': article.image_url_3,
            'image_url_4': article.image_url_4,
            'image_url_5': article.image_url_5,
            'image_url_6': article.image_url_6
        }

        video_urls = {
            'video_url': article.video_url,
            'video_url_2': article.video_url_2,
            'video_url_3': article.video_url_3
        }

        return success({
            'article_id': article_id,
            **image_urls,
            **video_urls
        }, 'Article updated successfully')

    except Exception as e:
        db.session.rollback()
        return error(str(e))

# 路由已合并到 /recommend/articles/all 中，提供更完整的协同过滤推荐功能

@article_bp.route('/search', methods=['GET'])
def search_articles():
    """
    Search articles by title or location
    """
    try:
        title = request.args.get('title')
        location_name = request.args.get('location')

        if not title and not location_name:
            return error('Missing search parameters', 400)

        # Search by title
        if title:
            articles = Article.query.filter(Article.title.like(f'%{title}%')).all()
        # Search by location
        elif location_name:
            articles = Article.query.filter(Article.location.like(f'%{location_name}%')).all()

        # Get user information
        user_ids = [article.user_id for article in articles]
        users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()}

        # 创建文章服务实例
        article_service = ArticleService()

        # Build response
        result = []
        for article in articles:
            # 解压文章内容
            try:
                huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""
            except Exception as e:
                print(f"Error decompressing article {article.article_id}: {e}")
                content = "内容解压失败"

            # 获取点赞、收藏和评论数量
            likes_count = ArticleLike.query.filter_by(article_id=article.article_id).count()
            favorites_count = ArticleFavorite.query.filter_by(article_id=article.article_id).count()
            comments_count = ArticleComment.query.filter_by(article_id=article.article_id).count()

            # 收集所有图片和视频URL
            image_urls = {
                'image_url': article.image_url,
                'image_url_2': article.image_url_2,
                'image_url_3': article.image_url_3,
                'image_url_4': article.image_url_4,
                'image_url_5': article.image_url_5,
                'image_url_6': article.image_url_6
            }

            video_urls = {
                'video_url': article.video_url,
                'video_url_2': article.video_url_2,
                'video_url_3': article.video_url_3
            }

            # 解析标签
            try:
                tags = json.loads(article.tags) if article.tags else []
            except:
                tags = []

            result.append({
                'article_id': article.article_id,
                'user_id': article.user_id,
                'username': users[article.user_id].username if article.user_id in users else None,
                'avatar': users[article.user_id].avatar if article.user_id in users else None,  # 添加用户头像
                'title': article.title,
                'content': content,  # 添加解压后的内容
                'location': article.location,
                'tags': tags,
                'popularity': article.popularity,
                'evaluation': article.evaluation,
                **image_urls,
                **video_urls,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                'likes_count': likes_count,
                'favorites_count': favorites_count,
                'comments_count': comments_count
            })

        return success({'articles': result}, 'Articles retrieved successfully')

    except Exception as e:
        return error(str(e))

# 路由已合并到 /article/search 中，通过 title 参数搜索文章

# 路由已合并到 /article/search 中，通过 location 参数搜索文章

@article_bp.route('/update_popularity/<int:article_id>', methods=['POST'])
def update_popularity(article_id):
    """
    Update article popularity
    """
    try:
        # Get article
        article = Article.query.get(article_id)

        if not article:
            return jsonify({'error': 'Article not found'}), 404

        # Update popularity
        article.popularity += 1
        db.session.commit()

        return jsonify({'message': 'Article popularity updated successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@article_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_articles(user_id):
    """
    Get all articles by a specific user
    """
    try:
        # 创建文章服务实例
        article_service = ArticleService()

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 10, type=int)

        # Build query for user's articles
        query = Article.query.filter_by(user_id=user_id)

        # Apply sorting - newest first
        query = query.order_by(Article.created_at.desc())

        # Apply pagination
        pagination = query.paginate(page=page, per_page=size, error_out=False)
        articles = pagination.items

        # 不需要获取location信息，因为现在直接存储location字符串

        # 获取用户信息
        user = User.query.get(user_id)

        # Build response
        result = []
        for article in articles:
            # 解压文章内容
            try:
                huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""
            except Exception as e:
                print(f"Error decompressing article {article.article_id}: {e}")
                content = "内容解压失败"

            # 获取点赞、收藏和评论数量
            likes_count = ArticleLike.query.filter_by(article_id=article.article_id).count()
            favorites_count = ArticleFavorite.query.filter_by(article_id=article.article_id).count()
            comments_count = ArticleComment.query.filter_by(article_id=article.article_id).count()

            # 收集所有图片和视频URL
            image_urls = {
                'image_url': article.image_url,
                'image_url_2': article.image_url_2,
                'image_url_3': article.image_url_3,
                'image_url_4': article.image_url_4,
                'image_url_5': article.image_url_5,
                'image_url_6': article.image_url_6
            }

            video_urls = {
                'video_url': article.video_url,
                'video_url_2': article.video_url_2,
                'video_url_3': article.video_url_3
            }

            # 解析标签
            try:
                tags = json.loads(article.tags) if article.tags else []
            except:
                tags = []

            result.append({
                'article_id': article.article_id,
                'user_id': article.user_id,
                'username': user.username if user else None,
                'avatar': user.avatar if user else None,  # 添加用户头像
                'title': article.title,
                'content': content,  # 使用解压后的内容
                'location': article.location,
                'tags': tags,  # 添加标签信息
                'popularity': article.popularity,
                'evaluation': article.evaluation,
                **image_urls,
                **video_urls,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                'likes_count': likes_count,
                'favorites_count': favorites_count,
                'comments_count': comments_count
            })

        return success(result, 'User articles retrieved successfully')

    except Exception as e:
        print(f"Error in get_user_articles: {e}")
        import traceback
        traceback.print_exc()
        return error(str(e))

# 此功能在前端实现更合适，已移除

@article_bp.route('/fuzzy_search', methods=['GET'])
def fuzzy_search_articles():
    """
    文章模糊查询 - 用于AI生成模块的自动完成
    """
    try:
        query = request.args.get('query', '').strip()
        limit = int(request.args.get('limit', 10))

        if not query:
            # 如果没有查询词，返回热门文章
            articles = Article.query.order_by(Article.popularity.desc()).limit(limit).all()
        else:
            # 模糊搜索文章标题和地点
            articles = Article.query.filter(
                db.or_(
                    Article.title.like(f'%{query}%'),
                    Article.location.like(f'%{query}%')
                )
            ).limit(limit).all()

        # 获取用户信息
        user_ids = [article.user_id for article in articles]
        users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()}

        result = []
        for article in articles:
            # 获取用户信息
            user = users.get(article.user_id)
            username = user.username if user else '未知用户'

            result.append({
                'article_id': article.article_id,
                'title': article.title,
                'location': article.location,
                'popularity': article.popularity,
                'username': username,
                'created_at': article.created_at.isoformat() if article.created_at else None
            })

        return success(result, 'Fuzzy search completed successfully')

    except Exception as e:
        return error(f'Error in fuzzy search: {str(e)}')

@article_bp.route('/collaborative-recommendations', methods=['POST'])
def get_collaborative_article_recommendations():
    """
    获取基于协同过滤的文章推荐
    """
    try:
        data = request.get_json()

        if not data or 'user_id' not in data:
            return error('缺少user_id参数')

        user_id = data['user_id']
        limit = data.get('limit', 10)

        # 获取推荐系统
        recommendation_system = RecommendationFactory.get_algorithm_recommendation_system()

        # 刷新数据
        recommendation_system.refresh_all_data()

        # 使用协同过滤算法获取文章推荐
        start_time = time.time()
        article_recommendations = recommendation_system.get_collaborative_article_recommendations(user_id, limit)
        elapsed_time = time.time() - start_time

        if not article_recommendations:
            return success({
                'articles': [],
                'elapsed_time': f'{elapsed_time:.2f} 秒',
                'message': '暂无个性化推荐，建议查看热门文章'
            }, '协同过滤推荐获取成功')

        # 获取推荐的文章详情
        article_ids = [article_id for article_id, _ in article_recommendations]
        articles = Article.query.filter(Article.article_id.in_(article_ids)).all()

        # 创建ID到得分的映射
        score_map = {article_id: score for article_id, score in article_recommendations}

        # 获取用户信息
        user_ids = [article.user_id for article in articles]
        users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()}

        # 创建文章服务实例
        article_service = ArticleService()

        # 转换为字典列表，并保持原始排序
        result = []
        for article_id, _ in article_recommendations:
            article = next((a for a in articles if a.article_id == article_id), None)
            if article:
                # 解压文章内容
                try:
                    huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                    content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""
                except Exception as e:
                    print(f"Error decompressing article {article.article_id}: {e}")
                    content = "内容解压失败"

                # 获取点赞、收藏和评论数量
                likes_count = ArticleLike.query.filter_by(article_id=article.article_id).count()
                favorites_count = ArticleFavorite.query.filter_by(article_id=article.article_id).count()
                comments_count = ArticleComment.query.filter_by(article_id=article.article_id).count()

                # 收集所有图片和视频URL
                image_urls = {
                    'image_url': article.image_url,
                    'image_url_2': article.image_url_2,
                    'image_url_3': article.image_url_3,
                    'image_url_4': article.image_url_4,
                    'image_url_5': article.image_url_5,
                    'image_url_6': article.image_url_6
                }

                video_urls = {
                    'video_url': article.video_url,
                    'video_url_2': article.video_url_2,
                    'video_url_3': article.video_url_3
                }

                # 解析标签
                try:
                    tags = json.loads(article.tags) if article.tags else []
                except:
                    tags = []

                article_dict = {
                    'article_id': article.article_id,
                    'user_id': article.user_id,
                    'username': users[article.user_id].username if article.user_id in users else None,
                    'avatar': users[article.user_id].avatar if article.user_id in users else None,
                    'title': article.title,
                    'content': content,
                    'location': article.location,
                    'tags': tags,  # 添加标签信息
                    'popularity': article.popularity,
                    'evaluation': article.evaluation,
                    **image_urls,
                    **video_urls,
                    'created_at': article.created_at.isoformat() if article.created_at else None,
                    'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                    'likes_count': likes_count,
                    'favorites_count': favorites_count,
                    'comments_count': comments_count,
                    'collaborative_score': score_map[article_id],
                    'recommendation_type': 'collaborative'
                }
                result.append(article_dict)

        return success({
            'articles': result,
            'elapsed_time': f'{elapsed_time:.2f} 秒'
        }, '协同过滤推荐获取成功')

    except Exception as e:
        current_app.logger.error(f"获取文章协同过滤推荐出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'获取协同过滤推荐时出错: {str(e)}')

@article_bp.route('/for-you', methods=['POST'])
def get_for_you_article_recommendations():
    """
    获取"为您推荐"的文章

    如果是游客访问模式，则按照热度返回前20篇文章
    如果已经登录，则使用协同过滤推荐
    """
    try:
        data = request.get_json()

        if not data:
            return error('缺少请求数据')

        # 检查是否是游客模式
        is_guest = data.get('is_guest', False)
        limit = data.get('limit', 20)

        if is_guest:
            # 游客模式：按照热度返回前20篇文章
            start_time = time.time()
            articles = Article.query.order_by(Article.popularity.desc()).limit(limit).all()
            elapsed_time = time.time() - start_time

            # 获取用户信息
            user_ids = [article.user_id for article in articles]
            users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()}

            # 创建文章服务实例
            article_service = ArticleService()

            # 构建响应
            result = []
            for article in articles:
                # 解压文章内容
                try:
                    huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                    content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""
                except Exception as e:
                    print(f"Error decompressing article {article.article_id}: {e}")
                    content = "内容解压失败"

                # 获取点赞、收藏和评论数量
                likes_count = ArticleLike.query.filter_by(article_id=article.article_id).count()
                favorites_count = ArticleFavorite.query.filter_by(article_id=article.article_id).count()
                comments_count = ArticleComment.query.filter_by(article_id=article.article_id).count()

                # 收集所有图片和视频URL
                image_urls = {
                    'image_url': article.image_url,
                    'image_url_2': article.image_url_2,
                    'image_url_3': article.image_url_3,
                    'image_url_4': article.image_url_4,
                    'image_url_5': article.image_url_5,
                    'image_url_6': article.image_url_6
                }

                video_urls = {
                    'video_url': article.video_url,
                    'video_url_2': article.video_url_2,
                    'video_url_3': article.video_url_3
                }

                # 解析标签
                try:
                    tags = json.loads(article.tags) if article.tags else []
                except:
                    tags = []

                result.append({
                    'article_id': article.article_id,
                    'user_id': article.user_id,
                    'username': users[article.user_id].username if article.user_id in users else None,
                    'avatar': users[article.user_id].avatar if article.user_id in users else None,
                    'title': article.title,
                    'content': content,
                    'location': article.location,
                    'tags': tags,  # 添加标签信息
                    'popularity': article.popularity,
                    'evaluation': article.evaluation,
                    **image_urls,
                    **video_urls,
                    'created_at': article.created_at.isoformat() if article.created_at else None,
                    'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                    'likes_count': likes_count,
                    'favorites_count': favorites_count,
                    'comments_count': comments_count,
                    'recommendation_type': 'popular'
                })

            return success({
                'articles': result,
                'elapsed_time': f'{elapsed_time:.2f} 秒'
            }, '为游客推荐的文章获取成功')
        else:
            # 登录模式：使用协同过滤推荐
            user_id = data.get('user_id')
            if not user_id:
                return error('缺少user_id参数')

            # 直接调用协同过滤推荐
            return get_collaborative_article_recommendations()

    except Exception as e:
        current_app.logger.error(f"获取为您推荐文章出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'获取为您推荐文章时出错: {str(e)}')

@article_bp.route('/full-text-search', methods=['POST'])
def full_text_search():
    """
    全文检索API
    使用BM25算法进行文档相关性搜索
    """
    try:
        data = request.get_json()

        if not data or 'query' not in data:
            return error('缺少查询参数')

        query = data['query'].strip()
        limit = data.get('limit', 10)

        if not query:
            return error('查询关键词不能为空')

        # 获取所有文章用于搜索
        articles = Article.query.all()

        if not articles:
            return success({
                'articles': [],
                'total': 0,
                'query': query
            }, '没有找到文章')

        # 创建文章服务实例用于解压内容
        article_service = ArticleService()

        # 准备文章数据
        article_data = []
        for article in articles:
            try:
                # 解压文章内容
                huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""

                article_data.append({
                    'article_id': article.article_id,
                    'title': article.title,
                    'content': content,
                    'location': article.location,
                    'popularity': article.popularity,
                    'evaluation': article.evaluation,
                    'created_at': article.created_at.isoformat() if article.created_at else None
                })
            except Exception as e:
                print(f"解压文章 {article.article_id} 内容失败: {e}")
                continue

        # 创建搜索引擎并建立索引
        search_engine = TextSearchEngine()
        search_engine.index_articles(article_data)

        # 执行搜索
        start_time = time.time()
        search_results = search_engine.search(query, limit)
        elapsed_time = time.time() - start_time

        # 获取用户信息
        if search_results:
            article_ids = [result['article_id'] for result in search_results]
            articles_with_users = Article.query.filter(Article.article_id.in_(article_ids)).all()
            user_ids = [article.user_id for article in articles_with_users]
            users = {user.user_id: user for user in User.query.filter(User.user_id.in_(user_ids)).all()}

            # 补充用户信息和其他统计数据
            for result in search_results:
                article_id = result['article_id']
                article_obj = next((a for a in articles_with_users if a.article_id == article_id), None)

                if article_obj:
                    # 获取点赞、收藏和评论数量
                    likes_count = ArticleLike.query.filter_by(article_id=article_id).count()
                    favorites_count = ArticleFavorite.query.filter_by(article_id=article_id).count()
                    comments_count = ArticleComment.query.filter_by(article_id=article_id).count()

                    # 解析标签
                    try:
                        tags = json.loads(article_obj.tags) if article_obj.tags else []
                    except:
                        tags = []

                    # 添加用户信息
                    user = users.get(article_obj.user_id)
                    result['user_id'] = article_obj.user_id
                    result['username'] = user.username if user else None
                    result['avatar'] = user.avatar if user else None
                    result['tags'] = tags  # 添加标签信息
                    result['likes_count'] = likes_count
                    result['favorites_count'] = favorites_count
                    result['comments_count'] = comments_count

                    # 收集图片和视频URL
                    result.update({
                        'image_url': article_obj.image_url,
                        'image_url_2': article_obj.image_url_2,
                        'image_url_3': article_obj.image_url_3,
                        'image_url_4': article_obj.image_url_4,
                        'image_url_5': article_obj.image_url_5,
                        'image_url_6': article_obj.image_url_6,
                        'video_url': article_obj.video_url,
                        'video_url_2': article_obj.video_url_2,
                        'video_url_3': article_obj.video_url_3,
                        'updated_at': article_obj.updated_at.isoformat() if article_obj.updated_at else None
                    })

        return success({
            'articles': search_results,
            'total': len(search_results),
            'query': query,
            'elapsed_time': f'{elapsed_time:.3f} 秒'
        }, f'找到 {len(search_results)} 篇相关文章')

    except Exception as e:
        current_app.logger.error(f"全文检索出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'全文检索时出错: {str(e)}')

@article_bp.route('/search-suggestions', methods=['POST'])
def get_search_suggestions():
    """
    获取搜索建议
    返回包含关键词的句子片段
    """
    try:
        data = request.get_json()

        if not data or 'query' not in data:
            return error('缺少查询参数')

        query = data['query'].strip()
        limit = data.get('limit', 5)

        if not query or len(query) < 2:
            return success({
                'suggestions': [],
                'query': query
            }, '查询关键词太短')

        # 获取所有文章用于搜索
        articles = Article.query.all()

        if not articles:
            return success({
                'suggestions': [],
                'query': query
            }, '没有找到文章')

        # 创建文章服务实例用于解压内容
        article_service = ArticleService()

        # 准备文章数据
        article_data = []
        for article in articles:
            try:
                # 解压文章内容
                huffman_codes = json.loads(article.huffman_codes) if article.huffman_codes else {}
                content = article_service.decompress_text(article.content, huffman_codes) if article.content else ""

                article_data.append({
                    'article_id': article.article_id,
                    'title': article.title,
                    'content': content
                })
            except Exception as e:
                print(f"解压文章 {article.article_id} 内容失败: {e}")
                continue

        # 创建搜索引擎并建立索引
        search_engine = TextSearchEngine()
        search_engine.index_articles(article_data)

        # 查找包含关键词的句子
        suggestions = search_engine.find_sentences_with_keywords(query, limit)

        return success({
            'suggestions': suggestions,
            'query': query
        }, f'找到 {len(suggestions)} 个建议')

    except Exception as e:
        current_app.logger.error(f"获取搜索建议出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'获取搜索建议时出错: {str(e)}')
