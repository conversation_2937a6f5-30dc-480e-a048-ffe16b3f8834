#!/usr/bin/env python3
"""
测试AI图片生成功能的脚本
"""

import requests
import json

def test_image_generation():
    """测试图片生成API"""
    base_url = "http://localhost:5000"
    
    # 测试用例
    test_cases = [
        {
            "prompt": "美丽的日落海滩，金色沙滩，椰子树",
            "size": "1024x1024",
            "style": "realistic",
            "count": 1,
            "description": "测试写实风格图片生成"
        },
        {
            "prompt": "雪山下的湖泊，倒影清晰，蓝天白云",
            "size": "1280x720",
            "style": "artistic",
            "count": 2,
            "description": "测试艺术风格图片生成"
        },
        {
            "prompt": "古典中式庭院，亭台楼阁，荷花池",
            "size": "720x1280",
            "style": "watercolor",
            "count": 1,
            "description": "测试水彩风格图片生成"
        },
        {
            "prompt": "现代都市夜景，霓虹灯光，车水马龙",
            "size": "1920x1080",
            "style": "anime",
            "count": 3,
            "description": "测试动漫风格图片生成"
        }
    ]
    
    print("=" * 80)
    print("测试AI图片生成API")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['description']}")
        print("-" * 40)
        
        try:
            # 调用API
            url = f"{base_url}/api/ai/generate_image"
            data = {
                "prompt": test_case["prompt"],
                "size": test_case["size"],
                "style": test_case["style"],
                "count": test_case["count"]
            }
            
            print(f"请求URL: {url}")
            print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            response = requests.post(url, json=data, timeout=30)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('code') == 0:
                    images = result.get('data', {}).get('images', [])
                    print(f"✅ 成功生成 {len(images)} 张图片")
                    
                    for j, image in enumerate(images, 1):
                        print(f"  图片 {j}: {image.get('url')}")
                        print(f"    尺寸: {image.get('width')}x{image.get('height')}")
                        print(f"    风格: {image.get('style')}")
                        if image.get('fallback'):
                            print(f"    注意: 使用了备用方案")
                else:
                    print(f"❌ API返回错误: {result.get('message', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_invalid_requests():
    """测试无效请求的处理"""
    base_url = "http://localhost:5000"
    url = f"{base_url}/api/ai/generate_image"
    
    print("\n" + "=" * 80)
    print("测试无效请求处理")
    print("=" * 80)
    
    # 测试无效请求
    invalid_cases = [
        {
            "data": {},
            "description": "空请求"
        },
        {
            "data": {"prompt": ""},
            "description": "空提示词"
        },
        {
            "data": {"prompt": "测试", "size": "invalid"},
            "description": "无效尺寸"
        },
        {
            "data": {"prompt": "测试", "style": "invalid"},
            "description": "无效风格"
        },
        {
            "data": {"prompt": "测试", "count": 0},
            "description": "无效数量（0）"
        },
        {
            "data": {"prompt": "测试", "count": 10},
            "description": "无效数量（超过限制）"
        }
    ]
    
    for i, case in enumerate(invalid_cases, 1):
        print(f"\n无效请求测试 {i}: {case['description']}")
        print("-" * 40)
        
        try:
            response = requests.post(url, json=case["data"], timeout=10)
            result = response.json()
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('code') != 0:
                print(f"✅ 正确处理了无效请求: {result.get('message')}")
            else:
                print(f"❌ 应该拒绝无效请求")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    print("开始测试AI图片生成功能...")
    
    # 测试正常请求
    test_image_generation()
    
    # 测试无效请求
    test_invalid_requests()
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)
    print("\n注意事项:")
    print("1. 当前使用的是模拟图片生成，返回的是占位图片")
    print("2. 要使用真实的腾讯混元生图API，需要配置API密钥")
    print("3. 生成的图片URL可以在浏览器中查看")
    print("4. 实际部署时需要替换为真实的API调用")
