<template>
  <div class="diary-community">
    <!-- 页面头部 -->
    <div class="community-header">
      <h1 class="community-title">旅行游记社区</h1>
      <div class="community-actions">
        <el-button type="primary" @click="handlePostClick" class="publish-btn">
          <el-icon><Plus /></el-icon> 发布游记
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-container">
      <div class="search-box">
        <el-autocomplete
          v-model="searchQuery"
          :fetch-suggestions="querySearchAsync"
          placeholder="搜索游记标题或内容..."
          clearable
          @input="handleSearchInput"
          @select="handleSelect"
          :trigger-on-focus="false"
          :debounce="300"
          value-key="value"
          popper-class="search-suggestions-popper"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #default="{ item }">
            <div class="suggestion-item">
              <div class="suggestion-title">{{ item.title }}</div>
              <div class="suggestion-content" v-if="item.highlighted_sentence" v-html="item.highlighted_sentence"></div>
              <div class="suggestion-meta">
                <span class="suggestion-author">{{ item.username || item.author?.username || '未知作者' }}</span>
                <span class="suggestion-date">{{ formatSuggestionDate(item.created_at || item.createTime) }}</span>
              </div>
            </div>
          </template>
        </el-autocomplete>
      </div>

      <!-- 搜索模式切换 -->
      <div class="search-mode-toggle">
        <span class="filter-label">搜索模式：</span>
        <el-radio-group v-model="searchMode" size="small">
          <el-radio-button label="title">标题搜索</el-radio-button>
          <el-radio-button label="fulltext">全文检索</el-radio-button>
        </el-radio-group>
      </div>

      <div class="filter-options">
        <span class="filter-label">排序方式：</span>
        <el-radio-group v-model="sortOption" @change="handleSort">
          <el-radio-button label="for-you">为您推荐</el-radio-button>
          <el-radio-button label="latest">最新发布</el-radio-button>
          <el-radio-button label="heat">热度最高</el-radio-button>
          <el-radio-button label="rating">评分最高</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 游记列表 -->
    <div class="diary-list" v-if="displayedDiaryList.length">
      <el-row :gutter="20">
        <el-col :span="24" v-for="diary in displayedDiaryList" :key="diary.id">
          <DiaryCard
            :diary="diary"
            @like="handleLike"
            @favorite="handleFavorite"
            @view="handleViewClick"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <el-empty :description="searchQuery ? '没有找到匹配的游记' : '暂无游记，快来发布第一条吧！'">
        <template #empty>
          <el-button type="primary" @click="handlePostClick" v-if="!searchQuery">
            <el-icon><Plus /></el-icon> 发布游记
          </el-button>
          <el-button @click="clearSearch" v-else>
            清除搜索
          </el-button>
        </template>
      </el-empty>
    </div>

    <!-- 发布旅游日记对话框 -->
    <el-dialog
      v-model="postDialogVisible"
      title="发布旅行游记"
      width="800px"
      destroy-on-close
    >
      <el-form
        :model="newDiary"
        label-width="100px"
        :rules="diaryRules"
        ref="diaryFormRef"
        status-icon
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="newDiary.title"
            placeholder="请输入游记标题"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="旅行地点" prop="location">
          <el-autocomplete
            v-model="newDiary.location"
            :fetch-suggestions="queryLocationSuggestions"
            placeholder="请输入旅行地点"
            clearable
            @select="handleLocationSelect"
            :trigger-on-focus="true"
            :highlight-first-item="true"
            :debounce="300"
            style="width: 100%"
          >
            <template #default="{ item }">
              <div class="location-suggestion-item">
                <div class="location-name">{{ item.name }}</div>
                <div class="location-type">{{ item.type === 0 ? '学校' : '景点' }}</div>
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <!-- 自定义标签选择器 -->
          <div class="custom-tag-selector">
            <div class="tag-input-container">
              <input
                type="text"
                v-model="tagInput"
                @keyup.enter="addCustomTag"
                placeholder="添加我的标签..."
                class="tag-input"
              />
              <el-button type="primary" size="small" @click="addCustomTag">添加</el-button>
            </div>

            <div class="tag-options">
              <p class="tag-section-title">选择预设标签:</p>
              <div class="preset-tags">
                <el-tag
                  v-for="item in tagOptions"
                  :key="item.value"
                  class="preset-tag"
                  @click="addPresetTag(item.value)"
                  :class="{ 'selected': newDiary.tags.includes(item.value) }"
                  effect="light"
                >
                  {{ item.label }}
                </el-tag>
              </div>
            </div>

            <div class="selected-tags" v-if="newDiary.tags && newDiary.tags.length > 0">
              <p class="tag-section-title">已选标签:</p>
              <div class="tags-container">
                <el-tag
                  v-for="tag in newDiary.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
                  effect="light"
                  type="info"
                  class="selected-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input
            v-model="newDiary.content"
            type="textarea"
            placeholder="请输入游记内容，分享您的旅行体验、感受和建议..."
            :rows="8"
            maxlength="5000"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="图片上传">
          <el-upload
            class="diary-uploader"
            action="#"
            list-type="picture-card"
            :file-list="uploadFileList"
            :key="uploadKey"
            :auto-upload="false"
            :on-change="handleImageUpload"
            :on-remove="handleImageRemove"
            :limit="6"
            multiple
            accept="image/*"
            ref="uploadRef"
          >
            <template #default>
              <el-icon><Plus /></el-icon>
              <div class="el-upload__text">点击上传</div>
            </template>
            <template #file="{ file }">
              <div class="upload-item">
                <img class="upload-image" :src="file.url" alt="预览图" />
                <div class="upload-actions">
                  <el-icon class="delete-icon" @click.stop="handleImageRemove(file)"><Delete /></el-icon>
                </div>
              </div>
            </template>
          </el-upload>
          <div class="upload-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>最多上传6张图片，大小不超过50MB，支持jpg、png、gif格式</span>
          </div>
        </el-form-item>

        <el-form-item label="视频上传">
          <div class="video-upload-container">
            <div v-if="newDiary.videos.length < 3" class="video-upload-button" @click="triggerVideoUpload">
              <el-icon><VideoCamera /></el-icon>
              <span>点击上传视频 ({{ newDiary.videos.length }}/3)</span>
            </div>

            <input
              type="file"
              ref="videoInput"
              style="display: none"
              accept="video/mp4,video/webm,video/ogg"
              @change="handleVideoUpload"
            />

            <div v-for="(video, index) in newDiary.videos" :key="index" class="video-preview">
              <video
                controls
                class="preview-video"
                :src="newDiary.videoUrls[index]"
              ></video>
              <div class="video-actions">
                <el-button type="danger" size="small" @click="removeVideo(index)">
                  <el-icon><Delete /></el-icon> 移除视频
                </el-button>
              </div>
            </div>
          </div>
          <div class="upload-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>最多上传3个视频，大小不超过50MB，支持mp4、webm、ogg格式</span>
          </div>
        </el-form-item>

        <el-form-item label="隐私设置">
          <el-radio-group v-model="newDiary.privacy">
            <el-radio :label="'public'">公开</el-radio>
            <el-radio :label="'private'">私密</el-radio>
          </el-radio-group>
          <div class="privacy-tip">
            <span v-if="newDiary.privacy === 'public'">公开的游记所有人可见</span>
            <span v-else>私密的游记仅自己可见</span>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelPost">取消</el-button>
          <el-button type="primary" @click="handlePostSubmit" :loading="submitting">发布游记</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Plus, Search, Delete, InfoFilled, VideoCamera } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { article as articleApi, location as locationApi } from '@/api/articles'
import axios from 'axios'
import DiaryCard from '@/components/DiaryCard.vue'
import defaultAvatar from '@/assets/belog.jpg'


const router = useRouter()
const uploadKey = ref(Date.now())
// 搜索和筛选相关
const searchQuery = ref('')
const searchMode = ref('title') // 搜索模式：title(标题搜索) 或 fulltext(全文检索)
const sortOption = ref('latest') // 默认按最新发布排序

// 文章列表数据
const diaryList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取文章列表
const fetchArticles = async () => {
  loading.value = true;
  try {
    // 检查是否是"为您推荐"模式
    if (sortOption.value === 'for-you') {
      await fetchForYouRecommendations();
      return;
    }

    const params = {
      page: currentPage.value,
      size: pageSize.value,
      sort: sortOption.value === 'latest' ? 'created_at' :
            sortOption.value === 'heat' ? 'popularity' : 'evaluation',
      direction: 'desc'
    };

    console.log('获取文章列表，参数:', params);

    // 获取token
    const token = localStorage.getItem('token') || localStorage.getItem('authToken');

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    if (searchQuery.value) {
      // 如果有搜索关键词，根据搜索模式选择API
      console.log('使用搜索API，关键词:', searchQuery.value, '搜索模式:', searchMode.value);

      try {
        let res;
        if (searchMode.value === 'fulltext') {
          // 使用全文检索
          res = await articleApi.fullTextSearch(searchQuery.value, pageSize.value);
          console.log('全文检索结果:', res);
        } else {
          // 使用标题搜索
          res = await articleApi.searchArticlesByTitle(searchQuery.value);
          console.log('标题搜索结果:', res);
        }

        if (res.data && res.data.code === 0) {
          processArticles(res.data.data);
          // 在这里添加获取文章统计数据的代码
          fetchArticleStats();
        } else {
          // 如果API函数失败，尝试直接使用axios
          const axiosRes = await axios.get(`http://localhost:5000/api/articles/search?keyword=${encodeURIComponent(searchQuery.value)}`, {
            headers
          });

          console.log('axios搜索结果:', axiosRes);

          if (axiosRes.data && axiosRes.data.code === 0) {
            processArticles(axiosRes.data.data);
            // 在这里添加获取文章统计数据的代码
            fetchArticleStats();
          } else {
            ElMessage.error(axiosRes.data?.message || '获取文章列表失败');
          }
        }
      } catch (searchError) {
        console.error('搜索文章失败:', searchError);

        // 尝试直接使用axios作为备选方案
        try {
          const axiosRes = await axios.get(`http://localhost:5000/api/articles/search?keyword=${encodeURIComponent(searchQuery.value)}`, {
            headers
          });

          console.log('备选axios搜索结果:', axiosRes);

          if (axiosRes.data && axiosRes.data.code === 0) {
            processArticles(axiosRes.data.data);
            // 在这里添加获取文章统计数据的代码
            fetchArticleStats();
          } else {
            ElMessage.error(axiosRes.data?.message || '获取文章列表失败');
          }
        } catch (axiosError) {
          console.error('备选axios搜索失败:', axiosError);
          ElMessage.error('搜索文章失败，请稍后重试');
        }
      }
    } else {
      // 否则获取所有文章
      console.log('获取所有文章');

      try {
        // 尝试使用API函数
        const res = await articleApi.getArticles(params);
        console.log('获取文章结果:', res);

        if (res.data && res.data.code === 0) {
          processArticles(res.data.data);
          // 在这里添加获取文章统计数据的代码
          fetchArticleStats();
        } else {
          // 如果API函数失败，尝试直接使用axios
          // 构建查询参数
          const queryParams = new URLSearchParams();
          queryParams.append('page', params.page);
          queryParams.append('size', params.size);
          queryParams.append('sort', params.sort);
          queryParams.append('direction', params.direction);

          const axiosRes = await axios.get(`http://localhost:5000/api/articles?${queryParams.toString()}`, {
            headers
          });

          console.log('axios获取文章结果:', axiosRes);

          if (axiosRes.data && axiosRes.data.code === 0) {
            processArticles(axiosRes.data.data);
            // 在这里添加获取文章统计数据的代码
            fetchArticleStats();
          } else {
            ElMessage.error(axiosRes.data?.message || '获取文章列表失败');
          }
        }
      } catch (getError) {
        console.error('获取所有文章失败:', getError);

        // 尝试直接使用axios作为备选方案
        try {
          // 构建查询参数
          const queryParams = new URLSearchParams();
          queryParams.append('page', params.page);
          queryParams.append('size', params.size);
          queryParams.append('sort', params.sort);
          queryParams.append('direction', params.direction);

          const axiosRes = await axios.get(`http://localhost:5000/api/articles?${queryParams.toString()}`, {
            headers
          });

          console.log('备选axios获取文章结果:', axiosRes);

          if (axiosRes.data && axiosRes.data.code === 0) {
            processArticles(axiosRes.data.data);
            // 在这里添加获取文章统计数据的代码
            fetchArticleStats();
          } else {
            ElMessage.error(axiosRes.data?.message || '获取文章列表失败');
          }
        } catch (axiosError) {
          console.error('备选axios获取文章失败:', axiosError);
          ElMessage.error('获取文章列表失败，请稍后重试');
        }
      }
    }
  } catch (error) {
    console.error('获取文章列表失败:', error);
    ElMessage.error('获取文章列表失败，请稍后重试');
    // 出错时设置空列表，不再使用模拟数据
    diaryList.value = [];
  } finally {
    loading.value = false;
  }
};

// 根据标题生成默认标签
const getDefaultTags = (title) => {
  if (!title) return ['旅游景点', '自然景观'];

  // 如果标题包含某些关键词，返回相应的标签
  // 检查标题中是否包含地点关键词
  const locationKeywords = {
    '大学': ['大学', '文化古迹'],
    '公园': ['公园', '自然景观'],
    '动物园': ['动物园', '旅游景点'],
    '古建筑': ['古建筑', '文化古迹'],
    '古迹': ['文化古迹', '古建筑'],
    '自然': ['自然景观', '旅游景点'],
    '景点': ['旅游景点', '自然景观'],
    '游乐': ['游乐园', '旅游景点'],
    '寺庙': ['寺庙', '文化古迹']
  };

  // 遍历地点关键词，检查标题中是否包含
  const titleLower = title.toLowerCase();
  for (const [keyword, locationTags] of Object.entries(locationKeywords)) {
    if (titleLower.includes(keyword.toLowerCase())) {
      return locationTags;
    }
  }

  // 如果没有匹配到关键词，返回一些通用标签
  return ['旅游景点', '自然景观'];
}

// 处理从API获取的文章数据
const processArticles = (data) => {
  // 清空现有列表
  diaryList.value = [];

  if (!data) {
    console.warn('API返回的数据为空');
    return;
  }

  console.log('处理文章数据:', data);
  console.log('数据类型:', typeof data);
  console.log('是否为数组:', Array.isArray(data));
  if (typeof data === 'object') {
    console.log('对象键:', Object.keys(data));
  }

  // 设置总数
  if (data.total !== undefined) {
    total.value = data.total;
  } else if (data.articles && Array.isArray(data.articles)) {
    total.value = data.articles.length;
  } else if (Array.isArray(data)) {
    total.value = data.length;
  } else {
    total.value = 0;
  }

  // 确定文章数组
  let articles = [];
  if (data.articles && Array.isArray(data.articles)) {
    articles = data.articles;
    console.log('使用data.articles作为文章数组');
  } else if (Array.isArray(data)) {
    articles = data;
    console.log('使用data作为文章数组');
  } else if (data.data && Array.isArray(data.data)) {
    articles = data.data;
    console.log('使用data.data作为文章数组');
  } else {
    console.warn('无法确定文章数组');
    return;
  }

  if (articles.length > 0) {
    console.log(`找到${articles.length}篇文章`);

    // 处理每篇日记
    articles.forEach(article => {
      console.log('处理文章:', article);
      console.log('文章ID:', article.article_id);
      console.log('点赞数:', article.likes_count);
      console.log('收藏数:', article.favorites_count);
      console.log('评论数:', article.comments_count);

      // 确保所有必要的字段都存在
      const diaryItem = {
        id: article.article_id,
        title: article.title || '无标题',
        content: article.content || '',
        createTime: article.created_at ? new Date(article.created_at).getTime() : Date.now(),
        images: article.image_url ? [getFullImageUrl(article.image_url)] : [],
        video: article.video_url ? getFullVideoUrl(article.video_url) : null,
        commentsCount: article.comments_count || 0,
        comments_count: article.comments_count || 0,
        likes: article.likes_count || 0,
        likes_count: article.likes_count || 0,
        favorites: article.favorites_count || 0,
        favorites_count: article.favorites_count || 0,
        views: article.popularity || 0,
        popularity: article.popularity || 0,
        location_id: article.location_id || null,
        location: article.location || article.location_name || '未知地点',
        avgRating: article.evaluation || 0, // 平均评分
        ratingCount: article.rating_count || 0, // 评分人数
        author: {
          username: article.username || '未知用户',
          avatar: getFullAvatarUrl(article.avatar || article.user_avatar)
        },
        isLiked: article.is_liked || false,
        isFavorited: article.is_favorited || false,
        tags: article.tags || getDefaultTags(article.title),
        heat: 0 // 初始化热度为0
      };

      // 确保数值字段是数字类型
      diaryItem.commentsCount = Number(diaryItem.commentsCount);
      diaryItem.comments_count = Number(diaryItem.comments_count);
      diaryItem.likes = Number(diaryItem.likes);
      diaryItem.likes_count = Number(diaryItem.likes_count);
      diaryItem.favorites = Number(diaryItem.favorites);
      diaryItem.favorites_count = Number(diaryItem.favorites_count);
      diaryItem.views = Number(diaryItem.views);
      diaryItem.popularity = Number(diaryItem.popularity);

      // 打印处理后的数据
      console.log('处理后的数据:');
      console.log('- 点赞数:', diaryItem.likes, diaryItem.likes_count);
      console.log('- 收藏数:', diaryItem.favorites, diaryItem.favorites_count);
      console.log('- 评论数:', diaryItem.commentsCount, diaryItem.comments_count);
      console.log('- 浏览数:', diaryItem.views, diaryItem.popularity);

      // 计算热度
      updateHeat(diaryItem);

      // 将处理后的数据添加到列表中
      console.log('添加到列表的日记项:', diaryItem);
      diaryList.value.push(diaryItem);

      // 异步获取用户是否已点赞和收藏（不影响初始显示）
      const checkUserInteractions = async () => {
        try {
          // 检查当前用户是否已点赞
          const userId = localStorage.getItem('userId');
          if (userId) {
            try {
              const checkLikeResponse = await axios.post('http://localhost:5000/api/article_like/check', {
                user_id: userId,
                article_id: article.article_id
              });
              if (checkLikeResponse.data && checkLikeResponse.data.code === 0) {
                // 找到对应的日记项并更新
                const index = diaryList.value.findIndex(item => item.id === article.article_id);
                if (index !== -1) {
                  diaryList.value[index].isLiked = checkLikeResponse.data.data.is_liked;
                  console.log(`文章ID: ${article.article_id} 的点赞状态更新为: ${diaryList.value[index].isLiked}`);
                }
              }
            } catch (error) {
              console.error(`检查点赞状态失败:`, error);
            }

            // 检查当前用户是否已收藏
            try {
              const checkFavoriteResponse = await axios.post('http://localhost:5000/api/favorites/check', {
                user_id: userId,
                article_id: article.article_id
              });
              if (checkFavoriteResponse.data && checkFavoriteResponse.data.code === 0) {
                // 找到对应的日记项并更新
                const index = diaryList.value.findIndex(item => item.id === article.article_id);
                if (index !== -1) {
                  diaryList.value[index].isFavorited = checkFavoriteResponse.data.data.is_favorite;
                  console.log(`文章ID: ${article.article_id} 的收藏状态更新为: ${diaryList.value[index].isFavorited}`);
                }
              }
            } catch (error) {
              console.error(`检查收藏状态失败:`, error);
            }
          }

          // 获取平均评分
          try {
            const ratingResponse = await axios.get(`http://localhost:5000/api/article_score/average/${article.article_id}`);
            if (ratingResponse.data && ratingResponse.data.code === 0) {
              // 找到对应的日记项并更新
              const index = diaryList.value.findIndex(item => item.id === article.article_id);
              if (index !== -1) {
                // 格式化评分，保留一位小数
                const rawAvgRating = ratingResponse.data.data.average_score || 0;
                diaryList.value[index].avgRating = parseFloat(formatRatingScore(rawAvgRating));
                diaryList.value[index].ratingCount = ratingResponse.data.data.rating_count || 0;
                console.log(`文章ID: ${article.article_id} 的平均评分更新为: ${diaryList.value[index].avgRating}, 评分人数: ${diaryList.value[index].ratingCount}`);
              }
            }
          } catch (error) {
            console.error(`获取文章ID: ${article.article_id} 的平均评分失败:`, error);
          }
        } catch (error) {
          console.error(`获取文章ID: ${article.article_id} 的附加信息失败:`, error);
        }
      };

      // 执行异步检查
      checkUserInteractions();
    });

    console.log('处理后的日记列表:', diaryList.value);
  } else {
    console.log('未找到文章');
  }
};

// 表单引用
const diaryFormRef = ref(null)

// 发布相关状态
const postDialogVisible = ref(false)
const submitting = ref(false)
const newDiary = reactive({
  title: '',
  content: '',
  location: '',
  tags: [],
  images: [],
  videos: [],
  videoUrls: [],
  privacy: 'public'
})

// 视频上传相关
const videoInput = ref(null)
const uploadRef = ref(null)

// 标签输入
const tagInput = ref('')

// 上传文件列表
const uploadFileList = ref([])

// 表单验证规则
const diaryRules = {
  title: [
    { required: true, message: '请输入游记标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度应在2-50个字符之间', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请选择或输入旅行地点', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入游记内容', trigger: 'blur' },
    { min: 10, message: '内容至少需要10个字符', trigger: 'blur' }
  ]
}

// 地点选项
const locationOptions = ref([
  { value: 1, label: '北京' },
  { value: 2, label: '上海' },
  { value: 3, label: '广州' },
  { value: 4, label: '深圳' },
  { value: 5, label: '杭州' },
  { value: 6, label: '成都' },
  { value: 7, label: '西安' },
  { value: 8, label: '三亚' },
  { value: 9, label: '丽江' },
  { value: 10, label: '大理' }
])

// 标签选项 - 与数据库中的地点类型关键词匹配
const tagOptions = [
  { value: '大学', label: '大学' },
  { value: '公园', label: '公园' },
  { value: '动物园', label: '动物园' },
  { value: '古建筑', label: '古建筑' },
  { value: '文化古迹', label: '文化古迹' },
  { value: '自然景观', label: '自然景观' },
  { value: '旅游景点', label: '旅游景点' },
  { value: '游乐园', label: '游乐园' },
  { value: '寺庙', label: '寺庙' }
]

// 图片上传处理
const handleImageUpload = (file) => {

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  // eslint-disable-next-line no-unused-vars
  const isLt50M = file.raw.size / 1024 / 1024 < 50
  // 不显示错误提示，直接通过
const previewUrl = URL.createObjectURL(file.raw)
  // 读取文件
   uploadFileList.value.push({
    ...file,
    url: previewUrl
  })
   newDiary.images.push(file.raw)
  // const reader = new FileReader()
  // reader.onload = (e) => {
  //   file.url = e.target.result
  //   // 将图片URL添加到newDiary.images数组中
  //   if (!newDiary.images.includes(e.target.result)) {
  //     newDiary.images.push(e.target.result)
  //   }
  // }
  // reader.readAsDataURL(file.raw)
  return false // 阻止自动上传
}

const handleImageRemove = (file) => {
  // 从 uploadFileList 移除
  uploadFileList.value = uploadFileList.value.filter(f => f.uid !== file.uid)

  // 从 newDiary.images 移除对应的原始文件
  newDiary.images = newDiary.images.filter(
    rawFile => rawFile.uid !== file.uid
  )

  // 释放内存
  URL.revokeObjectURL(file.url)
   uploadKey.value = Date.now()
}


const formatRatingScore = (value) => {
  // 确保值是数字
  const numValue = parseFloat(value);
  if (isNaN(numValue)) return '0.0';

  return parseFloat(numValue.toFixed(1)).toFixed(1);
};
const getFullImageUrl = (url) => {
  if (!url) return require('@/assets/belog.jpg'); // 使用已有的图片作为替代

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // 如果是相对路径，添加基础URL
  return `http://localhost:5000${url}`;
}

// 获取完整头像URL
const getFullAvatarUrl = (url) => {
  if (!url || url === '' || url === null || url === undefined || url === 'default_avatar.jpg') {
    return defaultAvatar;
  }

  // 如果是完整URL或数据URL，直接返回
  if (url.startsWith('http') || url.startsWith('data:')) {
    return url;
  }

  // 如果是相对路径，添加后端基础URL
  if (url.startsWith('/uploads/')) {
    return `http://localhost:5000${url}`;
  }

  // 其他情况返回默认头像
  return defaultAvatar;
}

// 获取完整视频URL
const getFullVideoUrl = (url) => {
  if (!url) return '';

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // 如果是相对路径，添加基础URL
  return `http://localhost:5000${url}`;
}

// 获取地点名称 (保留以备将来使用)
// const getLocationName = (locationId) => {
//   if (!locationId) return '未知地点'
//   const location = locationOptions.value.find(loc => loc.value === locationId)
//   return location ? location.label : '未知地点'
// }

// 注意：stripHtml、handleImageError和handleVideoError函数已移至DiaryCard组件中

// 这个函数已经在下面定义过了，这里删除重复定义

// 对话框控制
const handlePostClick = () => {
  postDialogVisible.value = true
}

// 取消发布
const cancelPost = () => {
  ElMessageBox.confirm(
    '确定要取消发布吗？已编辑的内容将不会保存。',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    }
  ).then(() => {
    postDialogVisible.value = false
    resetForm()
  }).catch(() => {
    // 用户选择继续编辑，不做任何操作
  })
}

// 添加自定义标签
const addCustomTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !newDiary.tags.includes(tag)) {
    newDiary.tags.push(tag)
  }
  tagInput.value = '' // 清空输入框
}

// 添加预设标签
const addPresetTag = (tag) => {
  if (!newDiary.tags.includes(tag)) {
    newDiary.tags.push(tag)
  } else {
    // 如果已经选择了该标签，则移除它
    removeTag(tag)
  }
}

// 移除标签
const removeTag = (tag) => {
  const index = newDiary.tags.indexOf(tag)
  if (index !== -1) {
    newDiary.tags.splice(index, 1)
  }
}

// 视频上传处理
const handleVideoUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件类型
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return
  }

  // 检查文件大小
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('视频大小不能超过50MB!')
    return
  }

  // 检查是否已达到最大视频数量
  if (newDiary.videos.length >= 3) {
    ElMessage.warning('最多只能上传3个视频!')
    return
  }

  // 保存文件引用
  newDiary.videos.push(file)

  // 创建预览URL
  const videoUrl = URL.createObjectURL(file)
  newDiary.videoUrls.push(videoUrl)

  // 重置文件输入框，以便可以选择相同的文件
  if (videoInput.value) {
    videoInput.value.value = ''
  }
}

// 触发视频上传
const triggerVideoUpload = () => {
  if (videoInput.value) {
    videoInput.value.click()
  }
}

// 移除视频
const removeVideo = (index) => {
  // 释放URL对象
  if (newDiary.videoUrls[index]) {
    URL.revokeObjectURL(newDiary.videoUrls[index])
  }

  // 从数组中移除
  newDiary.videos.splice(index, 1)
  newDiary.videoUrls.splice(index, 1)
}

// 重置表单
const resetForm = () => {
  if (diaryFormRef.value) {
    diaryFormRef.value.resetFields()
  }
  newDiary.title = ''
  newDiary.content = ''
  newDiary.location = ''
  newDiary.tags = []
  newDiary.images = []

  // 清理视频资源
  newDiary.videos.forEach(video => {
    if (video.videoUrl) {
      URL.revokeObjectURL(video.videoUrl)
    }
  })
  newDiary.videos = []
  newDiary.videoUrls = []
  newDiary.privacy = 'public'

  // 重置标签输入
  tagInput.value = ''

  // 重置视频上传输入框
  if (videoInput.value) {
    videoInput.value.value = ''
  }

  // 重置图片上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  // 清空上传文件列表
  uploadFileList.value = []
}

// 提交发布
const handlePostSubmit = () => {
  if (!diaryFormRef.value) return

  diaryFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请完善表单信息')
      return
    }

    // 开始提交
    submitting.value = true

    try {
      // 获取当前用户ID
      const userId = localStorage.getItem('userId') || 1

      // 准备文章数据
      const articleData = {
        user_id: userId,
        title: newDiary.title,
        content: newDiary.content,
        location: newDiary.location,
        tags: newDiary.tags  // 添加标签数据
      }

      // 如果有图片，上传图片（最多6张）
      const imageUrls = []
if (newDiary.images.length > 0) {
  const token = localStorage.getItem('token') || localStorage.getItem('authToken')

  // 直接遍历 File 对象
  for (const [index, file] of newDiary.images.entries()) {
    try {
      // 直接使用 File 对象
      const formData = new FormData()
      formData.append('file', file)
      formData.append('image_index', index + 1)

      console.log(`准备上传图片 ${index+1}:`, file.name, file.size, file.type)

      const uploadRes = await axios.post('http://localhost:5000/api/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': token ? `Bearer ${token}` : undefined
        }
      })

      if (uploadRes.data.code === 0) {
        imageUrls.push(uploadRes.data.data.url)
        console.log(`图片 ${index+1} 上传成功，URL:`, uploadRes.data.data.url)
      }
    } catch (error) {
      console.error(`图片 ${index+1} 上传失败:`, error)
      ElMessage.warning(`图片 ${index+1} 上传失败，将只保存其他内容`)
    }
  }
}

      // 如果有视频，上传视频（最多3个）
      const videoUrls = []
      if (newDiary.videos.length > 0) {
        // 获取token
        const token = localStorage.getItem('token') || localStorage.getItem('authToken')

        // 逐个上传视频
        for (let i = 0; i < Math.min(newDiary.videos.length, 3); i++) {
          const video = newDiary.videos[i]
          try {
            // 上传视频
            const videoFormData = new FormData()
            videoFormData.append('file', video)

            // 如果是为文章上传，添加视频序号
            videoFormData.append('video_index', i + 1)

            console.log(`准备上传视频 ${i+1}:`, video.name, video.size, video.type)

            const videoUploadRes = await axios.post('http://localhost:5000/api/upload/video', videoFormData, {
              headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': token ? `Bearer ${token}` : undefined
              }
            })
            console.log(`视频 ${i+1} 上传响应:`, videoUploadRes.data)

            if (videoUploadRes.data.code === 0) {
              videoUrls.push(videoUploadRes.data.data.url)
              console.log(`视频 ${i+1} 上传成功，URL:`, videoUploadRes.data.data.url)
            }
          } catch (error) {
            console.error(`视频 ${i+1} 上传失败:`, error)

            if (error.response) {
              console.error('视频上传错误响应:', error.response.data)
              console.error('视频上传错误状态码:', error.response.status)
            }

            ElMessage.warning(`视频 ${i+1} 上传失败，将只保存其他内容`)
          }
        }
      }

      // 如果成功上传了图片，添加到文章数据中
      if (imageUrls.length > 0) {
        articleData.image_url = imageUrls[0]
        if (imageUrls.length > 1) articleData.image_url_2 = imageUrls[1]
        if (imageUrls.length > 2) articleData.image_url_3 = imageUrls[2]
        if (imageUrls.length > 3) articleData.image_url_4 = imageUrls[3]
        if (imageUrls.length > 4) articleData.image_url_5 = imageUrls[4]
        if (imageUrls.length > 5) articleData.image_url_6 = imageUrls[5]
      }

      // 如果成功上传了视频，添加到文章数据中
      if (videoUrls.length > 0) {
        articleData.video_url = videoUrls[0]
        if (videoUrls.length > 1) articleData.video_url_2 = videoUrls[1]
        if (videoUrls.length > 2) articleData.video_url_3 = videoUrls[2]
      }

      // 提交文章数据
      console.log('准备提交文章数据:', articleData)

      // 获取token
      const token = localStorage.getItem('token') || localStorage.getItem('authToken')

      // 使用axios实例发送请求，确保正确设置headers
      const response = await axios.post('http://localhost:5000/api/articles', articleData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : undefined
        }
      })

      if (response.data.code === 0) {
        // 获取当前用户信息
        let username = '当前用户';
        let avatar = null;

        try {
          const userStr = localStorage.getItem('currentUser');
          if (userStr) {
            const userData = JSON.parse(userStr);
            username = userData.username || '当前用户';
            avatar = userData.avatar;
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }

        // 创建新游记对象（用于前端显示）
        const newDiaryObj = {
          id: response.data.data.article_id,
          title: newDiary.title,
          content: newDiary.content,
          author: {
            username: username,
            avatar: getFullAvatarUrl(avatar)
          },
          createTime: Date.now(),
          images: imageUrls,
          videos: videoUrls,
          commentsCount: 0,
          likes: 0,
          favorites: 0,
          views: 1,
          isLiked: false,
          isFavorited: false,
          userRating: 0,
          avgRating: 0,
          location: locationOptions.value.find(item => item.value === newDiary.location)?.label || '',
          tags: newDiary.tags,
          privacy: newDiary.privacy
        }

        // 添加到列表开头
        diaryList.value.unshift(newDiaryObj)

        // 关闭对话框
        postDialogVisible.value = false
        // 重置表单
        resetForm()

        // 显示成功消息
        ElMessage.success('游记发布成功')
      } else {
        ElMessage.error(response.data.message || '游记发布失败')
      }
    } catch (error) {
      console.error('游记发布失败:', error)

      // 详细记录错误信息
      if (error.response) {
        // 服务器返回了错误状态码
        console.error('错误响应数据:', error.response.data)
        console.error('错误状态码:', error.response.status)
        console.error('错误响应头:', error.response.headers)

        // 显示服务器返回的错误信息
        const errorMsg = error.response.data?.message || error.response.data?.error || '服务器错误'
        ElMessage.error(`游记发布失败: ${errorMsg} (状态码: ${error.response.status})`)
      } else if (error.request) {
        // 请求已发送但没有收到响应
        console.error('请求未收到响应:', error.request)
        ElMessage.error('游记发布失败: 服务器无响应')
      } else {
        // 请求设置时出现错误
        console.error('请求错误:', error.message)
        ElMessage.error(`游记发布失败: ${error.message}`)
      }
    } finally {
      // 结束提交状态
      submitting.value = false
    }
  })
}

// 处理点赞
const handleLike = async (diary) => {
  try {
    console.log('点赞前状态:', diary.isLiked, '点赞数:', diary.likes, diary.likes_count);

    // 记录原始热度值
    const oldHeat = diary.heat;

    // 获取当前用户ID
    const userId = localStorage.getItem('userId');
    if (!userId) {
      ElMessage.warning('请先登录后再点赞');
      return;
    }

    // 更新前端状态
    diary.isLiked = !diary.isLiked;

    // 更新所有点赞计数字段
    const change = diary.isLiked ? 1 : -1;
    if (diary.likes !== undefined) diary.likes = Number(diary.likes) + change;
    if (diary.likes_count !== undefined) diary.likes_count = Number(diary.likes_count) + change;

    console.log('点赞后状态:', diary.isLiked, '点赞数:', diary.likes, diary.likes_count);

    // 更新热度
    updateHeat(diary);

    // 计算热度变化
    const heatChange = diary.heat - oldHeat;

    // 调用API更新点赞状态
    try {
      let response;
      if (diary.isLiked) {
        // 添加点赞
        response = await axios.post('http://localhost:5000/api/article_like', {
          user_id: userId,
          article_id: diary.id
        });
      } else {
        // 取消点赞
        response = await axios.post('http://localhost:5000/api/article_like/unlike', {
          user_id: userId,
          article_id: diary.id
        });
      }

      console.log('点赞API响应:', response);

      if (response.data && response.data.code === 0) {
        // 获取最新的点赞数
        const likeCountResponse = await axios.get(`http://localhost:5000/api/article_like/count/${diary.id}`);
        if (likeCountResponse.data && likeCountResponse.data.code === 0) {
          const newLikeCount = Number(likeCountResponse.data.data.like_count);
          diary.likes = newLikeCount;
          diary.likes_count = newLikeCount;
          console.log('更新后的点赞数:', newLikeCount);

          // 强制更新UI
          diaryList.value = [...diaryList.value];
        }

        // 调用API更新文章人气
        await articleApi.updateArticlePopularity(diary.id);

        console.log('点赞API调用成功');
      } else {
        // API调用失败，回滚UI状态
        diary.isLiked = !diary.isLiked;
        if (diary.likes !== undefined) diary.likes = Number(diary.likes) - change;
        if (diary.likes_count !== undefined) diary.likes_count = Number(diary.likes_count) - change;
        updateHeat(diary);
        ElMessage.error(response.data?.message || '操作失败');
        return;
      }
    } catch (apiError) {
      console.error('点赞API调用失败:', apiError);
      // API调用失败，回滚UI状态
      diary.isLiked = !diary.isLiked;
      if (diary.likes !== undefined) diary.likes = Number(diary.likes) - change;
      if (diary.likes_count !== undefined) diary.likes_count = Number(diary.likes_count) - change;
      updateHeat(diary);
      ElMessage.error('操作失败，请稍后重试');
      return;
    }

    // 显示提示
    ElMessage({
      message: diary.isLiked
        ? `点赞成功${heatChange > 0 ? `，热度 +${heatChange}` : ''}`
        : `已取消点赞${heatChange < 0 ? `，热度 ${heatChange}` : ''}`,
      type: diary.isLiked ? 'success' : 'info',
      duration: 1500
    });
  } catch (error) {
    console.error('点赞操作失败:', error);
    // 恢复原状态
    diary.isLiked = !diary.isLiked;
    const change = diary.isLiked ? 1 : -1;
    if (diary.likes !== undefined) diary.likes = Number(diary.likes) + change;
    if (diary.likes_count !== undefined) diary.likes_count = Number(diary.likes_count) + change;
    updateHeat(diary);
    ElMessage.error('操作失败，请稍后重试');
  }
}

// 处理收藏
const handleFavorite = async (diary) => {
  try {
    console.log('收藏前状态:', diary.isFavorited, '收藏数:', diary.favorites, diary.favorites_count);

    // 记录原始热度值
    const oldHeat = diary.heat;

    // 获取当前用户ID
    const userId = localStorage.getItem('userId');
    if (!userId) {
      ElMessage.warning('请先登录后再收藏');
      return;
    }

    // 确保用户ID是数字类型
    const userIdNum = parseInt(userId, 10);
    if (!userIdNum || isNaN(userIdNum)) {
      console.error('无效的用户ID:', userId);
      ElMessage.warning('用户信息不完整，请重新登录');
      return;
    }

    // 确保文章ID是数字类型
    const articleId = parseInt(diary.id, 10);
    if (!articleId || isNaN(articleId)) {
      console.error('无效的文章ID:', diary.id);
      ElMessage.error('无法获取文章信息');
      return;
    }

    // 先检查是否已经收藏
    const checkResponse = await axios.post('http://localhost:5000/api/favorites/check', {
      user_id: userIdNum,
      article_id: articleId
    });

    console.log('检查收藏状态响应:', checkResponse);

    const isFavorited = checkResponse.data && checkResponse.data.code === 0 && checkResponse.data.data.is_favorite;

    // 更新前端状态
    diary.isFavorited = !isFavorited;

    // 更新所有收藏计数字段
    const change = diary.isFavorited ? 1 : -1;
    if (diary.favorites !== undefined) diary.favorites = Number(diary.favorites) + change;
    if (diary.favorites_count !== undefined) diary.favorites_count = Number(diary.favorites_count) + change;

    console.log('收藏后状态:', diary.isFavorited, '收藏数:', diary.favorites, diary.favorites_count);

    // 更新热度
    updateHeat(diary);

    // 计算热度变化
    const heatChange = diary.heat - oldHeat;

    try {
      let response;

      if (diary.isFavorited) {
        // 添加收藏
        response = await axios.post('http://localhost:5000/api/favorites/add', {
          user_id: userIdNum,
          article_id: articleId
        });

        console.log('收藏API响应:', response);

        if (response.data && response.data.code === 0) {
          // 获取最新的收藏数
          const favoriteCountResponse = await axios.get(`http://localhost:5000/api/favorites/count/${diary.id}`);
          if (favoriteCountResponse.data && favoriteCountResponse.data.code === 0) {
            const newFavoriteCount = Number(favoriteCountResponse.data.data.count);
            diary.favorites = newFavoriteCount;
            diary.favorites_count = newFavoriteCount;
            console.log('更新后的收藏数:', newFavoriteCount);

            // 强制更新UI
            diaryList.value = [...diaryList.value];
          }

          // 显示提示
          ElMessage({
            message: `收藏成功${heatChange > 0 ? `，热度 +${heatChange}` : ''}`,
            type: 'success',
            duration: 1500
          });
        } else {
          // API调用失败，回滚UI状态
          diary.isFavorited = !diary.isFavorited;
          if (diary.favorites !== undefined) diary.favorites = Number(diary.favorites) - change;
          if (diary.favorites_count !== undefined) diary.favorites_count = Number(diary.favorites_count) - change;
          updateHeat(diary);
          ElMessage.error(response.data?.message || '收藏失败');
        }
      } else {
        // 取消收藏
        response = await axios.post('http://localhost:5000/api/favorites/remove', {
          user_id: userIdNum,
          article_id: articleId
        });

        console.log('取消收藏API响应:', response);

        if (response.data && response.data.code === 0) {
          // 获取最新的收藏数
          const favoriteCountResponse = await axios.get(`http://localhost:5000/api/favorites/count/${diary.id}`);
          if (favoriteCountResponse.data && favoriteCountResponse.data.code === 0) {
            const newFavoriteCount = Number(favoriteCountResponse.data.data.count);
            diary.favorites = newFavoriteCount;
            diary.favorites_count = newFavoriteCount;
            console.log('更新后的收藏数:', newFavoriteCount);

            // 强制更新UI
            diaryList.value = [...diaryList.value];
          }

          // 显示提示
          ElMessage({
            message: `已取消收藏${heatChange < 0 ? `，热度 ${heatChange}` : ''}`,
            type: 'info',
            duration: 1500
          });
        } else {
          // API调用失败，回滚UI状态
          diary.isFavorited = !diary.isFavorited;
          if (diary.favorites !== undefined) diary.favorites = Number(diary.favorites) - change;
          if (diary.favorites_count !== undefined) diary.favorites_count = Number(diary.favorites_count) - change;
          updateHeat(diary);
          ElMessage.error(response.data?.message || '取消收藏失败');
        }
      }
    } catch (apiError) {
      console.error('收藏API调用失败:', apiError);
      // API调用失败，回滚UI状态
      diary.isFavorited = !diary.isFavorited;
      if (diary.favorites !== undefined) diary.favorites = Number(diary.favorites) - change;
      if (diary.favorites_count !== undefined) diary.favorites_count = Number(diary.favorites_count) - change;
      updateHeat(diary);
      ElMessage.error('操作失败，请稍后重试');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
}

// 评分功能已移至详情页面

// 获取文章统计数据
const fetchArticleStats = () => {
  console.log('获取文章统计数据');

  // 遍历文章列表，获取每篇文章的点赞、收藏和评论数量
  diaryList.value.forEach(diary => {
    // 获取点赞数
    axios.get(`http://localhost:5000/api/article_like/count/${diary.id}`)
      .then(response => {
        if (response.data && response.data.code === 0) {
          diary.likes = Number(response.data.data.like_count);
          diary.likes_count = Number(response.data.data.like_count);
          console.log(`文章ID: ${diary.id} 的点赞数: ${diary.likes}`);
          // 更新热度
          updateHeat(diary);
        }
      })
      .catch(error => {
        console.error(`获取文章ID: ${diary.id} 的点赞数失败:`, error);
      });

    // 获取收藏数
    axios.get(`http://localhost:5000/api/article_favorite/count/${diary.id}`)
      .then(response => {
        if (response.data && response.data.code === 0) {
          diary.favorites = Number(response.data.data.count);
          diary.favorites_count = Number(response.data.data.count);
          console.log(`文章ID: ${diary.id} 的收藏数: ${diary.favorites}`);
          // 更新热度
          updateHeat(diary);
        }
      })
      .catch(error => {
        console.error(`获取文章ID: ${diary.id} 的收藏数失败:`, error);
        // 尝试备用API
        axios.get(`http://localhost:5000/api/favorites/count/${diary.id}`)
          .then(backupResponse => {
            if (backupResponse.data && backupResponse.data.code === 0) {
              diary.favorites = Number(backupResponse.data.data.count);
              diary.favorites_count = Number(backupResponse.data.data.count);
              console.log(`文章ID: ${diary.id} 的收藏数(备用API): ${diary.favorites}`);
              // 更新热度
              updateHeat(diary);
            }
          })
          .catch(backupError => {
            console.error(`备用API获取文章ID: ${diary.id} 的收藏数失败:`, backupError);
          });
      });

    // 获取评论数
    axios.get(`http://localhost:5000/api/article_comment/count/${diary.id}`)
      .then(response => {
        if (response.data && response.data.code === 0) {
          diary.commentsCount = Number(response.data.data.count);
          diary.comments_count = Number(response.data.data.count);
          console.log(`文章ID: ${diary.id} 的评论数: ${diary.commentsCount}`);
          // 更新热度
          updateHeat(diary);
        }
      })
      .catch(error => {
        console.error(`获取文章ID: ${diary.id} 的评论数失败:`, error);
        // 尝试备用API
        axios.get(`http://localhost:5000/api/comments/count/${diary.id}`)
          .then(backupResponse => {
            if (backupResponse.data && backupResponse.data.code === 0) {
              diary.commentsCount = Number(backupResponse.data.data.count);
              diary.comments_count = Number(backupResponse.data.data.count);
              console.log(`文章ID: ${diary.id} 的评论数(备用API): ${diary.commentsCount}`);
              // 更新热度
              updateHeat(diary);
            }
          })
          .catch(backupError => {
            console.error(`备用API获取文章ID: ${diary.id} 的评论数失败:`, backupError);
          });
      });
  });

  // 强制更新UI
  diaryList.value = [...diaryList.value];
};

// 更新热度
const updateHeat = (diary) => {
  // 保存原始热度值，用于调试和比较
  const oldHeat = diary.heat || 0;

  // 获取各项数据，支持多种字段名
  const likes = Number(diary.likes || diary.likes_count || 0);
  const favorites = Number(diary.favorites || diary.favorites_count || 0);
  const comments = Number(diary.commentsCount || diary.comments_count || 0);
  const views = Number(diary.views || diary.popularity || 0);

  console.log(`热度计算前的数据 - 文章ID: ${diary.id}, 点赞: ${likes}, 收藏: ${favorites}, 评论: ${comments}, 浏览: ${views}`);

  // 热度计算公式：点赞数 * 0.4 + 收藏数 * 0.3 + 评论数 * 0.2 + 浏览量 * 0.01
  const baseScore = likes * 0.4 + favorites * 0.3 + comments * 0.2 + views * 0.01;

  // 如果用户评分，增加额外热度
  const ratingBonus = diary.userRating > 0 ? diary.userRating * 5 : 0;

  // 计算最终热度，最高100
  const newHeat = Math.min(Math.round(baseScore + ratingBonus), 100);

  // 防止热度值异常波动
  if (newHeat < oldHeat) {
    // 如果是取消点赞/收藏/评分操作，允许热度下降，但最多下降一定值
    const isCancel =
      (diary.isLiked === false) ||
      (diary.isFavorited === false) ||
      (diary.userRating === 0);

    if (isCancel) {
      // 取消点赞：降低1-3点热度
      if (diary.isLiked === false) {
        diary.heat = Math.max(oldHeat - 3, newHeat);
        console.log('取消点赞，热度降低');
        return;
      }

      // 取消收藏：降低1-2点热度
      if (diary.isFavorited === false) {
        diary.heat = Math.max(oldHeat - 2, newHeat);
        console.log('取消收藏，热度降低');
        return;
      }

      // 取消评分：降低评分值 * 2的热度
      if (diary.userRating === 0) {
        // 假设之前的评分是3星（因为我们无法知道之前的具体评分）
        const previousRating = 3;
        diary.heat = Math.max(oldHeat - (previousRating * 2), newHeat);
        console.log('取消评分，热度降低');
        return;
      }
    } else if (oldHeat - newHeat > 10) {
      // 如果不是取消操作，但热度下降超过10点，限制下降幅度
      diary.heat = oldHeat - 10;
      console.log('热度下降幅度过大，已限制');
      return;
    }
  }

  // 更新热度值
  diary.heat = newHeat;

  // 调试信息
  console.log(`文章ID: ${diary.id} 的热度计算 - 点赞: ${likes}, 收藏: ${favorites}, 评论: ${comments}, 浏览: ${views}, 热度: ${newHeat}%`);
  console.log(`热度更新: ${oldHeat} -> ${diary.heat}, 基础分: ${baseScore}, 评分加成: ${ratingBonus}`);
}



// 地点建议相关函数
const queryLocationSuggestions = async (queryString, callback) => {
  console.log('queryLocationSuggestions 被调用，查询字符串:', queryString);

  if (!queryString || queryString.trim().length < 1) {
    console.log('查询字符串为空，返回空数组');
    callback([]);
    return;
  }

  try {
    console.log('准备调用API，URL:', `http://localhost:5000/api/locations/query`);
    console.log('请求参数:', { name: queryString, sortOrder: '0' });

    // 调用后端API获取地点建议
    const response = await axios.get(`http://localhost:5000/api/locations/query`, {
      params: {
        name: queryString,
        sortOrder: '0' // 按人气排序
      }
    });

    console.log('API响应:', response);
    console.log('响应数据:', response.data);

    if (response.data && Array.isArray(response.data)) {
      // 转换数据格式，添加value字段供el-autocomplete使用
      const suggestions = response.data.slice(0, 10).map(location => ({
        ...location,
        value: location.name, // el-autocomplete需要的value字段
        name: location.name,
        type: location.type,
        location_id: location.location_id
      }));

      console.log('转换后的建议数据:', suggestions);
      callback(suggestions);
    } else {
      console.log('响应数据格式不正确或为空');
      callback([]);
    }
  } catch (error) {
    console.error('获取地点建议失败:', error);
    console.error('错误详情:', error.response);
    callback([]);
  }
};

// 处理地点选择
const handleLocationSelect = (item) => {
  console.log('选择了地点:', item);
  newDiary.location = item.name;
};

// 搜索建议相关函数（使用新的全文检索功能）
const querySearchAsync = async (queryString, callback) => {
  if (!queryString || queryString.trim().length < 1) {
    callback([]);
    return;
  }

  try {
    if (searchMode.value === 'fulltext') {
      // 全文检索模式：获取包含关键词的句子
      const response = await articleApi.getSearchSuggestions(queryString, 5);

      if (response.data && response.data.code === 0) {
        const suggestions = response.data.data.suggestions || [];
        // 转换为autocomplete需要的格式
        const formattedSuggestions = suggestions.map(item => ({
          title: item.title,
          highlighted_sentence: item.highlighted_sentence,
          username: item.username || '未知作者',
          created_at: item.created_at,
          article_id: item.article_id,
          value: item.sentence // autocomplete需要的value字段
        }));
        callback(formattedSuggestions);
      } else {
        callback([]);
      }
    } else {
      // 标题搜索模式：使用现有的模糊搜索
      try {
        const response = await articleApi.fuzzySearchArticles(queryString, 5);

        if (response.data && response.data.code === 0) {
          const articles = response.data.data || [];
          const suggestions = articles.map(article => ({
            title: article.title,
            username: article.username || '未知作者',
            created_at: article.created_at,
            article_id: article.article_id,
            value: article.title
          }));
          callback(suggestions);
        } else {
          callback([]);
        }
      } catch (error) {
        console.warn('模糊搜索失败，使用空建议:', error);
        callback([]);
      }
    }
  } catch (error) {
    console.error('获取搜索建议失败:', error);
    callback([]);
  }
};

// 处理搜索输入
const handleSearchInput = (value) => {
  // 防抖处理，避免频繁搜索
  if (searchInputTimer) {
    clearTimeout(searchInputTimer);
  }

  searchInputTimer = setTimeout(async () => {
    if (!value || value.trim().length === 0) {
      // 如果搜索框为空，重新加载所有文章
      await fetchArticles();
    }
  }, 500);
};

// 搜索输入防抖定时器
let searchInputTimer = null;

// 处理建议选择
const handleSelect = (item) => {
  console.log('选择了搜索建议:', item);
  if (item.article_id) {
    // 如果选择的是具体的文章建议，直接跳转到文章详情
    router.push(`/diary/detail/${item.article_id}?highlight=${encodeURIComponent(searchQuery.value)}`);
  } else {
    // 否则执行搜索
    searchQuery.value = item.value || item.title;
    handleSearch();
  }
};

// 格式化建议日期
const formatSuggestionDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  }
};

// 搜索处理
const handleSearch = async () => {
  try {
    await fetchArticles();
  } catch (error) {
    console.error('搜索文章失败:', error);
    ElMessage.error('搜索文章失败，请稍后重试');
  }
}

// 清除搜索
const clearSearch = async () => {
  searchQuery.value = '';
  try {
    await fetchArticles();
  } catch (error) {
    console.error('清除搜索后获取文章失败:', error);
    ElMessage.error('获取文章列表失败，请稍后重试');
  }
}



// 获取"为您推荐"的文章
const fetchForYouRecommendations = async () => {
  try {
    console.log('获取为您推荐的文章');

    // 检查用户是否登录
    const token = localStorage.getItem('token') || localStorage.getItem('authToken');
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const userId = userInfo.user_id;

    let response;

    if (!token || !userId) {
      // 游客模式：获取热门文章
      console.log('游客模式，获取热门文章');
      response = await articleApi.getForYouRecommendations(null, true, pageSize.value);
    } else {
      // 登录模式：使用协同过滤推荐
      console.log('登录模式，使用协同过滤推荐，用户ID:', userId);
      response = await articleApi.getForYouRecommendations(userId, false, pageSize.value);
    }

    console.log('为您推荐响应:', response);

    if (response.data && response.data.code === 0) {
      const articles = response.data.data.articles || [];
      console.log('推荐文章数量:', articles.length);

      if (articles.length === 0) {
        ElMessage.info('暂无推荐内容，为您展示热门文章');
        // 如果没有推荐结果，切换到热门排序
        sortOption.value = 'heat';
        await fetchArticles();
        return;
      }

      // 处理推荐文章数据
      processArticles({ articles });

      // 显示推荐信息
      const recommendationType = response.data.data.articles[0]?.recommendation_type || 'unknown';
      const elapsedTime = response.data.data.elapsed_time || '0.00 秒';

      if (recommendationType === 'collaborative') {
        ElMessage.success(`为您推荐 ${articles.length} 篇文章（协同过滤算法，耗时 ${elapsedTime}）`);
      } else {
        ElMessage.success(`为您推荐 ${articles.length} 篇热门文章（耗时 ${elapsedTime}）`);
      }

      // 获取文章统计数据
      fetchArticleStats();
    } else {
      console.error('获取推荐失败:', response.data?.message);
      ElMessage.error(response.data?.message || '获取推荐失败');

      // 失败时切换到热门排序
      sortOption.value = 'heat';
      await fetchArticles();
    }
  } catch (error) {
    console.error('获取为您推荐失败:', error);
    ElMessage.error('获取推荐失败，为您展示热门文章');

    // 出错时切换到热门排序
    sortOption.value = 'heat';
    await fetchArticles();
  } finally {
    loading.value = false;
  }
};

// 排序处理
const handleSort = async () => {
  try {
    await fetchArticles();
  } catch (error) {
    console.error('排序文章失败:', error);
    ElMessage.error('排序文章失败，请稍后重试');
  }
}

// 页面加载时获取文章列表
onMounted(async () => {
  try {
    await fetchArticles();

    // 获取地点列表
    try {
      const res = await locationApi.getAllLocations();
      if (res.data.code === 0 && res.data.data) {
        // 更新地点选项
        locationOptions.value = res.data.data.map(location => ({
          value: location.location_id,
          label: location.name
        }));
      }
    } catch (err) {
      console.error('获取地点列表失败:', err);
    }
  } catch (error) {
    console.error('页面加载时获取文章列表失败:', error);
    ElMessage.error('获取文章列表失败，请稍后重试');
  }
});

// 计算属性：根据搜索和排序条件过滤和排序日志列表
const displayedDiaryList = computed(() => {
  // 1. 先根据搜索条件过滤
  let result = diaryList.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(diary =>
      diary.title.toLowerCase().includes(query) ||
      diary.content.toLowerCase().includes(query)
    )
  }

  // 2. 如果是"为您推荐"模式，直接返回结果，不进行额外排序
  if (sortOption.value === 'for-you') {
    return result;
  }

  // 3. 其他模式根据排序条件排序
  return result.slice().sort((a, b) => {
    if (sortOption.value === 'latest') {
      // 按发布时间排序（最新的在前面）
      return b.createTime - a.createTime
    } else if (sortOption.value === 'heat') {
      // 按热度排序（热度高的在前面）
      return b.heat - a.heat
    } else if (sortOption.value === 'rating') {
      // 按评分排序（评分高的在前面）
      return b.avgRating - a.avgRating
    }
    return 0
  })
})

// 查看详情
const handleViewClick = async (diary) => {
  try {
    console.log('点击阅读全文，日记ID:', diary.id);

    // 不在前端预先增加浏览量，让后端统一处理
    // 这样避免了重复计算和显示不一致的问题

    // 跳转到详情页，添加来源参数，告诉详情页这是从社区页面跳转的
    const detailPath = `/diary/detail/${diary.id}?from=diary-community&increment_popularity=true`;
    console.log('跳转到:', detailPath);
    router.push(detailPath);
  } catch (error) {
    console.error('处理阅读全文失败:', error);

    // 尝试直接跳转
    try {
      router.push(`/diary/detail/${diary.id}?from=diary-community&increment_popularity=true`);
    } catch (routerError) {
      console.error('路由跳转失败:', routerError);
      ElMessage.error('无法跳转到详情页，请稍后再试');
    }
  }
}
</script>

<style scoped>
.diary-community {
  padding: 30px;
  background-color: #f9fafc;
  min-height: calc(100vh - 60px);
}

.community-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.community-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 3px;
}

.community-title {
  font-size: 2.4rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 700;
  position: relative;
}

.community-actions {
  display: flex;
  gap: 1rem;
}

.publish-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 20px;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.publish-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.4);
  background: linear-gradient(135deg, #66b1ff, #85ce61);
}

.search-filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0 2rem;
  flex-wrap: wrap;
  gap: 1.5rem;
  background-color: white;
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-box {
  width: 350px;
}

.search-box :deep(.el-input__wrapper) {
  border-radius: 20px;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2) inset;
  transition: all 0.3s ease;
}

.search-box :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.4) inset;
}

.search-box :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset !important;
}

/* 搜索建议样式 */
:deep(.search-suggestions-popper) {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

:deep(.search-suggestions-popper .el-autocomplete-suggestion__list) {
  padding: 8px 0;
  max-height: 300px;
  overflow-y: auto;
}

:deep(.search-suggestions-popper .el-autocomplete-suggestion__item) {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  transition: all 0.2s ease;
  cursor: pointer;
}

:deep(.search-suggestions-popper .el-autocomplete-suggestion__item:last-child) {
  border-bottom: none;
}

:deep(.search-suggestions-popper .el-autocomplete-suggestion__item:hover) {
  background-color: #f5f7fa;
}

:deep(.search-suggestions-popper .el-autocomplete-suggestion__item.highlighted) {
  background-color: #ecf5ff;
}

.suggestion-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.suggestion-author {
  font-weight: 500;
  color: #606266;
}

.suggestion-date {
  color: #c0c4cc;
}

.suggestion-content {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin: 2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-content :deep(mark) {
  background-color: #fff2cc;
  color: #e6a23c;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

.search-mode-toggle {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}

.filter-label {
  color: #606266;
  font-size: 0.9rem;
  font-weight: 500;
}

.diary-list {
  margin-bottom: 2rem;
}

.diary-card {
  margin-bottom: 1.5rem;
  background-color: #fff !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  overflow: hidden;
  padding-bottom: 0 !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.diary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1) !important;
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 12px 16px 0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: 13px;
  margin: 0;
  font-weight: 500;
  color: #333;
}

.post-date {
  font-size: 12px;
  color: #999;
  display: block;
  margin-top: 2px;
}

.diary-content {
  padding: 0 16px;
  margin-bottom: 1rem;
}

.diary-title {
  font-size: 22px;
  font-weight: 700;
  margin: 0 0 12px;
  color: #2c3e50;
  line-height: 1.4;
  position: relative;
  padding-bottom: 8px;
}

.diary-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 3px;
}

.diary-excerpt {
  color: #5a6a7f;
  font-size: 15px;
  line-height: 1.7;
  margin: 12px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  letter-spacing: 0.3px;
}

.diary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 12px 0 15px;
}

.diary-tag {
  font-size: 12px !important;
  padding: 2px 10px !important;
  height: 24px !important;
  line-height: 20px !important;
  border-radius: 12px !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #409EFF !important;
  border: none !important;
  transition: all 0.3s ease;
}

.diary-tag:hover {
  background-color: rgba(64, 158, 255, 0.2) !important;
  transform: translateY(-2px);
}

.diary-media {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin: 15px 0;
  overflow: hidden;
  border-radius: 8px;
}

.diary-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.diary-image:hover {
  transform: scale(1.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.more-images {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 180px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.more-images:hover {
  background-color: rgba(0, 0, 0, 0.7);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.diary-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
  background-color: #fafafa;
}

.diary-stats {
  display: flex;
  align-items: center;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.stat-item:hover {
  color: #409EFF;
}

.active-stat {
  color: #409EFF;
}

.active-icon {
  color: #409EFF;
}

.liked-icon {
  color: #F56C6C !important;
}

.favorited-icon {
  color: #E6A23C !important;
}

.stat-count {
  font-weight: 500;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.heat-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
}

.heat-progress {
  width: 60px;
  margin-left: 5px;
}

.diary-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-left: auto;
}

.rating-stars {
  display: flex;
  align-items: center;
  gap: 10px;
}

.read-more-btn {
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 4px;
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
  margin-left: auto;
  transition: all 0.3s ease;
}

.read-more-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.rating-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.diary-rating {
  margin: 12px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  background-color: rgba(255, 153, 0, 0.05);
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid #ff9900;
}

.read-more-btn {
  margin-left: auto;
}

.empty-state {
  text-align: center;
  padding: 4rem 0;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 2rem 0;
}

.empty-state :deep(.el-empty__image) {
  width: 180px;
  height: 180px;
}

.empty-state :deep(.el-empty__description) {
  margin-top: 20px;
  font-size: 16px;
  color: #606266;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.pagination-container :deep(.el-pagination) {
  padding: 15px 20px;
  background-color: white;
  border-radius: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.pagination-container :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409EFF;
  color: white;
  font-weight: bold;
}

/* 对话框样式 */
.preview-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.diary-uploader {
  width: 100%;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 4px;
}

.delete-icon {
  color: #fff;
  cursor: pointer;
  font-size: 16px;
}

.delete-icon:hover {
  color: #f56c6c;
}

.upload-tip {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.privacy-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

/* 显示地点和标签 */
.diary-meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px 0;
}

.diary-location {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 0.9rem;
}

.diary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.diary-tag {
  padding: 2px 8px;
  background-color: #f0f2f5;
  color: #606266;
  border-radius: 12px;
  font-size: 0.8rem;
}

/* 自定义标签选择器样式 */
.custom-tag-selector {
  width: 100%;
}

.tag-input-container {
  display: flex;
  margin-bottom: 10px;
  gap: 10px;
}

.tag-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.tag-section-title {
  font-size: 14px;
  color: #606266;
  margin: 10px 0 5px;
}

.preset-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.preset-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.preset-tag.selected {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.selected-tags {
  margin-top: 15px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  margin-right: 0;
}

/* 视频上传样式 */
.video-upload-container {
  width: 100%;
}

.video-upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 120px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #fafafa;
}

.video-upload-button:hover {
  border-color: #409eff;
}

.video-upload-button i {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.video-upload-button span {
  font-size: 14px;
  color: #606266;
}

.video-preview {
  margin-top: 10px;
}

.preview-video {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
}

.video-container {
  margin: 15px 0;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.diary-video {
  width: 100%;
  height: 240px;
  border-radius: 8px;
  background-color: #000;
  object-fit: cover;
  transition: all 0.3s ease;
}

.video-container:hover .diary-video {
  transform: scale(1.01);
}

.video-error-message {
  padding: 20px;
  background-color: #f8f9fa;
  color: #dc3545;
  text-align: center;
  border-radius: 4px;
  margin-top: 10px;
}

.video-actions {
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
}

/* 地点建议样式 */
.location-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.location-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.location-type {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 12px;
}
</style>