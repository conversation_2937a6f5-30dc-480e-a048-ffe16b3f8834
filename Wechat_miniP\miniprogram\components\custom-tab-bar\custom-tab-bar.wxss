.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tab-bar-icon {
  font-family: "Material Icons";
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #999999;
}

.tab-bar-icon.active {
  color: #409EFF;
}

.tab-bar-text {
  font-size: 12px;
  color: #999999;
}

.tab-bar-text.active {
  color: #409EFF;
}
