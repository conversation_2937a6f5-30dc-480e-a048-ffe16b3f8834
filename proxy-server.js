// 本地代理服务器，用于解决微信小程序域名校验问题
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 启用CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept']
}));

// 代理配置
const proxyOptions = {
  target: 'http://**************:5000', // 您的实际IP地址
  changeOrigin: true,
  pathRewrite: {
    '^/api': '/api' // 保持API路径不变
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err);
    res.status(500).json({ error: '代理服务器错误' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`代理请求: ${req.method} ${req.url} -> ${proxyOptions.target}${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`代理响应: ${proxyRes.statusCode} ${req.url}`);
  }
};

// 设置代理中间件
app.use('/api', createProxyMiddleware(proxyOptions));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: '代理服务器运行正常' });
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`代理服务器启动成功！`);
  console.log(`本地访问地址: http://localhost:${PORT}`);
  console.log(`网络访问地址: http://**************:${PORT}`); // 您的实际IP
  console.log(`代理目标: ${proxyOptions.target}`);
  console.log(`\n请在小程序中使用以下API地址:`);
  console.log(`http://**************:${PORT}/api`);
});

// 错误处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});
