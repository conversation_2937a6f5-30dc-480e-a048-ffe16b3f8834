#!/usr/bin/env python3
"""
测试配置加载
"""
import os
import sys

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_env_loading():
    """测试环境变量加载"""
    print("🧪 测试环境变量加载...")
    
    # 直接测试环境变量
    print(f"DOUBAO_ACCESS_KEY_ID: {os.getenv('DOUBAO_ACCESS_KEY_ID', 'NOT_FOUND')}")
    print(f"DOUBAO_SECRET_ACCESS_KEY: {os.getenv('DOUBAO_SECRET_ACCESS_KEY', 'NOT_FOUND')}")
    
    # 测试.env文件是否存在
    env_file = os.path.join('backend', '.env')
    print(f"📁 .env文件路径: {env_file}")
    print(f"📁 .env文件存在: {os.path.exists(env_file)}")
    
    if os.path.exists(env_file):
        print("📋 .env文件内容:")
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                if 'DOUBAO' in line:
                    print(f"  {i}: {line.strip()}")

def test_config_loading():
    """测试配置类加载"""
    print("\n🧪 测试配置类加载...")
    
    try:
        from backend.config import Config
        print("✅ 配置类导入成功")
        
        print(f"DOUBAO_ACCESS_KEY_ID: {getattr(Config, 'DOUBAO_ACCESS_KEY_ID', 'NOT_FOUND')}")
        print(f"DOUBAO_SECRET_ACCESS_KEY: {getattr(Config, 'DOUBAO_SECRET_ACCESS_KEY', 'NOT_FOUND')}")
        print(f"DOUBAO_BASE_URL: {getattr(Config, 'DOUBAO_BASE_URL', 'NOT_FOUND')}")
        print(f"DOUBAO_DEMO_MODE: {getattr(Config, 'DOUBAO_DEMO_MODE', 'NOT_FOUND')}")
        
    except Exception as e:
        print(f"❌ 配置类导入失败: {e}")

def test_flask_app_config():
    """测试Flask应用配置"""
    print("\n🧪 测试Flask应用配置...")
    
    try:
        from backend.app import create_app
        app = create_app()
        
        with app.app_context():
            print("✅ Flask应用创建成功")
            print(f"DOUBAO_ACCESS_KEY_ID: {app.config.get('DOUBAO_ACCESS_KEY_ID', 'NOT_FOUND')}")
            print(f"DOUBAO_SECRET_ACCESS_KEY: {app.config.get('DOUBAO_SECRET_ACCESS_KEY', 'NOT_FOUND')}")
            print(f"DOUBAO_BASE_URL: {app.config.get('DOUBAO_BASE_URL', 'NOT_FOUND')}")
            print(f"DOUBAO_DEMO_MODE: {app.config.get('DOUBAO_DEMO_MODE', 'NOT_FOUND')}")
            
    except Exception as e:
        print(f"❌ Flask应用配置测试失败: {e}")

def test_aigc_service():
    """测试AIGC服务配置"""
    print("\n🧪 测试AIGC服务配置...")
    
    try:
        from backend.app import create_app
        app = create_app()
        
        with app.app_context():
            from backend.services.aigc_animation_service import AIGCAnimationService
            service = AIGCAnimationService()
            
            print("✅ AIGC服务创建成功")
            print(f"access_key_id: {getattr(service, 'access_key_id', 'NOT_FOUND')}")
            print(f"secret_access_key: {getattr(service, 'secret_access_key', 'NOT_FOUND')}")
            print(f"demo_mode: {getattr(service, 'demo_mode', 'NOT_FOUND')}")
            
    except Exception as e:
        print(f"❌ AIGC服务配置测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 配置测试工具")
    print("=" * 50)
    
    test_env_loading()
    test_config_loading()
    test_flask_app_config()
    test_aigc_service()
