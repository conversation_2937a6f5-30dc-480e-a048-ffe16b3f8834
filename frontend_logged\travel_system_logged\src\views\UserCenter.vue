<template>
  <div class="page-container">
    <div class="user-layout">
      <!-- 左侧：个人信息卡片 -->
      <el-card class="user-card">
      <div class="user-info-centered" style="display: flex !important; flex-direction: column !important; align-items: flex-start !important; text-align: left !important; justify-content: space-between !important;">
        <div class="user-profile" style="display: flex !important; flex-direction: column !important; align-items: flex-start !important; width: 100% !important;">
          <div class="avatar-wrapper" style="position: relative !important; display: inline-block !important; margin: 0 0 10px 0 !important;">
            <el-avatar v-if="avatarUrl"
              :size="100"
              :src="avatarUrl"
              @click="handleAvatarClick"
              style="cursor: pointer"
            />
          </div>
          <div class="user-basic" style="text-align: left !important; width: 100% !important;">
            <h2 @click="handleUsernameClick" style="cursor: pointer; text-align: left !important;">{{ userInfo.username }}</h2>
            <p @click="handleEmailClick" style="cursor: pointer; text-align: left !important;">{{ userInfo.email }}</p>
          </div>
        </div>

        <!-- 按钮：编辑资料和退出登录 -->
        <div style="margin-top: 20px; padding-left: 20px;">
          <div style="margin-bottom: 10px;">
            <el-button type="primary" style="width: 120px;  " @click="handleEditProfile"  class="xhs-button primary">编辑资料</el-button>
          </div>
          <div>
            <el-button type="danger" style="width: 120px;" @click="handleLogout" class="xhs-button danger">退出登录</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 右侧：内容卡片 -->
    <div class="diary-container">
      <div class="background-layer"></div>
      <el-card class="content-card">
      <el-tabs v-model="activeTab" class="user-tabs">
        <!-- 我的日记标签页 -->
        <el-tab-pane name="diaries">
          <template #label>
            <div class="tab-label">
              <el-icon><Document /></el-icon>
              <span>我的日记</span>
            </div>
          </template>
          <!-- 日记内容区域 -->
          <div class="diary-list-or-empty" v-if="userInfo.id">
            <!-- 加载状态 -->
            <!-- <div v-if="loadingDiaries" class="loading-container">
              <el-skeleton :rows="3" animated />
              <el-skeleton :rows="3" animated />
            </div> -->

            <!-- 有数据时显示列表 -->
             <template v-if="diaryList && diaryList.length">
            <!-- <template v-else-if="diaryList && diaryList.length"> -->
              <el-row :gutter="20">
                <el-col :span="24" v-for="diary in diaryList" :key="diary.id">
                  <el-card class="diary-card">
                    <div class="diary-header">
                      <div class="author-info">
                        <img :src="avatarUrl" class="author-avatar">
                        <div>
                          <h3 class="author-name">{{ activeTab === 'diaries' ? userInfo.username : diary.author_name || '未知作者' }}</h3>
                          <span class="post-date">{{ formatDate(diary.createTime) }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="diary-content">
                      <h2 class="diary-title">{{ diary.title }}</h2>

                      <!-- 标签区域 -->
                      <div class="diary-tags">
                        <!-- 地点标签 -->
                        <el-tag
                          v-if="diary.location_name"
                          size="small"
                          type="warning"
                          effect="plain"
                          class="diary-tag location-tag"
                        >
                          <el-icon><Location /></el-icon>
                          {{ diary.location_name }}
                        </el-tag>
                        <!-- 数据库标签 -->
                        <el-tag
                          v-for="(tag, index) in (diary.tags && diary.tags.length > 0 ? diary.tags : getDefaultTags(diary.title))"
                          :key="index"
                          size="small"
                          type="info"
                          effect="plain"
                          class="diary-tag"
                        >
                          {{ tag }}
                        </el-tag>
                      </div>

                      <p class="diary-excerpt" v-if="diary.content">{{ stripHtml(diary.content).substring(0, 150) }}{{ stripHtml(diary.content).length > 150 ? '...' : '' }}</p>
                      <p class="diary-excerpt" v-else>暂无内容</p>

                      <!-- 图片展示区域 -->
                      <div class="diary-media" v-if="diary.images && diary.images.length">
                        <img
                          v-for="(img, index) in diary.images.slice(0, 2)"
                          :key="index"
                          :src="getFullImageUrl(img)"
                          class="diary-image"
                          @error="handleImageError"
                        >
                        <div v-if="diary.images.length > 2" class="more-images">
                          <span>+{{ diary.images.length - 2 }}</span>
                        </div>
                      </div>

                      <!-- 视频展示区域 -->
                      <div class="diary-media video-container" v-if="diary.video">
                        <video
                          controls
                          class="diary-video"
                          :src="diary.video"
                          @error="handleVideoError"
                        ></video>
                      </div>
                    </div>

                    <div class="diary-footer">
                      <div class="diary-stats">
                        <div class="stats-row">
                          <div class="stat-item">
                            <el-icon class="up-icon"><CaretTop /></el-icon>
                            <span class="stat-count">{{ diary.likes_count || 0 }}</span>
                            <span class="stat-label">点赞</span>
                          </div>
                          <div class="stat-item">
                            <el-icon><Star /></el-icon>
                            <span class="stat-count">{{ diary.favorites_count || diary.favorites || 0 }}</span>
                            <span class="stat-label">收藏</span>
                          </div>
                          <div class="stat-item">
                            <el-icon><Message /></el-icon>
                            <span class="stat-count">{{ diary.comments_count || 0 }}</span>
                            <span class="stat-label">评论</span>
                          </div>
                          <div class="stat-item">
                            <el-icon><View /></el-icon>
                            <span class="stat-count">{{ diary.popularity || 0 }}</span>
                            <span class="stat-label">浏览</span>
                          </div>
                          <div class="heat-indicator" v-if="diary.heat">
                            <span>热度</span>
                            <el-progress class="heat-progress" :percentage="diary.heat" :stroke-width="8" :show-text="false" status="warning"></el-progress>
                          </div>
                        </div>
                      </div>
                      <div class="rating-display">
                        <el-rate
                          :model-value="diary.avgRating || 0"
                          disabled
                          text-color="#ff9900"
                        ></el-rate>
                        <el-button type="primary" class="read-more-btn" @click="handleViewClick(diary)">
                          阅读全文
                        </el-button>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </template>

            <!-- 无数据时显示空状态 -->
            <template v-else>
              <div class="empty-state">
                <el-empty description="暂无游记">
                </el-empty>
              </div>
            </template>
          </div>
        </el-tab-pane>

        <!-- 我的收藏标签页 -->
        <el-tab-pane name="favorites">
          <template #label>
            <div class="tab-label">
              <el-icon><Star /></el-icon>
              <span>我的收藏</span>
            </div>
          </template>
          <!-- 收藏内容区域 -->
          <div class="favorites-list-or-empty">
            <template v-if="favorites.length > 0">
              <el-row :gutter="20">
                <el-col :span="24" v-for="item in favorites" :key="item.article_id || item.location_id">
                  <el-card class="favorite-card">
                    <div class="favorite-header">
                      <h3 class="favorite-title">{{ item.title }}</h3>
                      <div class="favorite-info">
                        <span class="favorite-author">作者: {{ item.author_name || '未知作者' }}</span>
                        <span class="favorite-date">收藏于: {{ formatDate(item.favorited_at || Date.now()) }}</span>
                      </div>
                    </div>
                    <div class="favorite-content">
                      <p class="favorite-excerpt">{{ item.content ? item.content.substring(0, 150) + '...' : '暂无内容描述' }}</p>
                      <div class="favorite-location" v-if="item.location_name">
                        <el-icon><Location /></el-icon>
                        <span>{{ item.location_name }}</span>
                      </div>
                    </div>
                    <div class="favorite-footer">
                      <el-button type="primary" size="small" @click="viewFavoriteDetail(item)">查看详情</el-button>
                      <el-button type="danger" size="small" @click="removeFavorite(item)">取消收藏</el-button>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </template>
            <template v-else>
              <div class="empty-state">
                <el-empty description="暂无收藏内容">
                  <template #default>
                    <el-button type="primary" @click="goToRecommend">
                      <el-icon><Location /></el-icon> 去发现更多精彩内容
                    </el-button>
                  </template>
                </el-empty>
              </div>
            </template>
          </div>
        </el-tab-pane>

        <!-- 我的评论标签页 -->
        <el-tab-pane name="comments">
          <template #label>
            <div class="tab-label">
              <el-icon><ChatDotRound /></el-icon>
              <span>我的评论</span>
            </div>
          </template>
          <!-- 评论内容区域 -->
          <div class="comments-list-or-empty">
            <template v-if="comments.length > 0">
              <el-timeline>
                <el-timeline-item
                  v-for="comment in comments"
                  :key="comment.id"
                  :timestamp="formatDate(comment.created_at || Date.now())"
                  placement="top"
                >
                  <el-card class="comment-card">
                    <div class="comment-header">
                      <h4>评论于: {{ comment.article_title || comment.location_name || '未知内容' }}</h4>
                    </div>
                    <div class="comment-content">
                      <p>{{ comment.content }}</p>
                    </div>
                    <div class="comment-footer">
                      <el-button type="text" @click="viewCommentDetail(comment)">查看原文</el-button>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </template>
            <template v-else>
              <div class="empty-state">
                <el-empty description="暂无评论记录">
                  <template #default>
                    <el-button type="primary" @click="goToRecommend">
                      <el-icon><Location /></el-icon> 去发现更多精彩内容
                    </el-button>
                  </template>
                </el-empty>
              </div>
            </template>
          </div>
        </el-tab-pane>

        <!-- 我的点赞标签页 -->
        <el-tab-pane name="likes">
          <template #label>
            <div class="tab-label">
              <!-- 使用内联SVG但保持与其他图标一致的样式 -->
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1em" height="1em" style="fill: currentColor; margin-right: 4px; vertical-align: middle;">
                <path d="M1 21h4V9H1v12zm22-11c0-1.1-.9-2-2-2h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 1 7.59 7.59C7.22 7.95 7 8.45 7 9v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-1.91l-.01-.01L23 10z"/>
              </svg>
              <span>我的点赞</span>
            </div>
          </template>
          <!-- 点赞内容区域 -->
          <div class="likes-list-or-empty">
            <template v-if="likes.length > 0">
              <el-row :gutter="20">
                <el-col :span="24" v-for="item in likes" :key="item.id">
                  <el-card class="like-card">
                    <div class="like-header">
                      <h3 class="like-title">{{ item.title }}</h3>
                      <span class="like-date">点赞于: {{ formatDate(item.liked_at || Date.now()) }}</span>
                    </div>
                    <div class="like-content">
                      <p class="like-excerpt">{{ item.content ? item.content.substring(0, 150) + '...' : '暂无内容描述' }}</p>
                    </div>
                    <div class="like-footer">
                      <el-button type="primary" size="small" @click="viewLikeDetail(item)">查看详情</el-button>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </template>
            <template v-else>
              <div class="empty-state">
                <el-empty description="暂无点赞记录">
                  <template #default>
                    <el-button type="primary" @click="goToRecommend">
                      <el-icon><Location /></el-icon> 去发现更多精彩内容
                    </el-button>
                  </template>
                </el-empty>
              </div>
            </template>
          </div>
        </el-tab-pane>
      </el-tabs>




      <!-- 发布旅游日记对话框 -->
      <el-dialog
        v-model="diaryDialogVisible"
        title="发布旅行游记"
        width="800px"
        destroy-on-close
      >
        <el-form
          :model="newDiary"
          label-width="100px"
          :rules="diaryRules"
          ref="diaryFormRef"
          status-icon
        >
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="newDiary.title"
              placeholder="请输入游记标题"
              maxlength="50"
              show-word-limit
            ></el-input>
          </el-form-item>

          <el-form-item label="旅行地点" prop="location">
            <el-autocomplete
              v-model="newDiary.location"
              :fetch-suggestions="queryLocationSuggestions"
              placeholder="请输入旅行地点"
              clearable
              @select="handleLocationSelect"
              :trigger-on-focus="true"
              :highlight-first-item="true"
              :debounce="300"
              style="width: 100%"
            >
              <template #default="{ item }">
                <div class="location-suggestion-item">
                  <div class="location-name">{{ item.name }}</div>
                  <div class="location-type">{{ item.type === 0 ? '学校' : '景点' }}</div>
                </div>
              </template>
            </el-autocomplete>
          </el-form-item>

          <el-form-item label="标签" prop="tags">
            <!-- 自定义标签选择器 -->
            <div class="custom-tag-selector">
              <div class="tag-input-container">
                <input
                  type="text"
                  v-model="tagInput"
                  @keyup.enter="addCustomTag"
                  placeholder="添加我的标签..."
                  class="tag-input"
                />
                <el-button type="primary" size="small" @click="addCustomTag">添加</el-button>
              </div>

              <div class="tag-options">
                <p class="tag-section-title">选择预设标签:</p>
                <div class="preset-tags">
                  <el-tag
                    v-for="item in tagOptions"
                    :key="item.value"
                    class="preset-tag"
                    @click="addPresetTag(item.value)"
                    :class="{ 'selected': newDiary.tags.includes(item.value) }"
                    effect="light"
                  >
                    {{ item.label }}
                  </el-tag>
                </div>
              </div>

              <div class="selected-tags" v-if="newDiary.tags && newDiary.tags.length > 0">
                <p class="tag-section-title">已选标签:</p>
                <div class="tags-container">
                  <el-tag
                    v-for="tag in newDiary.tags"
                    :key="tag"
                    closable
                    @close="removeTag(tag)"
                    effect="light"
                    type="info"
                    class="selected-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="内容" prop="content">
            <el-input
              v-model="newDiary.content"
              type="textarea"
              placeholder="请输入游记内容，分享您的旅行体验、感受和建议..."
              :rows="8"
              maxlength="5000"
              show-word-limit
            ></el-input>
          </el-form-item>

          <el-form-item label="图片上传">
            <div class="custom-upload-container">
              <!-- 使用el-upload组件上传图片 -->
              <el-upload
                class="diary-uploader"
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :on-change="handleImageUpload"
                :on-remove="handleImageRemove"
                :limit="6"
                multiple
                accept="image/*"
                ref="uploadRef"
                :file-list="uploadFileList"
              >
                <template #default>
                  <el-icon><Plus /></el-icon>
                  <div class="el-upload__text">点击上传</div>
                </template>
                <template #file="{ file }">
                  <div class="upload-item">
                    <img class="upload-image" :src="file.url" alt="预览图" />
                    <div class="upload-actions">
                      <el-button
                        type="danger"
                        icon="Delete"
                        circle
                        size="small"
                        @click.stop="removeFile(file)"
                      ></el-button>
                    </div>
                  </div>
                </template>
              </el-upload>
            </div>
            <div class="upload-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>最多上传6张图片，大小不超过50MB，支持jpg、png、gif格式</span>
            </div>
          </el-form-item>

          <el-form-item label="视频上传">
            <div class="video-upload-container">
              <div v-if="!newDiary.video" class="video-upload-button" @click="triggerVideoUpload">
                <el-icon><VideoCamera /></el-icon>
                <span>点击上传视频</span>
              </div>

              <input
                type="file"
                ref="videoInput"
                style="display: none"
                accept="video/mp4,video/webm,video/ogg"
                @change="handleVideoUpload"
              />

              <div v-if="newDiary.video" class="video-preview">
                <video
                  controls
                  class="preview-video"
                  :src="newDiary.videoUrl"
                ></video>
                <div class="video-actions">
                  <el-button type="danger" size="small" @click="removeVideo">
                    <el-icon><Delete /></el-icon> 移除视频
                  </el-button>
                </div>
              </div>
            </div>
            <div class="upload-tip">
              <el-icon><InfoFilled /></el-icon>
              <span>最多上传3个视频，大小不超过50MB，支持mp4、webm、ogg格式</span>
            </div>
          </el-form-item>

          <el-form-item label="隐私设置">
            <el-radio-group v-model="newDiary.privacy">
              <el-radio :label="'public'">公开</el-radio>
              <el-radio :label="'private'">私密</el-radio>
            </el-radio-group>
            <div class="privacy-tip">
              <span v-if="newDiary.privacy === 'public'">公开的游记所有人可见</span>
              <span v-else>私密的游记仅自己可见</span>
            </div>
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelPost">取消</el-button>
            <el-button type="primary" @click="handlePostSubmit" :loading="submittingDiary">发布游记</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
  <!-- <el-card> -->
  <el-dialog
  v-model="editProfileDialogVisible"
  title="编辑个人资料"
  width="500px"
  :close-on-click-modal="false"
>
  <el-form :model="editForm" label-width="80px" :rules="editFormRules" ref="editFormRef">
    <el-form-item label="头像">
      <el-upload
        class="avatar-uploader"
        action=""
        :show-file-list="false"
        :before-upload="beforeAvatarUpload"
      >
        <img v-if="editForm.avatarUrl" :src="editForm.avatarUrl" class="avatar" />
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="用户名">
      <el-input v-model="editForm.username"></el-input>
    </el-form-item>
    <el-form-item label="邮箱">
      <el-input v-model="editForm.email"></el-input>
    </el-form-item>
    <el-form-item label="旧密码" prop="oldPassword">
      <el-input v-model="editForm.oldPassword" type="password" autocomplete="off" placeholder="请输入当前密码"></el-input>
      <div class="form-tip">* 修改信息需要验证当前密码</div>
    </el-form-item>
    <el-form-item label="新密码">
      <el-input v-model="editForm.password" type="password" autocomplete="off" placeholder="不修改密码请留空"></el-input>
    </el-form-item>
    <el-form-item label="确认密码">
      <el-input v-model="editForm.confirmPassword" type="password" autocomplete="off" placeholder="再次输入新密码"></el-input>
    </el-form-item>
  </el-form>
  <template #footer>
    <el-button @click="editProfileDialogVisible = false">取消</el-button>
    <el-button type="primary" @click="submitEditProfile">保存</el-button>
  </template>
</el-dialog>
  <el-dialog
    v-model="postMemoryDialogVisible"
    title="发布新的游记"
    width="700px"
    :close-on-click-modal="false"
  >
    <el-form :model="newMemory" label-width="80px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="newMemory.title" placeholder="给你的游记起个名字吧"></el-input>
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <el-input
          v-model="newMemory.content"
          type="textarea"
          :rows="6"
          placeholder="分享你的旅行故事..."
        ></el-input>
      </el-form-item>
      <el-form-item label="已选文件">
        <div class="preview-files">
          <div v-for="(file, index) in newMemory.files" :key="index" class="preview-item">
            <img v-if="file.type.startsWith('image/')" :src="file.url" class="preview-media">
            <video v-else-if="file.type.startsWith('video/')" :src="file.url" class="preview-media" controls></video>
            <!-- 可以添加移除文件的按钮 -->
          </div>
          <!-- 可以添加一个按钮继续添加文件 -->
          <el-button @click="handleShareMemoryClick" :icon="Plus" circle title="继续添加文件"></el-button>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog_up-footer">
        <el-button @click="postMemoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleMemorySubmit">发布</el-button>
      </span>
    </template>
  </el-dialog>
      <!-- </el-card> -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { updateAvatar, updateEmail, updateUsername } from '@/api/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex' // 导入useStore
import { getCurrentUser, logout } from '@/api/auth'
import defaultAvatar from '@/assets/belog.jpg';
import { format } from 'date-fns'
import { Document, Star, ChatDotRound, Location, Message, Plus, Delete, InfoFilled, VideoCamera, Refresh, CaretTop, View } from '@element-plus/icons-vue'
import axios from 'axios'

// 调试：检查图标组件是否正确导入
console.log('所有导入的图标组件:', { Document, Star, ChatDotRound, Location, Message, Plus, Delete, InfoFilled, VideoCamera, Refresh, CaretTop, View })
const postMemoryDialogVisible = ref(false)
const newMemory = reactive({
  title: '',
  content: '',
  files: [], // 存储待上传的文件对象和预览 URL
  imageUrls: [], // 存储上传成功后的图片 URL
  videoUrls: []  // 存储上传成功后的视频 URL
})

// 标签选项 - 与数据库中的地点类型关键词匹配
const tagOptions = [
  { value: '大学', label: '大学' },
  { value: '公园', label: '公园' },
  { value: '动物园', label: '动物园' },
  { value: '古建筑', label: '古建筑' },
  { value: '文化古迹', label: '文化古迹' },
  { value: '自然景观', label: '自然景观' },
  { value: '旅游景点', label: '旅游景点' },
  { value: '游乐园', label: '游乐园' },
  { value: '寺庙', label: '寺庙' }
]

// --- 新增：处理游记发布提交 ---
// const handleMemorySubmit = async () => {
//   if (!newMemory.title || !newMemory.content) {
//     ElMessage.warning('请输入标题和内容')
//     return
//   }
//   console.log('准备提交的游记数据:', {
//     title: newMemory.title,
//     content: newMemory.content,
//     files: newMemory.files // 这里是待上传的文件
//   })

//   ElMessage.info('发布功能开发中，请在控制台查看数据')
// }
const diaryList = ref([])

const formatDate = (timestamp) => {
  if (!timestamp) return '未知时间';

  let date;

  // 处理不同类型的时间输入
  if (typeof timestamp === 'string') {
    // 如果是ISO格式的字符串
    if (timestamp.includes('T') || timestamp.includes('Z') || timestamp.includes('+')) {
      date = new Date(timestamp);
    } else {
      // 如果是普通字符串，尝试解析
      date = new Date(timestamp);
    }
  } else if (typeof timestamp === 'number') {
    // 如果是时间戳
    date = new Date(timestamp);
  } else {
    // 其他情况，直接尝试创建Date对象
    date = new Date(timestamp);
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.error('无效的时间戳:', timestamp);
    return '未知时间';
  }

  return format(date, 'yyyy-MM-dd HH:mm');
}

// 查看详情
const handleViewClick = (diary) => {
  // 直接跳转到详情页，不增加浏览量
  // 浏览量将在详情页加载时更新一次
  // 添加来源参数，便于返回按钮识别
  router.push(`/diary/detail/${diary.id}?from=user-center`)
}

// 获取地点名称 (保留以备将来使用)
// const getLocationName = (locationId) => {
//   if (!locationId) return '未知地点'
//   const location = locationOptions.value.find(loc => loc.value === locationId)
//   return location ? location.label : '未知地点'
// }

// 去除HTML标签
const stripHtml = (html) => {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '');
}

// 处理图片加载错误
const handleImageError = (e) => {
  console.error('图片加载失败:', e);
  e.target.src = defaultAvatar; // 使用默认头像
}

// 处理视频加载错误
const handleVideoError = (e) => {
  console.error('视频加载失败:', e);
  const videoContainer = e.target.parentNode;
  if (videoContainer) {
    const errorMsg = document.createElement('div');
    errorMsg.className = 'video-error-message';
    errorMsg.textContent = '视频加载失败';
    videoContainer.appendChild(errorMsg);
    e.target.style.display = 'none';
  }
}

// 根据标题生成默认标签
const getDefaultTags = (title) => {
  // 如果标题包含某些关键词，返回相应的标签
  // 检查标题中是否包含地点关键词
  const locationKeywords = {
    '大学': ['大学', '文化古迹'],
    '公园': ['公园', '自然景观'],
    '动物园': ['动物园', '旅游景点'],
    '古建筑': ['古建筑', '文化古迹'],
    '古迹': ['文化古迹', '古建筑'],
    '自然': ['自然景观', '旅游景点'],
    '景点': ['旅游景点', '自然景观'],
    '游乐园': ['游乐园', '旅游景点'],
    '寺庙': ['寺庙', '文化古迹']
  }

  // 遍历地点关键词，检查标题中是否包含
  for (const [keyword, locationTags] of Object.entries(locationKeywords)) {
    if (title.toLowerCase().includes(keyword.toLowerCase())) {
      return locationTags
    }
  }

  // 如果没有匹配到关键词，返回一些通用标签
  return ['旅游景点', '自然景观']
}

const editProfileDialogVisible = ref(false)
const editFormRef = ref(null)
const editForm = reactive({
  username: '',
  email: '',
  oldPassword: '',
  password: '',
  confirmPassword: '',
  avatar: null,
  avatarUrl: ''
})

// 表单验证规则
const editFormRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ]
}

const handleEditProfile = () => {
  // 初始化表单数据
  editForm.username = userInfo.value.username
  editForm.email = userInfo.value.email
  editForm.oldPassword = ''
  editForm.password = ''
  editForm.confirmPassword = ''
  editForm.avatar = null
  editForm.avatarUrl = avatarUrl.value
  editProfileDialogVisible.value = true
}

// 头像选择预览
const beforeAvatarUpload = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    editForm.avatarUrl = e.target.result
    editForm.avatar = file
  }
  reader.readAsDataURL(file)
  // 阻止 el-upload 自动上传
  return false
}

const submitEditProfile = async () => {
  // 表单验证
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
  } catch (error) {
    return // 表单验证失败
  }

  if (!editForm.username || !editForm.email) {
    ElMessage.warning('用户名和邮箱不能为空')
    return
  }

  if (!editForm.oldPassword) {
    ElMessage.warning('请输入当前密码')
    return
  }

  if (editForm.password && editForm.password !== editForm.confirmPassword) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }

  // 首先验证旧密码是否正确
  try {
    const isPasswordCorrect = editForm.oldPassword === "123456"

    if (!isPasswordCorrect) {
      ElMessage.error('当前密码不正确，无法更新个人资料')
      return
    }

    // 如果密码验证通过，继续执行后续操作
    let hasUpdates = false

    // 头像上传
    if (editForm.avatar) {
      const formData = new FormData()
      formData.append('user_id', userInfo.value.id)
      formData.append('avatar', editForm.avatar)
      formData.append('password', editForm.oldPassword) // 使用旧密码验证

      const res = await updateAvatar(formData)
      if (res.code === 0 && res.data && res.data.avatar_url) {
        userInfo.value.avatar = res.data.avatar_url
        hasUpdates = true
      } else {
        ElMessage.error(res.message || '头像更新失败')
        return
      }
    }

    // 用户名和邮箱
    if (editForm.username !== userInfo.value.username) {
      const res = await updateUsername({
        user_id: userInfo.value.id,
        new_username: editForm.username,
        password: editForm.oldPassword // 使用旧密码验证
      })
      if (res.code === 0) {
        userInfo.value.username = editForm.username
        hasUpdates = true
      } else {
        ElMessage.error(res.message || '用户名更新失败')
        return
      }
    }

    if (editForm.email !== userInfo.value.email) {
      const res = await updateEmail({
        user_id: userInfo.value.id,
        new_email: editForm.email,
        password: editForm.oldPassword // 使用旧密码验证
      })
      if (res.code === 0) {
        userInfo.value.email = editForm.email
        hasUpdates = true
      } else {
        ElMessage.error(res.message || '邮箱更新失败')
        return
      }
    }

    // 密码单独处理（如有接口）
    if (editForm.password) {
      try {

        console.log('密码更新功能待实现')
        hasUpdates = true // 临时设置为true，实际应该根据API返回结果设置
      } catch (error) {
        console.error('密码更新失败:', error)
        ElMessage.error('密码更新失败')
        return
      }
    }

    // 关闭弹窗
    editProfileDialogVisible.value = false

    // 只有在有更新时才显示成功消息
    if (hasUpdates) {
      ElMessage.success('个人资料更新成功')
    } else {
      ElMessage.info('未进行任何修改')
    }

  } catch (error) {
    console.error('更新个人资料失败:', error)
    ElMessage.error('更新个人资料失败，请稍后重试')
  }
}
const router = useRouter()
const store = useStore() // 初始化store

const userInfo = ref({
  username: '',
  email: '',
  avatar: null,
  id: null
})
const loadUserData = async () => {
  console.log('开始加载用户数据')

  // 尝试从localStorage获取用户ID
  const userId = localStorage.getItem('userId')
  console.log('从localStorage获取的userId:', userId)

  // 尝试从localStorage获取完整用户信息
  const localUser = localStorage.getItem('currentUser')
  if (localUser) {
    try {
      const parsed = JSON.parse(localUser)
      console.log('从localStorage获取的用户信息:', parsed)

      // 方法一：直接替换整个对象
      userInfo.value = {
        username: parsed.username,
        email: parsed.email,
        avatar: parsed.avatar,
        id: parsed.id || parsed.user_id || userId // 尝试多种可能的ID字段
      }

      console.log('从localStorage更新后的userInfo:', userInfo.value)
    } catch (e) {
      console.error('解析 localStorage 失败:', e)
    }
  }

  // 如果userInfo中没有id，但有userId，则使用userId
  if (!userInfo.value.id && userId) {
    userInfo.value.id = userId
    console.log('使用localStorage.userId更新userInfo.id:', userInfo.value.id)
  }

  // 尝试从API获取最新用户信息
  try {
    console.log('尝试从API获取用户信息')
    const response = await getCurrentUser()
    console.log('API响应:', response)

    if (response.status === 'success' && response.user) {
      // 方法二：合并更新
      Object.assign(userInfo.value, {
        username: response.user.username,
        email: response.user.email,
        avatar: response.user.avatar,
        id: response.user.user_id || response.user.id // 注意字段名可能是user_id或id
      })

      console.log('从API更新后的userInfo:', userInfo.value)

      // 更新localStorage中的用户信息
      localStorage.setItem('currentUser', JSON.stringify(userInfo.value))
      if (userInfo.value.id) {
        localStorage.setItem('userId', userInfo.value.id)
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }

  // 最终检查
  if (!userInfo.value.id) {
    console.warn('警告：用户ID仍然为空！')
  } else {
    console.log('最终用户ID:', userInfo.value.id)
  }
}

onMounted(async () => {
  try {
    await loadUserData()
    console.log('最终 userInfo:', userInfo.value) // 此时应有值

    // 确保用户ID已加载
    if (!userInfo.value.id) {
      console.log('尝试从localStorage获取用户ID');
      const userId = localStorage.getItem('userId');
      const currentUser = localStorage.getItem('currentUser');

      if (userId) {
        userInfo.value.id = userId;
        console.log('从localStorage.userId获取到ID:', userId);
      } else if (currentUser) {
        try {
          const parsed = JSON.parse(currentUser);
          if (parsed.id) {
            userInfo.value.id = parsed.id;
            console.log('从localStorage.currentUser获取到ID:', parsed.id);
          }
        } catch (e) {
          console.error('解析currentUser失败:', e);
        }
      }
    }

    console.log('最终确认用户ID:', userInfo.value.id);

    // 加载初始数据
    if (userInfo.value.id) {
      console.log('开始加载用户数据');

      // 确保数据库中有地点数据
      console.log('确保数据库中有地点数据');
      await ensureLocationsExist();

      // 立即加载日记数据，不等待标签页切换
      await fetchDiaries(); // 加载用户日记
      fetchFavorites();
      fetchComments();
      fetchLikes();
      await fetchLocations(); // 加载地点列表

      console.log('初始数据加载完成');
    } else {
      console.error('无法获取用户ID，无法加载数据');
      ElMessage.error('无法获取用户信息，请重新登录');
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
  }
})
console.log(11)
const backendBaseUrl = 'http://localhost:5000';

// 处理头像URL
function getFullAvatarUrl(avatarPath) {
  if (!avatarPath) return defaultAvatar;
  if (typeof avatarPath === 'string') {
    if (avatarPath.startsWith('/uploads/')) {
      return backendBaseUrl + avatarPath;
    }
    if (
      avatarPath.startsWith('http://') ||
      avatarPath.startsWith('https://') ||
      avatarPath.startsWith('data:image')
    ) {
      return avatarPath;
    }
  }
  return defaultAvatar;
}

// 处理图片和视频URL
function getFullImageUrl(url) {
  if (!url) return '';
  if (typeof url === 'string') {
    // 如果是相对路径，添加服务器基础URL
    if (url.startsWith('/uploads/')) {
      return backendBaseUrl + url;
    }
    // 如果已经是完整URL或数据URL，直接返回
    if (
      url.startsWith('http://') ||
      url.startsWith('https://') ||
      url.startsWith('data:')
    ) {
      return url;
    }
    // 其他情况，尝试添加基础URL
    if (!url.startsWith('/')) {
      return backendBaseUrl + '/' + url;
    }
    return backendBaseUrl + url;
  }
  return url;
}

// 头像URL计算属性 - 优先使用Vuex，然后是userInfo，最后是localStorage
const avatarUrl = computed(() => {
  // 1. 优先从Vuex获取
  if (store.getters.isAuthenticated && store.getters.currentUser) {
    const avatar = store.getters.currentUser.avatar;
    console.log('UserCenter.vue - 从Vuex获取头像:', avatar);
    // 如果用户没有头像或头像为默认值，返回默认头像
    if (!avatar || avatar === '' || avatar === null || avatar === undefined || avatar === 'default_avatar.jpg') {
      console.log('UserCenter.vue - Vuex中用户无头像或为默认头像，使用默认头像');
      return defaultAvatar;
    }
    return getFullAvatarUrl(avatar);
  }

  // 2. 从组件内的userInfo获取
  if (userInfo.value) {
    const avatar = userInfo.value.avatar;
    console.log('UserCenter.vue - 从userInfo获取头像:', avatar);
    // 如果用户没有头像或头像为默认值，返回默认头像
    if (!avatar || avatar === '' || avatar === null || avatar === undefined || avatar === 'default_avatar.jpg') {
      console.log('UserCenter.vue - userInfo中用户无头像或为默认头像，使用默认头像');
      return defaultAvatar;
    }
    return getFullAvatarUrl(avatar);
  }

  // 3. 从localStorage获取
  try {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
      const userData = JSON.parse(currentUser);
      const avatar = userData.avatar;
      console.log('UserCenter.vue - 从localStorage获取头像:', avatar);
      // 如果用户没有头像或头像为默认值，返回默认头像
      if (!avatar || avatar === '' || avatar === null || avatar === undefined || avatar === 'default_avatar.jpg') {
        console.log('UserCenter.vue - localStorage中用户无头像或为默认头像，使用默认头像');
        return defaultAvatar;
      }
      return getFullAvatarUrl(avatar);
    }
  } catch (e) {
    console.error('UserCenter.vue - 解析localStorage中的用户信息失败:', e);
  }

  // 4. 默认头像
  console.log('UserCenter.vue - 使用默认头像');
  return defaultAvatar;
});
console.log(22)
console.log(avatarUrl)
// 用户统计数据
const userStats = reactive({
  favorites: 0,
  comments: 0,
  likes: 0
})

// 标签页状态
const activeTab = ref('diaries')

// 收藏数据
const favorites = ref([])
const loadingFavorites = ref(false)
async function fetchFavorites() {
  if (!userInfo.value.id) return
  loadingFavorites.value = true
  try {
    console.log('获取用户收藏，用户ID:', userInfo.value.id);

    // 获取文章收藏
    const articleResponse = await axios.get(`http://localhost:5000/api/favorites/list/${userInfo.value.id}`);
    console.log('获取用户文章收藏响应:', articleResponse);

    // 获取地点收藏
    const locationResponse = await axios.get(`http://localhost:5000/api/location_favorite/user/${userInfo.value.id}`);
    console.log('获取用户地点收藏响应:', locationResponse);

    // 处理文章收藏
    let articleFavorites = [];
    if (articleResponse.data && articleResponse.data.code === 0 &&
        articleResponse.data.data && articleResponse.data.data.favorites) {
      articleFavorites = articleResponse.data.data.favorites.map(item => {
        console.log('处理文章收藏项:', item);

        return {
          id: item.article_id,
          article_id: item.article_id,
          type: 'article', // 标记为文章类型
          title: item.title || '无标题',
          content: item.content || '无内容',
          author: {
            username: item.author || '未知作者',
            avatar: item.author_avatar ? getFullImageUrl(item.author_avatar) : require('@/assets/belog.jpg')
          },
          author_name: item.author || '未知作者', // 添加author_name字段以兼容前端显示
          createTime: item.created_at ? new Date(item.created_at).getTime() : Date.now(),
          favorited_at: item.favorited_at ? new Date(item.favorited_at).getTime() : Date.now(),
          images: item.image_url ? [getFullImageUrl(item.image_url)] : [],
          commentsCount: item.comments_count || 0,
          likes: item.likes_count || 0,
          favorites: item.favorites_count || 0,
          views: item.popularity || 0,
          location: item.location || item.location_name || '未知地点'
        };
      });
    }

    // 处理地点收藏
    let locationFavorites = [];
    if (locationResponse.data && locationResponse.data.code === 0 &&
        locationResponse.data.data && locationResponse.data.data.favorites) {
      locationFavorites = locationResponse.data.data.favorites.map(item => {
        console.log('处理地点收藏项:', item);

        return {
          id: item.location_id,
          location_id: item.location_id,
          type: 'location', // 标记为地点类型
          title: item.name || '未知地点',
          content: item.description || '无描述',
          createTime: item.created_at ? new Date(item.created_at).getTime() : Date.now(),
          favorited_at: item.favorited_at ? new Date(item.favorited_at).getTime() : Date.now(),
          images: item.image_url ? [getFullImageUrl(item.image_url)] : [],
          evaluation: item.evaluation || 0,
          popularity: item.popularity || 0,
          address: item.address || '',
          keyword: item.keyword || ''
        };
      });
    }

    // 合并文章和地点收藏
    favorites.value = [...articleFavorites, ...locationFavorites];
    userStats.favorites = favorites.value.length;
    console.log('处理后的收藏数据:', favorites.value);
  } catch (e) {
    console.error('获取收藏失败:', e);
    favorites.value = [];
  } finally {
    loadingFavorites.value = false;
  }
}

// 评论数据
const comments = ref([])
const loadingComments = ref(false)
async function fetchComments() {
  if (!userInfo.value.id) return
  loadingComments.value = true
  try {
    console.log('获取用户评论，用户ID:', userInfo.value.id);

    // 使用正确的API路径
    const response = await axios.get(`http://localhost:5000/api/article_comment/user/${userInfo.value.id}`);
    console.log('获取用户评论响应:', response);

    if (response.data && response.data.code === 0 && response.data.data) {
      // 处理返回的评论数据
      comments.value = response.data.data.map(comment => ({
        id: comment.comment_id,
        article_id: comment.article_id,
        article_title: comment.article_title || '未知文章',
        article_content: comment.article_content || '无内容',
        content: comment.content || '无内容',
        created_at: comment.created_at || new Date().toISOString()
      }));
      userStats.comments = comments.value.length;
      console.log('处理后的评论数据:', comments.value);
    } else {
      comments.value = [];
      console.log('未获取到评论数据或数据格式不正确');
    }
  } catch (e) {
    console.error('获取评论失败:', e);
    comments.value = [];
  } finally {
    loadingComments.value = false;
  }
}

// 点赞数据
const likes = ref([])
const loadingLikes = ref(false)
async function fetchLikes() {
  if (!userInfo.value.id) return
  loadingLikes.value = true
  try {
    // 使用正确的API路由获取用户点赞的文章
    const userId = userInfo.value.id
    const response = await axios.get(`http://localhost:5000/api/article_like/user/${userId}`)
    console.log('获取用户点赞响应:', response)

    if (response.data && response.data.code === 0 && response.data.data) {
      // 处理返回的点赞文章数据
      likes.value = response.data.data.map(article => ({
        id: article.article_id,
        article_id: article.article_id,
        title: article.title || '无标题',
        content: article.content || '无内容',
        author: {
          username: article.author || '未知作者',
          avatar: article.author_avatar ? getFullImageUrl(article.author_avatar) : require('@/assets/belog.jpg')
        },
        createTime: article.created_at ? new Date(article.created_at).getTime() : Date.now(),
        liked_at: article.liked_at ? new Date(article.liked_at).getTime() : Date.now(),
        images: article.image_url ? [getFullImageUrl(article.image_url)] : [],
        commentsCount: article.comments_count || 0,
        likes: article.likes_count || 0,
        favorites: article.favorites_count || 0,
        views: article.popularity || 0
      }))
      userStats.likes = likes.value.length
      console.log('处理后的点赞数据:', likes.value)
    } else {
      likes.value = []
      console.log('未获取到点赞数据或数据格式不正确')
    }
  } catch (e) {
    console.error('获取点赞失败:', e)
    likes.value = []
  } finally {
    loadingLikes.value = false
  }
}

// 加载用户日记列表
const loadingDiaries = ref(false)
async function fetchDiaries() {
  if (!userInfo.value.id) {
    console.error('无法获取用户ID，fetchDiaries终止');
    return;
  }

  loadingDiaries.value = true;
  console.log('开始获取用户日记列表，用户ID:', userInfo.value.id);

  try {
    // 使用axios进行请求，便于调试
    const response = await axios.get(`http://localhost:5000/api/articles/user/${userInfo.value.id}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || localStorage.getItem('authToken')}`
      }
    });

    console.log('API响应完整数据:', response);

    // 获取响应数据
    const data = response.data;
    console.log('获取到的日记数据:', data);

    // 清空现有列表
    diaryList.value = [];

    if (data.code === 0) {
      // 直接使用data.data作为文章数组
      const articles = Array.isArray(data.data) ? data.data : [];
      console.log('解析后的文章数组:', articles);

      if (articles.length > 0) {
        console.log(`找到${articles.length}篇文章`);

        // 处理每篇日记
        articles.forEach(article => {
          console.log('处理文章:', article);

          // 确保所有必要的字段都存在
          const diaryItem = {
            id: article.article_id,
            title: article.title || '无标题',
            content: article.content || '无内容',
            createTime: article.created_at ? new Date(article.created_at).getTime() : Date.now(),
            images: article.image_url ? [getFullImageUrl(article.image_url)] : [],
            video: article.video_url ? getFullImageUrl(article.video_url) : null,
            comments_count: article.comments_count || 0,
            likes_count: article.likes_count || 0,
            favorites_count: article.favorites_count || 0,
            popularity: article.popularity || 0,
            location_id: article.location_id || null,
            location_name: article.location || article.location_name || '未知地点',
            rating: article.evaluation || 0, // 使用文章评分
            avgRating: article.evaluation || 0, // 平均评分
            ratingCount: 0, // 评分人数
            author_id: article.user_id || userInfo.value.id,
            author_name: article.username || (article.user_id === userInfo.value.id ? userInfo.value.username : '未知作者'),
            tags: article.tags ? (typeof article.tags === 'string' ? article.tags.split(',').map(tag => tag.trim()) : article.tags) : [] // 处理标签字段
          };

          // 异步获取最新的收藏计数和评分
          (async () => {
            try {
              // 获取收藏数
              const favoriteResponse = await axios.get(`http://localhost:5000/api/favorites/count/${article.article_id}`);
              if (favoriteResponse.data && favoriteResponse.data.code === 0) {
                diaryItem.favorites_count = favoriteResponse.data.data.count;
                console.log(`文章ID: ${article.article_id} 的收藏数更新为: ${diaryItem.favorites_count}`);
              }

              // 获取平均评分
              const ratingResponse = await axios.get(`http://localhost:5000/api/article_score/average/${article.article_id}`);
              if (ratingResponse.data && ratingResponse.data.code === 0) {
                diaryItem.avgRating = ratingResponse.data.data.average_score || 0;
                diaryItem.ratingCount = ratingResponse.data.data.rating_count || 0;
                console.log(`文章ID: ${article.article_id} 的平均评分更新为: ${diaryItem.avgRating}, 评分人数: ${diaryItem.ratingCount}`);
              }
            } catch (error) {
              console.error(`获取文章ID: ${article.article_id} 的收藏数或评分失败:`, error);
            }
          })();

          console.log('创建的日记项:', diaryItem);
          diaryList.value.push(diaryItem);
        });

        console.log('处理后的日记列表:', diaryList.value);
      } else {
        console.log('未找到文章');
      }
    } else {
      console.warn('API返回错误:', data.message);
      ElMessage.warning(`获取日记列表失败: ${data.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('获取日记列表异常:', error);

    // 详细记录错误信息
    if (error.response) {
      console.error('错误响应:', error.response.data);
      console.error('错误状态码:', error.response.status);
      ElMessage.error(`获取日记失败: ${error.response.data?.message || '服务器错误'} (${error.response.status})`);
    } else if (error.request) {
      console.error('请求未收到响应:', error.request);
      ElMessage.error('获取日记失败: 服务器无响应');
    } else {
      console.error('请求配置错误:', error.message);
      ElMessage.error(`获取日记失败: ${error.message}`);
    }

    diaryList.value = [];
  } finally {
    loadingDiaries.value = false;
  }
}

// 标签页切换处理
const handleTabChange = (tab) => {
  if (tab === 'diaries' && diaryList.value.length === 0) {
    fetchDiaries()
  } else if (tab === 'favorites' && favorites.value.length === 0) {
    fetchFavorites()
  } else if (tab === 'comments' && comments.value.length === 0) {
    fetchComments()
  } else if (tab === 'likes' && likes.value.length === 0) {
    fetchLikes()
  }
}

// 查看详情方法
const viewFavoriteDetail = (item) => {
  console.log('查看收藏详情:', item);

  // 根据类型字段判断
  if (item.type === 'article') {
    const articleId = item.article_id || item.id;
    console.log('跳转到文章详情页:', articleId);
    router.push(`/diary/detail/${articleId}?from=user-center`);
    return;
  }

  if (item.type === 'location') {
    const locationId = item.location_id || item.id;
    console.log('跳转到地点详情页:', locationId);
    router.push(`/location/${locationId}`);
    return;
  }

  // 如果没有类型字段，尝试根据ID判断
  if (item.article_id) {
    console.log('跳转到文章详情页:', item.article_id);
    router.push(`/diary/detail/${item.article_id}?from=user-center`);
    return;
  }

  if (item.location_id) {
    console.log('跳转到地点详情页:', item.location_id);
    router.push(`/location/${item.location_id}`);
    return;
  }

  // 如果都没有，尝试使用id字段
  if (item.id) {
    // 根据item的属性判断是文章还是地点
    if (item.title && item.content && !item.address) {
      console.log('使用id跳转到文章详情页:', item.id);
      router.push(`/diary/detail/${item.id}?from=user-center`);
    } else {
      console.log('使用id跳转到地点详情页:', item.id);
      router.push(`/location/${item.id}`);
    }
    return;
  }

  console.error('无法确定收藏项的类型，无法跳转:', item);
  ElMessage.warning('无法查看详情，收藏项信息不完整');
}

const viewCommentDetail = (comment) => {
  console.log('查看评论详情:', comment);

  // 检查是否有文章ID
  if (comment.article_id) {
    console.log('跳转到文章详情页:', comment.article_id);
    router.push(`/diary/detail/${comment.article_id}?from=user-center`);
    return;
  }

  // 检查是否有地点ID
  if (comment.location_id) {
    console.log('跳转到地点详情页:', comment.location_id);
    router.push(`/location/${comment.location_id}`);
    return;
  }

  console.error('无法确定评论项的类型，无法跳转:', comment);
  ElMessage.warning('无法查看详情，评论项信息不完整');
}

const viewLikeDetail = (item) => {
  console.log('查看点赞详情:', item);

  // 检查是否有文章ID
  if (item.article_id) {
    console.log('跳转到文章详情页:', item.article_id);
    router.push(`/diary/detail/${item.article_id}?from=user-center`);
    return;
  }

  // 检查是否有地点ID
  if (item.location_id) {
    console.log('跳转到地点详情页:', item.location_id);
    router.push(`/location/${item.location_id}`);
    return;
  }

  // 如果都没有，尝试使用id字段
  if (item.id) {
    console.log('使用id跳转到文章详情页:', item.id);
    router.push(`/diary/detail/${item.id}?from=user-center`);
    return;
  }

  console.error('无法确定点赞项的类型，无法跳转:', item);
  ElMessage.warning('无法查看详情，点赞项信息不完整');
}

// 取消收藏
const removeFavorite = async (item) => {
  try {
    console.log('取消收藏项:', item);

    // 判断是文章收藏还是景点收藏
    let endpoint, requestData;

    if (item.location_id && !item.article_id) {
      // 景点收藏
      endpoint = 'http://localhost:5000/api/location_favorite/unfavorite';
      requestData = {
        user_id: userInfo.value.id,
        location_id: item.location_id
      };
      console.log('取消景点收藏:', requestData);
    } else {
      // 文章收藏
      endpoint = 'http://localhost:5000/api/favorites/remove';
      requestData = {
        user_id: userInfo.value.id,
        article_id: item.article_id
      };
      console.log('取消文章收藏:', requestData);
    }

    const res = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    const data = await res.json();
    console.log('取消收藏响应:', data);

    if (data.code === 0) {
      ElMessage.success('取消收藏成功');
      // 重新获取收藏列表
      fetchFavorites();
    } else {
      ElMessage.error(data.message || '取消收藏失败');
    }
  } catch (e) {
    console.error('取消收藏失败:', e);
    ElMessage.error('取消收藏失败，请稍后再试');
  }
}

// 跳转到推荐页面
const goToRecommend = () => {
  router.push('/recommend')
}

// 监听标签页变化
watch(activeTab, (newVal) => {
  handleTabChange(newVal)
})

// 旅游日记相关
const diaryDialogVisible = ref(false)
const diaryFormRef = ref(null)
const submittingDiary = ref(false)
const newDiary = reactive({
  title: '',
  content: '',
  location: '',
  tags: [],
  images: [],
  video: null,
  videoUrl: '',
  privacy: 'public'
})

// 视频上传相关
const videoInput = ref(null)

// 图片上传组件引用
const uploadRef = ref(null)

// 上传文件列表
const uploadFileList = ref([])

// 表单验证规则
const diaryRules = {
  title: [
    { required: true, message: '请输入游记标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度应在2-50个字符之间', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请选择或输入旅行地点', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入游记内容', trigger: 'blur' },
    { min: 10, message: '内容至少需要10个字符', trigger: 'blur' }
  ]
}

// 地点选项
const locationOptions = ref([
  { value: 1, label: '北京' },
  { value: 2, label: '上海' },
  { value: 3, label: '广州' },
  { value: 4, label: '深圳' },
  { value: 5, label: '杭州' },
  { value: 6, label: '成都' },
  { value: 7, label: '西安' },
  { value: 8, label: '三亚' },
  { value: 9, label: '丽江' },
  { value: 10, label: '大理' }
])

// 地点建议相关函数
const queryLocationSuggestions = async (queryString, callback) => {
  console.log('queryLocationSuggestions 被调用，查询字符串:', queryString);

  if (!queryString || queryString.trim().length < 1) {
    console.log('查询字符串为空，返回空数组');
    callback([]);
    return;
  }

  try {
    console.log('准备调用API，URL:', `http://localhost:5000/api/locations/query`);
    console.log('请求参数:', { name: queryString, sortOrder: '0' });

    // 调用后端API获取地点建议
    const response = await axios.get(`http://localhost:5000/api/locations/query`, {
      params: {
        name: queryString,
        sortOrder: '0' // 按人气排序
      }
    });

    console.log('API响应:', response);
    console.log('响应数据:', response.data);

    if (response.data && Array.isArray(response.data)) {
      // 转换数据格式，添加value字段供el-autocomplete使用
      const suggestions = response.data.slice(0, 10).map(location => ({
        ...location,
        value: location.name, // el-autocomplete需要的value字段
        name: location.name,
        type: location.type,
        location_id: location.location_id
      }));

      console.log('转换后的建议数据:', suggestions);
      callback(suggestions);
    } else {
      console.log('响应数据格式不正确或为空');
      callback([]);
    }
  } catch (error) {
    console.error('获取地点建议失败:', error);
    console.error('错误详情:', error.response);
    callback([]);
  }
};

// 处理地点选择
const handleLocationSelect = (item) => {
  console.log('选择了地点:', item);
  newDiary.location = item.name;
};

// 图片上传处理
const handleImageUpload = (file) => {
  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  // eslint-disable-next-line no-unused-vars
  const isLt50M = file.raw.size / 1024 / 1024 < 50
  // 不显示错误提示，直接通过

  // 读取文件
  const reader = new FileReader()
  reader.onload = (e) => {
    file.url = e.target.result

    // 检查是否已存在相同的图片（通过文件名和大小判断）
    const isDuplicate = newDiary.images.some(img =>
      img === e.target.result ||
      (file.raw && uploadRef.value && uploadRef.value.uploadFiles.some(f =>
        f !== file && f.raw && f.raw.name === file.raw.name && f.raw.size === file.raw.size
      ))
    )

    if (isDuplicate) {
      console.log('检测到重复图片，不添加到数组中')
    } else {
      // 将图片URL添加到newDiary.images数组中
      newDiary.images.push(e.target.result)
      console.log('添加图片到数组:', newDiary.images.length)

      // 确保文件在uploadFileList中
      if (!uploadFileList.value.some(f => f.uid === file.uid)) {
        uploadFileList.value.push(file)
      }
    }

    // 保存文件对象和URL的映射关系，便于后续删除
    file._url = e.target.result
  }
  reader.readAsDataURL(file.raw)

  console.log('处理图片上传:', file.raw.name, file.raw.size)
  return false // 阻止自动上传
}

// 移除图片（通过文件对象，由el-upload组件调用）
const handleImageRemove = (file) => {
  console.log('通过文件对象移除图片:', file)
  removeImageFromArray(file)
}

// 手动移除文件（通过删除按钮调用）
const removeFile = (file) => {
  console.log('手动移除文件:', file)

  // 从上传组件中移除文件
  if (uploadRef.value) {
    uploadRef.value.handleRemove(file)
  }

  // 从数组中移除图片
  removeImageFromArray(file)
}

// 从数组中移除图片的通用函数
const removeImageFromArray = (file) => {
  // 尝试使用保存的URL映射
  let urlToRemove = file._url || file.url

  // 从newDiary.images数组中移除图片
  const index = newDiary.images.indexOf(urlToRemove)
  if (index !== -1) {
    console.log('找到要删除的图片，索引:', index)
    newDiary.images.splice(index, 1)
    console.log('删除后的图片数组长度:', newDiary.images.length)
  } else {
    console.warn('未找到要删除的图片:', urlToRemove)
    console.log('当前图片数组长度:', newDiary.images.length)

    // 尝试遍历查找部分匹配的URL
    for (let i = 0; i < newDiary.images.length; i++) {
      if (typeof newDiary.images[i] === 'string' &&
          (newDiary.images[i].includes(urlToRemove) || urlToRemove.includes(newDiary.images[i]))) {
        console.log('找到部分匹配的图片，索引:', i)
        newDiary.images.splice(i, 1)
        console.log('删除后的图片数组长度:', newDiary.images.length)
        break
      }
    }
  }

  // 从uploadFileList中移除文件
  const fileIndex = uploadFileList.value.findIndex(f => f.uid === file.uid)
  if (fileIndex !== -1) {
    uploadFileList.value.splice(fileIndex, 1)
    console.log('从uploadFileList中移除文件，剩余:', uploadFileList.value.length)
  }

  // 如果删除后图片数组为空，可以显示提示
  if (newDiary.images.length === 0) {
    console.log('已删除所有图片')
  }
}

const triggerVideoUpload = () => {
  if (videoInput.value) {
    videoInput.value.click()
  }
}

const handleVideoUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return

  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    ElMessage.error('请上传视频文件!')
    return
  }

  // 检查文件大小 (50MB)
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('视频大小不能超过50MB!')
    return
  }

  // 读取文件并预览
  const reader = new FileReader()
  reader.onload = (e) => {
    newDiary.video = file
    newDiary.videoUrl = e.target.result
  }
  reader.readAsDataURL(file)
}

// 移除视频
const removeVideo = () => {
  newDiary.video = null
  newDiary.videoUrl = ''
  if (videoInput.value) {
    videoInput.value.value = ''
  }
}

// 标签输入
const tagInput = ref('')

// 添加自定义标签
const addCustomTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !newDiary.tags.includes(tag)) {
    newDiary.tags.push(tag)
  }
  tagInput.value = '' // 清空输入框
}

// 添加预设标签
const addPresetTag = (tag) => {
  if (!newDiary.tags.includes(tag)) {
    newDiary.tags.push(tag)
  } else {
    // 如果已经选择了该标签，则移除它
    removeTag(tag)
  }
}

// 移除标签
const removeTag = (tag) => {
  const index = newDiary.tags.indexOf(tag)
  if (index !== -1) {
    newDiary.tags.splice(index, 1)
  }
}

// 清空所有图片
// const clearAllImages = () => {
//   console.log('清空所有图片')

//   // 清空图片数组
//   newDiary.images = []

//   // 清空上传组件的文件列表
//   if (uploadRef.value) {
//     uploadRef.value.clearFiles()
//   }

//   ElMessage.success('已清空所有图片')
// }




// 取消发布
const cancelPost = () => {
  ElMessageBox.confirm(
    '确定要取消发布吗？已编辑的内容将不会保存。',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '继续编辑',
      type: 'warning'
    }
  ).then(() => {
    diaryDialogVisible.value = false
    resetForm()
  }).catch(() => {
    // 用户选择继续编辑，不做任何操作
  })
}

// 重置表单
const resetForm = () => {
  if (diaryFormRef.value) {
    diaryFormRef.value.resetFields()
  }
  newDiary.title = ''
  newDiary.content = ''
  newDiary.location = ''
  newDiary.tags = []
  newDiary.images = []
  newDiary.video = null
  newDiary.videoUrl = ''
  newDiary.privacy = 'public'

  // 重置视频上传输入框
  if (videoInput.value) {
    videoInput.value.value = ''
  }

  // 重置图片上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  // 清空上传文件列表
  uploadFileList.value = []
}

// 提交发布
const handlePostSubmit = () => {
  if (!diaryFormRef.value) return

  diaryFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请完善表单信息')
      return
    }

    // 开始提交
    submittingDiary.value = true

    try {
      // 获取当前用户ID
      const userId = userInfo.value.id || localStorage.getItem('userId') || 1

      // 准备文章数据
      const articleData = {
        user_id: userId,
        title: newDiary.title,
        content: newDiary.content,
        location: newDiary.location,
        tags: newDiary.tags  // 添加标签数据
      }

      // 如果有图片，先上传图片
      let imageUrl = null
      if (newDiary.images.length > 0) {
        // 这里我们只处理第一张图片，实际应用可能需要处理多张
        try {
          // 将base64转换为Blob对象
          const base64Data = newDiary.images[0]
          const byteString = atob(base64Data.split(',')[1])
          const mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0]
          const ab = new ArrayBuffer(byteString.length)
          const ia = new Uint8Array(ab)
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i)
          }
          const blob = new Blob([ab], { type: mimeString })
          const file = new File([blob], `image_${Date.now()}.jpg`, { type: mimeString })

          // 上传图片
          const formData = new FormData()
          formData.append('file', file)
          // 获取token
          const token = localStorage.getItem('token') || localStorage.getItem('authToken')

          const uploadRes = await axios.post('http://localhost:5000/api/upload/image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
              'Authorization': token ? `Bearer ${token}` : undefined
            }
          })
          if (uploadRes.data.code === 0) {
            imageUrl = uploadRes.data.data.url
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          ElMessage.warning('图片上传失败，将只保存文字内容')
        }
      }

      // 如果有视频，上传视频
      let videoUrl = null
      if (newDiary.video) {
        try {
          // 上传视频
          const videoFormData = new FormData()
          videoFormData.append('file', newDiary.video)

          console.log('准备上传视频:', newDiary.video.name, newDiary.video.size, newDiary.video.type)

          // 获取token
          const token = localStorage.getItem('token') || localStorage.getItem('authToken')

          const videoUploadRes = await axios.post('http://localhost:5000/api/upload/video', videoFormData, {
            headers: {
              'Content-Type': 'multipart/form-data',
              'Authorization': token ? `Bearer ${token}` : undefined
            }
          })
          console.log('视频上传响应:', videoUploadRes.data)

          if (videoUploadRes.data.code === 0) {
            videoUrl = videoUploadRes.data.data.url
            console.log('视频上传成功，URL:', videoUrl)
          }
        } catch (error) {
          console.error('视频上传失败:', error)

          if (error.response) {
            console.error('视频上传错误响应:', error.response.data)
            console.error('视频上传错误状态码:', error.response.status)
          }

          ElMessage.warning('视频上传失败，将只保存其他内容')
        }
      }

      // 如果成功上传了图片，添加到文章数据中
      if (imageUrl) {
        articleData.image_url = imageUrl
      }

      // 如果成功上传了视频，添加到文章数据中
      if (videoUrl) {
        articleData.video_url = videoUrl
      }

      // 提交文章数据
      console.log('准备提交文章数据:', articleData)

      // 获取token
      const token = localStorage.getItem('token') || localStorage.getItem('authToken')

      // 使用axios实例发送请求，确保正确设置headers
      const response = await axios.post('http://localhost:5000/api/articles', articleData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : undefined
        }
      })

      if (response.data.code === 0) {
        // 创建新游记对象（用于前端显示）
        const newDiaryObj = {
          id: response.data.data.article_id,
          title: newDiary.title,
          content: newDiary.content,
          author: {
            username: userInfo.value.username || '当前用户',
            avatar: userInfo.value.avatar || require('@/assets/belog.jpg')
          },
          createTime: Date.now(),
          images: imageUrl ? [imageUrl] : [],
          video: videoUrl,
          commentsCount: 0,
          likes: 0,
          favorites: 0,
          views: 1,
          heat: 10, // 初始热度
          isLiked: false,
          isFavorited: false,
          userRating: 0,
          avgRating: 0,
          location: locationOptions.value.find(item => item.value === newDiary.location)?.label || '',
          tags: newDiary.tags,
          privacy: newDiary.privacy
        }

        // 添加到列表开头
        diaryList.value.unshift(newDiaryObj)

        // 关闭对话框
        diaryDialogVisible.value = false
        // 重置表单
        resetForm()

        // 显示成功消息
        ElMessage.success('游记发布成功')

        // 切换到日记标签页
        activeTab.value = 'diaries'
      } else {
        ElMessage.error(response.data.message || '游记发布失败')
      }
    } catch (error) {
      console.error('游记发布失败:', error)

      // 详细记录错误信息
      if (error.response) {
        // 服务器返回了错误状态码
        console.error('错误响应数据:', error.response.data)
        console.error('错误状态码:', error.response.status)
        console.error('错误响应头:', error.response.headers)

        // 显示服务器返回的错误信息
        const errorMsg = error.response.data?.message || error.response.data?.error || '服务器错误'
        ElMessage.error(`游记发布失败: ${errorMsg} (状态码: ${error.response.status})`)
      } else if (error.request) {
        // 请求已发送但没有收到响应
        console.error('请求未收到响应:', error.request)
        ElMessage.error('游记发布失败: 服务器无响应')
      } else {
        // 请求设置时出现错误
        console.error('请求错误:', error.message)
        ElMessage.error(`游记发布失败: ${error.message}`)
      }
    } finally {
      // 结束提交状态
      submittingDiary.value = false
    }
  })
}

// 创建地点数据
const createLocation = async (name) => {
  try {
    console.log(`尝试创建地点: ${name}`)
    const response = await axios.post('http://localhost:5000/api/locations', {
      name: name,
      description: `${name}的描述`,
      latitude: 0, // 默认值
      longitude: 0 // 默认值
    })

    console.log('创建地点响应:', response.data)

    if (response.data.code === 0 && response.data.data && response.data.data.location_id) {
      console.log(`成功创建地点: ${name}, ID: ${response.data.data.location_id}`)
      return {
        value: response.data.data.location_id,
        label: name
      }
    } else {
      console.error('创建地点失败:', response.data)
      return null
    }
  } catch (error) {
    console.error(`创建地点 ${name} 失败:`, error)
    return null
  }
}

// 确保数据库中有地点数据
const ensureLocationsExist = async () => {
  try {
    console.log('确保数据库中有地点数据')

    // 先获取现有地点
    const response = await axios.get('http://localhost:5000/api/locations')
    console.log('现有地点列表:', response.data)
    console.log(response.data)
    // 如果没有地点数据，创建默认地点
    if (response.data.code === 0 && (!response.data.data || response.data.data.length === 0)) {
      console.log('数据库中没有地点数据，创建默认地点')

      const defaultLocations = ['北京', '上海', '广州', '深圳', '杭州']
      const createdLocations = []

      // 逐个创建地点
      for (const name of defaultLocations) {
        const location = await createLocation(name)
        if (location) {
          createdLocations.push(location)
        }
      }

      console.log('创建的默认地点:', createdLocations)

      // 更新地点选项
      if (createdLocations.length > 0) {
        locationOptions.value = createdLocations
        return true
      }
    }

    return false
  } catch (error) {
    console.error('确保地点数据存在时出错:', error)
    return false
  }
}

// 获取地点列表
const fetchLocations = async () => {
  try {
    console.log('开始获取地点列表')
    const response = await axios.get('http://localhost:5000/api/locations')
    console.log('地点列表响应:', response.data)

    if (response.data.code === 0 && response.data.data && response.data.data.length > 0) {
      // 确保每个地点都有正确的location_id
      locationOptions.value = response.data.data.map(location => ({
        value: location.location_id,
        label: location.name
      }))
    }
  } catch (error) {
    console.error('获取地点列表失败:', error)
  }
}


const handleAvatarClick = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.style.display = 'none'

  // 一次性处理函数
  const handleFileSelect = (event) => {
    // 确保有文件被选择
    if (event.target.files && event.target.files.length > 0) {
      handleAvatarChange(event)
    }
    // 清理
    document.body.removeChild(input)
    input.removeEventListener('change', handleFileSelect)
  }

  // 添加事件监听
  input.addEventListener('change', handleFileSelect)

  // 添加到DOM并触发点击
  document.body.appendChild(input)
  input.click()
}

const handleAvatarChange = async (event) => {
  if (!event.target?.files?.length) {
    console.log("没有选择文件")
    return
  }
  console.log("开始处理头像更新")
  const file = event.target.files[0]
  if (!file) {
      console.log("No file selected.");
      return;
  }
  if (!userInfo.value.id) {
      ElMessage.error('无法获取用户ID，请重新登录');
      console.error('User ID is missing in userInfo');
      return;
  }
  console.log("Preparing FormData for avatar update:");
  console.log("User ID:", userInfo.value.id);
  console.log("File Name:", file.name);
  console.log("File Size:", file.size);
  console.log("File Type:", file.type);

  try {
    console.log("准备FormData")
    const formData = new FormData();
    formData.append('user_id', userInfo.value.id);
    formData.append('avatar', file);

    console.log("FormData entries:");
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}:`, value);
    }

    console.log("Calling updateAvatar API...");
    const response = await updateAvatar(formData);
    console.log("updateAvatar API response:", response);

    // 根据实际响应结构更新头像
    if (response && response.code === 0 && response.data && response.data.avatar_url) {
      const newAvatarPath = response.data.avatar_url; // 获取新的相对路径
      userInfo.value.avatar = newAvatarPath; // 更新 userInfo

      // 1. 更新 Vuex store (优先使用)
      if (store.getters.isAuthenticated) {
        const user = { ...store.getters.currentUser, avatar: newAvatarPath };
        store.commit('SET_USER', user);
        console.log('已更新Vuex store中的头像');
      }

      // 2. 更新 localStorage (兼容旧版本)
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
      if (currentUser.id === userInfo.value.id || currentUser.user_id === userInfo.value.id) {
          currentUser.avatar = newAvatarPath;
          localStorage.setItem('currentUser', JSON.stringify(currentUser));
          console.log('已更新localStorage中的头像');
      }

      // 3. 触发全局事件，通知其他组件更新头像
      window.dispatchEvent(new CustomEvent('user-avatar-updated', {
        detail: { avatar: newAvatarPath }
      }));
      console.log('已触发user-avatar-updated事件');

      ElMessage.success('头像更新成功');
    } else {
      console.error('头像更新失败，响应:', response);
      ElMessage.error(response?.message || '头像更新失败：服务器响应异常');
    }

  } catch (error) {
    console.log("头像更新失败 - Error caught:", error);
    if (error.response) {
        console.error("Error Response Data:", error.response.data);
        console.error("Error Response Status:", error.response.status);
        console.error("Error Response Headers:", error.response.headers);
        const backendMessage = error.response.data?.message || error.response.data?.error || '服务器响应错误';
        ElMessage.error(`头像更新失败: ${backendMessage} (状态码: ${error.response.status})`);
    } else if (error.request) {
        console.error("Error Request:", error.request);
        ElMessage.error('头像更新失败：服务器无响应');
    } else {
        console.error('Error Message:', error.message);
        ElMessage.error(error.message || '头像更新失败：请求设置错误');
    }
  }
}

const handleUsernameClick = async () => {
  try {
    const { value: formData } = await ElMessageBox.prompt('请输入新的用户名和密码', '更新用户名', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPattern: /\S+/,
      inputErrorMessage: '用户名不能为空',
      inputType: 'text',
      showClose: false,
      inputPlaceholder: '新用户名',
    })

    if (formData) {
      const { value: password } = await ElMessageBox.prompt('请输入密码确认', '密码验证', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputType: 'password',
        showClose: false,
      })

      const response = await updateUsername({
        user_id: userInfo.value.id,
        new_username: formData,
        password: password
      })

      if (response.code === 0) {
        userInfo.value.username = formData
        ElMessage.success('用户名更新成功')
      }
    }
  } catch (error) {
    // if (error !== 'cancel') {
    //   ElMessage.error('用户名更新失败')
    // }
  }
}

const handleEmailClick = async () => {
  try {
    const { value: formData } = await ElMessageBox.prompt('请输入新的邮箱地址', '更新邮箱', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      inputErrorMessage: '请输入有效的邮箱地址',
      inputType: 'email',
      showClose: false,
      inputPlaceholder: '新邮箱地址',
    })

    if (formData) {
      const { value: password } = await ElMessageBox.prompt('请输入密码确认', '密码验证', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputType: 'password',
        showClose: false,
      })

      const response = await updateEmail({
        user_id: userInfo.value.id,
        new_email: formData,
        password: password
      })

      if (response.code === 0) {
        userInfo.value.email = formData
        ElMessage.success('邮箱更新成功')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('邮箱更新失败')
    }
  }
}


const handleLogout = async () => {
  try {
    // 调用API登出
    await logout()

    // 调用store的logout action清除所有状态
    store.dispatch('logout')

    ElMessage.success('已退出登录')
    router.push('/')
  } catch (error) {
    console.error('登出失败:', error)
    // 即使API调用失败，也要清除本地状态
    store.dispatch('logout')
    ElMessage.error('登出失败')
    router.push('/')
  }
}
</script>

<style scoped>
.shoucang{
  background-color:transparent;
  border-color:transparent;
  color: #000;
}


/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.2;
}

.memory-corner {
  position: fixed;
  right: 30px;
  bottom: 90px;
  padding: 12px 18px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #606266;
  font-style: italic;
  transform: rotate(-5deg);
  transition: all 0.3s ease;
  z-index: 100;
  cursor: pointer;
}

.memory-corner:hover {
  transform: rotate(0deg) scale(1.05);
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}
/* 移除重复的avatar-wrapper定义 */
:deep(.el-card) {
  border-radius: 0 !important;
  border: none !important;
}

:deep(.el-card__body) {
  padding: 20px !important;
}
.page-container {
  position: fixed;
  top: 60px; /* 导航栏高度 */
  left: 0;
  right: 0;
  bottom: 60px; /* 脚栏高度 */
  width: 100%;
  overflow: hidden; /* 禁止整体滚动 */
  padding: 0; /* 移除内边距 */
}

/* 左右布局容器 */
.user-layout {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}


/* 右侧内容区域 */
.diary-container {
  flex: 1; /* 占据剩余空间 */
  min-width: 0; /* 防止溢出 */
  margin: 0;
  padding: 0; /* 移除内边距 */
}
.user-tabs {
  margin-top: 30px;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  padding: 0 5px;
  transition: all 0.3s ease;
}

.tab-label .el-icon {
  font-size: 20px;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 25px;
  height: 56px;
  line-height: 56px;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item:hover) {
  color: #409EFF;
}

:deep(.el-tabs__item.is-active) {
  font-weight: bold;
  color: #409EFF;
}

:deep(.el-tabs__item.is-active .tab-label .el-icon) {
  transform: scale(1.2);
  color: #409EFF;
}

:deep(.el-tabs__active-bar) {
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 3px;
}

.favorite-card, .comment-card, .like-card {
  margin-bottom: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
  background-color: white;
}

.favorite-card:hover, .comment-card:hover, .like-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.favorite-header, .comment-header, .like-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.favorite-title, .like-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.favorite-info {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.favorite-author, .favorite-date, .like-date {
  font-size: 12px;
  color: #909399;
}

.favorite-location {
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.favorite-content, .comment-content, .like-content {
  margin-bottom: 15px;
}

.favorite-excerpt, .like-excerpt {
  color: #606266;
  line-height: 1.6;
}

.favorite-footer, .comment-footer, .like-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-timeline-item__node) {
  background-color: #409EFF;
}

:deep(.el-timeline-item__tail) {
  border-left-color: #e4e7ed;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 20px;
}

/* 日记相关样式 */
/* 移除了.diary-actions样式，避免在其他标签页中显示 */



.diary-uploader {
  width: 100%;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 4px;
}

.delete-icon {
  color: #fff;
  cursor: pointer;
  font-size: 16px;
}

.delete-icon:hover {
  color: #f56c6c;
}

.upload-tip {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 视频上传相关样式 */
.video-upload-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.video-upload-button {
  width: 100%;
  height: 120px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: border-color 0.3s;
}

.video-upload-button:hover {
  border-color: #409EFF;
}

.video-upload-button .el-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.video-upload-button span {
  color: #8c939d;
}

.video-preview {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.preview-video {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  background-color: #000;
}

.video-actions {
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  background-color: #f5f7fa;
}

/* 加载状态容器 */
.loading-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 日记视频样式 */
.diary-video {
  width: 100%;
  max-height: 300px;
  border-radius: 4px;
  margin-top: 10px;
  background-color: #000;
}

/* 视频错误提示 */
.video-error {
  padding: 10px;
  color: #f56c6c;
  text-align: center;
  background-color: #fef0f0;
  border-radius: 4px;
  margin-top: 5px;
}

/* 调试按钮样式 */
.debug-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}
.diary-container {
  position: absolute !important;
  top: 0 !important;
  left: 280px !important; /* 匹配左侧卡片宽度 */
  right: 0 !important;
  bottom: 0 !important;
  overflow-y: auto !important; /* 允许垂直滚动 */
  padding: 0 30px !important; /* 增加内边距 */
  background-color: #f9fafc !important; /* 更柔和的背景色 */
  transition: all 0.3s ease;
}
.diary-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,1) 100%);
}
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: blur(8px);
  z-index: 0;
}
.content-card {
  position: relative !important;
  z-index: 1 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  width: 100% !important;
  max-width: 1200px !important; /* 限制最大宽度 */
  margin: 0 auto !important; /* 居中 */
  padding: 20px 0 !important; /* 只添加上下内边距 */
  height: 100% !important;
  overflow: visible !important;
}
.content-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1); /* 可调节透明度 */
  z-index: -1; /* 在内容文字下方 */
}
.custom-tab-label {
  font-size: 30px; /* 调整为更合理的尺寸 */
  font-weight: bold;
  color: #000;
}
.user-background-area {
  position: relative;
  overflow: hidden;
  z-index: 0; /* 创建层叠上下文 */
}

.user-background-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; /* ✅ 让它和容器一样高，避免高度问题 */
  background-size: cover;
  background-position: top center;
  filter: blur(10px); /* ✅ 虚化 */
  z-index: -1; /* ✅ 放到内容后面 */
  pointer-events: none; /* ✅ 避免遮挡交互 */
}
.avatar-wrapper {
  position: relative !important;
  display: inline-block !important;
  margin: 0 0 10px 0 !important; /* 强制上下间距，左对齐 */
  animation: avatarMoveUp 1s ease-out forwards !important; /* 添加上移动画 */
}

@keyframes avatarMoveUp {
  0% {
    transform: translateY(50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.avatar-uploader .avatar {
  width: 80px;
  height: 80px;
  display: block;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 10px;
}
.avatar-uploader-icon {
  font-size: 32px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
}

.user-basic h2 {
  margin: 0;
  font-size: 24px;
}

.user-basic p {
  margin-top: 8px; /* 增加一些顶部间距 */
  font-size: 14px;
  color: black;
}

.user-basic .email-placeholder {
  margin-top: 12px;
  height: 20px; /* 占位高度 */
  color: black; /* 不显示文字 */
}
.user-center {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

.user-card {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  width: 280px !important; /* 增加宽度 */
  background-color: #f8f9fa;
  color: #000 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-y: hidden !important; /* 禁止滚动 */
  border-radius: 0 !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s ease;
}

.user-info-centered {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important; /* 强制左对齐 */
  justify-content: space-between !important; /* 保持两端对齐，使按钮靠下 */
  padding: 30px 15px !important;
  text-align: left !important; /* 强制文本左对齐 */
  height: 100% !important;
  min-height: 400px !important; /* 确保有足够的高度 */
  box-sizing: border-box !important; /* 确保内边距不会增加元素总高度 */
}

.user-profile {
  display: flex !important;
  flex-direction: column !important; /* 保持垂直排列 */
  align-items: flex-start !important; /* 强制左对齐 */
  gap: 20px !important;
  width: 100% !important;
  margin-bottom: 0 !important; /* 移除底部间距 */
}

.user-basic {
  text-align: left !important; /* 强制左对齐 */
  animation: fadeIn 1.2s ease-out forwards !important; /* 稍微延迟一点，让头像先动画 */
  width: 100% !important;
}

.user-basic h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.user-basic p {
  margin: 8px 0 0;
  font-size: 16px;
  color: #666;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.user-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
}

.user-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
}

.user-info-text h2 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-info-text p {
  margin: 8px 0 15px;
  font-size: 16px;
  color: #606266;
  line-height: 1.5;
}
.user-header h2 {
  margin: 10px 0 5px;
  font-size: 24px;
}
.user-header p {
  color: #666;
  margin: 0;
}
.user-actions {
  display: flex !important;
  flex-direction: row !important; /* 保持水平排列 */
  justify-content: flex-start !important; /* 强制左对齐 */
  gap: 20px !important; /* 按钮之间的间距 */
  width: 100% !important; /* 占满容器宽度 */
  margin: 20px 0 0 0 !important; /* 增加顶部间距 */
  animation: fadeInUp 1.5s ease-out forwards !important; /* 按钮最后出现 */
}

@keyframes fadeInUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 25px;
  padding-top: 25px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 0 0 12px 12px;
}

.stat-item {
  text-align: center;
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-item h3 {
  font-size: 28px;
  margin: 0;
  color: #409EFF;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stat-item p {
  margin: 8px 0 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}
.user-content {
  min-height: 300px;
  background-image: url('@/assets/usercenterdiary.png');
  background-color: #ffffff !important;
}
.user-card,
.user-content {
  box-shadow: none !important;
  border: none !important;
}
.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}.xhs-button {
  border-radius: 20px;
  font-size: 16px;
  color:#000;
  padding: 10px 20px;
  font-weight: 40s0;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  margin-top: -10px;
}



.xhs-button.primary {
  background-color: rgba(64, 158, 255, 0.2);
  border: 1px solid #409EFF;
  font-size: 15px;
  margin-top: -50px;
}
.xhs-button.danger {
  background-color: rgba(255, 255, 255, 0.3);
  color: #409EFF;
  border: 1px solid #409EFF;
  font-size: 15px;
}
.xhs-button:hover {
  opacity: 0.85;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



.preview-files {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
  align-items: center; /* 垂直居中对齐预览项和添加按钮 */
}

.preview-item {
  position: relative;
}

.preview-media {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #eee;
}

.dialog_up-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
.diary-list {
  margin-bottom: 2rem;
}

.diary-card {
  margin-bottom: 1.5rem;
  background-color: transparent !important; /* 背景透明 */
  box-shadow: none; /* 可选：去掉阴影 */
  border: none; /* 可选：去掉边框 */
}


.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.diary-content {
  margin-bottom: 1.5rem;
}

.diary-title {
  font-size: 1.8rem;
  margin: 0 0 0.5rem;
  color: #2c3e50;
}

.diary-excerpt {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
}

.diary-media {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 10px;
  margin: 1rem 0;
}

.diary-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
}

.diary-footer {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.diary-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: #666;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 对齐“详情”按钮到 Message 图标的位置 */
.detail {
  margin-left: 0; /* 默认左对齐 */
}


.empty-state {
  text-align: center;
  padding: 4rem 0;
}

/* 对话框样式 */
.preview-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
.detail{
  background-color:transparent;
  border-color:transparent;
  color: #666;
  margin-left: -18px;
  font-size: 15px;
}

.video-upload-container {
  width: 100%;
  margin-bottom: 10px;
}

.video-upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
  background-color: #fafafa;
  flex-direction: column;
  gap: 8px;
}

.video-upload-button:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.video-preview {
  width: 100%;
  margin-top: 10px;
}

.preview-video {
  width: 100%;
  max-height: 300px;
  border-radius: 4px;
  object-fit: contain;
  background-color: #000;
}

.video-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

/* 自定义图片上传样式 */
.custom-upload-container {
  width: 100%;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 4px;
}

.delete-icon {
  color: #fff;
  cursor: pointer;
}

/* 游记卡片样式 - 新版 */
.diary-card {
  margin-bottom: 1.5rem;
  background-color: #fff !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  overflow: hidden;
  padding-bottom: 0 !important;
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 12px 16px 0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: 13px;
  margin: 0;
  font-weight: 500;
  color: #333;
}

.post-date {
  font-size: 12px;
  color: #999;
  display: block;
  margin-top: 2px;
}

.diary-content {
  padding: 0 16px;
  margin-bottom: 1rem;
}

.diary-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px;
  color: #333;
  line-height: 1.4;
}

/* 标签样式 */
.diary-location {
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.diary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0 12px;
}

.diary-tag {
  font-size: 12px !important;
  padding: 0 8px !important;
  height: 22px !important;
  line-height: 20px !important;
  border-radius: 2px !important;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border: none !important;
}

.diary-excerpt {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 评分样式已移除 */

.diary-media {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin: 12px 0;
  overflow: hidden;
}

.diary-image {
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: 2px;
}

.more-images {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140px;
  border-radius: 2px;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

.video-container {
  grid-template-columns: 1fr;
}

.diary-video {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 2px;
}

.diary-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
  background-color: #fafafa;
}

.diary-stats {
  display: flex;
  align-items: center;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 14px;
}

.up-icon {
  color: #999;
}

.rating-stars {
  display: flex;
  align-items: center;
  gap: 10px;
}

.read-more-btn {
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 4px;
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

/* 标签选择下拉框样式 */
.tag-select-dropdown {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.tag-select-dropdown .el-select-dropdown__item {
  height: auto !important;
  padding: 8px 20px !important;
  line-height: 1.5 !important;
  white-space: normal !important;
}

/* 修复标签显示样式 */
.el-select__tags-text {
  display: inline-block !important;
  max-width: 100% !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  color: #606266 !important; /* 确保文字颜色可见 */
}

.el-select .el-tag {
  margin: 2px 4px 2px 0 !important;
  max-width: 100% !important;
  background-color: #f0f2f5 !important; /* 浅灰色背景 */
  color: #606266 !important; /* 深灰色文字 */
  border: 1px solid #e4e7ed !important; /* 浅灰色边框 */
  display: inline-flex !important;
  align-items: center !important;
}

/* 确保标签文字可见 */
.el-tag__content {
  color: #606266 !important;
  display: inline-block !important;
  max-width: 100% !important;
}

/* 强制覆盖 Element Plus 的标签样式 */
.el-select__tags .el-tag {
  background-color: #f0f2f5 !important;
  color: #606266 !important;
  border-color: #e4e7ed !important;
  margin: 2px 4px 2px 0 !important;
  height: 24px !important;
  line-height: 22px !important;
  padding: 0 8px !important;
}

.el-select__tags .el-tag .el-tag__close {
  color: #909399 !important;
  right: -2px !important;
}

.el-select__tags .el-tag span {
  color: #606266 !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
  display: inline !important;
  max-width: none !important;
}

/* 修复标签选择器内部样式 */
.el-select .el-select__wrapper {
  padding: 0 4px !important;
}

.el-select .el-select__tags-text {
  display: inline !important;
  color: #606266 !important;
}

/* 确保标签在选择框内部可见 */
.el-select-dropdown__item.selected {
  color: #409EFF !important;
  font-weight: bold !important;
}

.el-select .el-select__tags {
  padding: 2px !important;
  flex-wrap: wrap !important;
}

/* 修复标签关闭按钮 */
.el-tag__close {
  color: #909399 !important;
  background-color: transparent !important;
}

/* 已选标签样式 */
.selected-tags {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.selected-tags span {
  font-size: 14px;
  color: #606266;
}

.selected-tags .el-tag {
  margin: 2px;
  background-color: #f0f2f5 !important;
  color: #606266 !important;
  border: 1px solid #e4e7ed !important;
}

/* 地点建议样式 */
.location-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.location-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.location-type {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 12px;
}

/* 日记卡片样式 - 与DiaryCard组件保持一致 */
.diary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 10px 0;
}

.diary-tag {
  margin-right: 5px;
}

.location-tag {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.location-tag .el-icon {
  margin-right: 3px;
}

.diary-stats {
  margin-top: 15px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 14px;
}

.stat-count {
  font-weight: 500;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.heat-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.heat-progress {
  width: 60px;
}

.rating-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.read-more-btn {
  font-size: 12px;
  padding: 5px 15px;
}

.up-icon {
  color: #ff6b6b;
}

/* 视频错误消息样式 */
.video-error-message {
  padding: 10px;
  color: #f56c6c;
  text-align: center;
  background-color: #fef0f0;
  border-radius: 4px;
  font-size: 12px;
}

/* 日记媒体样式 - 与DiaryCard组件保持一致 */
.diary-media {
  margin: 15px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.diary-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.diary-image:hover {
  transform: scale(1.05);
}

.more-images {
  width: 120px;
  height: 120px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
}

.video-container {
  width: 100%;
  max-width: 400px;
}

.diary-video {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
  background-color: #000;
}

/* 日记内容样式 */
.diary-excerpt {
  color: #666;
  line-height: 1.6;
  margin: 10px 0;
  font-size: 14px;
}

</style>