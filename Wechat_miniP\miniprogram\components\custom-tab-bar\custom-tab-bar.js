Component({
  data: {
    selected: 0,
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        icon: "home"
      },
      {
        pagePath: "/pages/place-search/place-search",
        text: "景点",
        icon: "place"
      },
      {
        pagePath: "/pages/route-plan/route-plan",
        text: "路线",
        icon: "map"
      },
      {
        pagePath: "/pages/diary/diary",
        text: "日记",
        icon: "book"
      },
      {
        pagePath: "/pages/user-center/user-center",
        text: "我的",
        icon: "person"
      }
    ]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({
        url
      });
      this.setData({
        selected: data.index
      });
    }
  }
})
